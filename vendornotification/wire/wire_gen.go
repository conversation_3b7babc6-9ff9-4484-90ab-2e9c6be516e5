// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/kinesis"
	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/aws/v2/wire"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/ratelimit/keygen"
	faas2 "github.com/epifi/be-common/pkg/epifitemporal/faas"
	namespace2 "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/faas"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/be-common/pkg/ratelimiter/store"
	"github.com/epifi/be-common/pkg/syncwrapper/response"
	genconf2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/collection"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/cx/call_ivr"
	"github.com/epifi/gamma/api/cx/call_routing"
	"github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	"github.com/epifi/gamma/api/cx/chat/bot/workflow"
	"github.com/epifi/gamma/api/cx/dispute"
	federal10 "github.com/epifi/gamma/api/cx/federal"
	"github.com/epifi/gamma/api/cx/sprinklr"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/firefly/v2"
	"github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/user_declaration"
	catalog2 "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/usstocks/catalog"
	aa2 "github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/api/vendorgateway/sms"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/featurestore"
	fennel2 "github.com/epifi/gamma/featurestore/fennel"
	scienaptic2 "github.com/epifi/gamma/featurestore/scienaptic"
	helper2 "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	txnaggregates2 "github.com/epifi/gamma/pkg/dmf/txnaggregates"
	"github.com/epifi/gamma/quest/sdk/init"
	"github.com/epifi/gamma/vendornotification/aa"
	"github.com/epifi/gamma/vendornotification/aa/ignosis"
	"github.com/epifi/gamma/vendornotification/aml/tss"
	auth2 "github.com/epifi/gamma/vendornotification/auth"
	"github.com/epifi/gamma/vendornotification/card/shipway"
	"github.com/epifi/gamma/vendornotification/comms/unsubscribe"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/creditcard/m2p"
	"github.com/epifi/gamma/vendornotification/creditcard/paisabazaar"
	"github.com/epifi/gamma/vendornotification/creditcard/saven"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/auth/senseforth"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/collectors"
	freshchat2 "github.com/epifi/gamma/vendornotification/cx/chatbot/freshchat"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	senseforth2 "github.com/epifi/gamma/vendornotification/cx/chatbot/livechatfallback/senseforth"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/nugget"
	senseforth3 "github.com/epifi/gamma/vendornotification/cx/chatbot/workflow/senseforth"
	federal11 "github.com/epifi/gamma/vendornotification/cx/federal"
	"github.com/epifi/gamma/vendornotification/cx/freshchat"
	"github.com/epifi/gamma/vendornotification/cx/ozonetel"
	sprinklr2 "github.com/epifi/gamma/vendornotification/cx/sprinklr"
	"github.com/epifi/gamma/vendornotification/email"
	karza3 "github.com/epifi/gamma/vendornotification/epan/karza"
	"github.com/epifi/gamma/vendornotification/fi_coins_accounting"
	"github.com/epifi/gamma/vendornotification/healthinsurance/riskcovry"
	ratelimiter2 "github.com/epifi/gamma/vendornotification/interceptor/ratelimiter"
	"github.com/epifi/gamma/vendornotification/interceptor/ratelimiter/namespace"
	"github.com/epifi/gamma/vendornotification/investment/smallcase"
	axis3 "github.com/epifi/gamma/vendornotification/kyc/axis"
	"github.com/epifi/gamma/vendornotification/kyc/idfc"
	"github.com/epifi/gamma/vendornotification/lending/bre/inhouse"
	"github.com/epifi/gamma/vendornotification/lending/credgenics"
	"github.com/epifi/gamma/vendornotification/lending/loans/abfl"
	federal8 "github.com/epifi/gamma/vendornotification/lending/loans/federal"
	"github.com/epifi/gamma/vendornotification/lending/loans/fiftyfin"
	idfc2 "github.com/epifi/gamma/vendornotification/lending/loans/idfc"
	"github.com/epifi/gamma/vendornotification/lending/loans/moneyview"
	"github.com/epifi/gamma/vendornotification/lending/setu"
	"github.com/epifi/gamma/vendornotification/liveness/karza"
	"github.com/epifi/gamma/vendornotification/notifications/moengage"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/factory"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/insights/spends"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/stocks"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/area"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase/loans"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase/loans/dropoff"
	"github.com/epifi/gamma/vendornotification/offers/externalredemptions"
	axis2 "github.com/epifi/gamma/vendornotification/openbanking/accounts/axis"
	federal6 "github.com/epifi/gamma/vendornotification/openbanking/accounts/federal"
	federal4 "github.com/epifi/gamma/vendornotification/openbanking/auth/federal"
	federal2 "github.com/epifi/gamma/vendornotification/openbanking/card/federal"
	federal3 "github.com/epifi/gamma/vendornotification/openbanking/deposit/federal"
	dispute2 "github.com/epifi/gamma/vendornotification/openbanking/dispute"
	federal5 "github.com/epifi/gamma/vendornotification/openbanking/kyctypechange/federal"
	"github.com/epifi/gamma/vendornotification/openbanking/payment/axis"
	"github.com/epifi/gamma/vendornotification/openbanking/payment/federal"
	federal9 "github.com/epifi/gamma/vendornotification/openbanking/recurringpayment/federal"
	federal7 "github.com/epifi/gamma/vendornotification/openbanking/shipping_preference/federal"
	"github.com/epifi/gamma/vendornotification/paymentgateway/razorpay"
	"github.com/epifi/gamma/vendornotification/pushnotification"
	gupshup2 "github.com/epifi/gamma/vendornotification/rcs/gupshup"
	"github.com/epifi/gamma/vendornotification/reward"
	"github.com/epifi/gamma/vendornotification/sms/acl"
	"github.com/epifi/gamma/vendornotification/sms/airtel"
	"github.com/epifi/gamma/vendornotification/sms/kaleyra"
	"github.com/epifi/gamma/vendornotification/sms/netcore"
	types2 "github.com/epifi/gamma/vendornotification/types"
	"github.com/epifi/gamma/vendornotification/videocall/videosdk"
	karza2 "github.com/epifi/gamma/vendornotification/vkyc/karza"
	acl2 "github.com/epifi/gamma/vendornotification/whatsapp/acl"
	airtel2 "github.com/epifi/gamma/vendornotification/whatsapp/airtel"
	"github.com/epifi/gamma/vendornotification/whatsapp/gupshup"
	"github.com/orcaman/concurrent-map"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func InitializeFederalPayService(ctx context.Context, conf *config.Config, awsConf aws.Config, updateTransactionEventsPublisher federal.UpdateTransactionEventsPublisher, inboundTxnPublisher federal.InboundTxnPublisher, upiReqAuthEventPublisher federal.UPIReqAuthEventPublisher, upiRespPayEventPublisher federal.UPIRespPayEventPublisher, upiRespMandateEventPublisher federal.UPIRespMandateEventPublisher, upiReqTxnConfirmationPublisher federal.UPIReqTxnConfirmationEventPublisher, upiReqValAddressPublisher federal.UPIReqValAddressEventPublisher, upiListPspKeysPublisher federal.UPIListPspKeysEventPublisher, orderClient order.OrderServiceClient, upiReqAuthMandateEventPublisher federal.UPIReqAuthMandateEventPublisher, upiReqMandateConfirmationEventPublisher federal.UPIReqMandateConfirmationEventPublisher, upiReqTxnConfirmationComplaintPublisher federal.UPIReqTxnConfirmationComplaintEventPublisher, upiReqAuthValCustEventPublisher federal.UPIReqAuthValCustEventPublisher, inboundUpiTxnPublisher federal.InboundUpiTxnPublisher, inboundLoanTxnPublisher federal.InboundLoanTxnPublisher, upiRespComplaintEventPublisher federal.UPIRespComplaintEventPublisher, upiReqMapperConfirmationEventPublisher federal.UPIReqMapperConfirmationEventPublisher, dynamicConf *genconf.Config) (*federal.Service, error) {
	concurrentMap := syncRespCallbackHandlerChannelsProvider()
	syncRespHandler := syncRespHandlerProvider(conf)
	handler, err := response.NewHandler(concurrentMap, syncRespHandler)
	if err != nil {
		return nil, err
	}
	upiListVaePublisher := upiListVaePublisherProvider(ctx, conf, awsConf)
	service := federal.NewService(updateTransactionEventsPublisher, inboundTxnPublisher, handler, upiReqAuthEventPublisher, upiRespPayEventPublisher, upiRespMandateEventPublisher, upiReqTxnConfirmationPublisher, upiReqValAddressPublisher, upiListPspKeysPublisher, upiListVaePublisher, orderClient, conf, upiReqAuthMandateEventPublisher, upiReqMandateConfirmationEventPublisher, upiReqTxnConfirmationComplaintPublisher, upiReqAuthValCustEventPublisher, inboundUpiTxnPublisher, inboundLoanTxnPublisher, upiRespComplaintEventPublisher, upiReqMapperConfirmationEventPublisher, dynamicConf)
	return service, nil
}

func InitializeFederalCardService(creationCallbackPublisher federal2.CreateCardCallbackPublisher, dispatchPhysicalCardCallbackPublisher federal2.DispatchPhysicalCardCallbackPublisher, cardSwitchFinancialNotificationPublisher federal2.CardSwitchFinancialNotificationPublisher, cardSwitchNonFinancialNotificationPublisher federal2.CardSwitchNonFinancialNotificationPublisher) *federal2.Service {
	service := federal2.NewService(creationCallbackPublisher, dispatchPhysicalCardCallbackPublisher, cardSwitchFinancialNotificationPublisher, cardSwitchNonFinancialNotificationPublisher)
	return service
}

func InitialiseShipwayCardService(cardTrackingCallbackPublisher shipway.CardTrackingCallbackPublisher) *shipway.Service {
	service := shipway.NewService(cardTrackingCallbackPublisher)
	return service
}

func InitializeKarzaLivenessService() *karza.Service {
	service := karza.NewService()
	return service
}

func InitializeVideoSdkService() *videosdk.Service {
	service := videosdk.NewService()
	return service
}

func InitializeFederalDepositService(createDepositCallbackPublisher federal3.CreateDepositCallbackPublisher, preCloseDepositCallbackPublisher federal3.PreCloseDepositCallbackPublisher, fdAutoRenewCallbackPublisher federal3.FdAutoRenewCallbackPublisher) *federal3.Service {
	service := federal3.NewService(createDepositCallbackPublisher, preCloseDepositCallbackPublisher, fdAutoRenewCallbackPublisher)
	return service
}

func InitializeFederalDmpService(disputeClient dispute.DisputeClient) *dispute2.Service {
	service := dispute2.NewService(disputeClient)
	return service
}

func InitializeFederalAuthService(deviceReregCallbackPublisher federal4.DeviceReRegCallbackPublisher, deviceRegSMSAckPublisher federal4.DeviceRegSMSAckPublisher, mobileNumberUpdateCallbackPub federal4.FederalMobileNumberUpdatePublisher) *federal4.Service {
	service := federal4.NewService(deviceReregCallbackPublisher, deviceRegSMSAckPublisher, mobileNumberUpdateCallbackPub)
	return service
}

func InitializeSmsAclCallbackService(callbackPublisher acl.AclSmsCallbackPublisher, conf *config.Config) *acl.Service {
	service := acl.NewService(callbackPublisher, conf)
	return service
}

func InitializeWhatsappAclCallbackService(conf *config.Config, whatsappCallbackPub acl2.AclWhatsappCallbackPublisher, whatsappReplyPub acl2.AclWhatsappReplyPublisher) *acl2.Service {
	service := acl2.NewService(conf, whatsappCallbackPub, whatsappReplyPub)
	return service
}

func InitializeKycTypeChangeCallbackService(fedVkycUpdPublisher federal5.FederalVkycUpdatePublisher, fedBankCustKycStateChangePublisherfederalKycTypeChange federal5.FederalBankCustKycStateChangePublisher) *federal5.Service {
	service := federal5.NewService(fedVkycUpdPublisher, fedBankCustKycStateChangePublisherfederalKycTypeChange)
	return service
}

func InitializeAccountsCallbackService(bankCustCallbackPublisher federal6.BankCustCallbackPublisher, accountCreationCallbackPublisher federal6.AccountCreationCallbackPublisher, config2 *genconf.Config, accountStatusCallBackPublisher federal6.AccountStatusCallBackPublisher, publisher federal6.FederalResidentialStatusUpdatePublisher) *federal6.Service {
	service := federal6.NewService(bankCustCallbackPublisher, accountCreationCallbackPublisher, config2, accountStatusCallBackPublisher, publisher)
	return service
}

func InitializeShippingAddressUpdateCallbackService(addressUpdateCallbackPublisher federal7.UpdateShippingAddressCallbackPublisher) *federal7.Service {
	service := federal7.NewService(addressUpdateCallbackPublisher)
	return service
}

func InitializeVKYCKarzaCallbackService(callEventPub karza2.KarzaVkycCallEventPublisher, agentPub karza2.KarzaVkycAgentResponsePublisher, auditorPub karza2.KarzaVkycAuditorResponsePublisher, conf *config.Config) *karza2.Service {
	service := karza2.NewService(callEventPub, agentPub, auditorPub, conf)
	return service
}

func InitializeEmailCallbackService(emailCallbackPub email.EmailCallbackPublisher, conf *config.Config) *email.Service {
	service := email.NewService(emailCallbackPub, conf)
	return service
}

func InitializeAANotificationService(consentPub aa.ConsentCallbackPublisher, fiPub aa.FICallbackPublisher, beCaClient connected_account.ConnectedAccountClient, conf *genconf.Config, vgAaClient aa2.AccountAggregatorClient, alsPub aa.AccountLinkStatusCallbackPublisher) *aa.NotificationService {
	cacheService := connectedaccount.NewCacheService(vgAaClient)
	notificationService := aa.NewNotificationService(consentPub, fiPub, beCaClient, cacheService, conf, vgAaClient, alsPub)
	return notificationService
}

func InitializeOzonetelCallRoutingService(cxCallRoutingClient call_routing.CallRoutingClient, conf *config.Config, ozonetelCallDetails ozonetel.OzonetelCallDetailsPublisher, CallIvrClient call_ivr.IvrClient) *ozonetel.CallRoutingService {
	callRoutingService := ozonetel.NewCallRoutingService(cxCallRoutingClient, conf, ozonetelCallDetails, CallIvrClient)
	return callRoutingService
}

func InitializeSprinklrEventHandlingService(sprinklrEventProcessingService sprinklr.SprinklrClient, conf *config.Config) *sprinklr2.EventsHandlingService {
	eventsHandlingService := sprinklr2.NewEventsHandlingService(sprinklrEventProcessingService, conf)
	return eventsHandlingService
}

func InitializeAxisPaymentCallBackService(conf *config.Config) *axis.Service {
	service := axis.NewService(conf)
	return service
}

func InitializeAxisAccountsCallBackService(conf *config.Config) *axis2.Service {
	service := axis2.NewService(conf)
	return service
}

func InitializeAxisEKycCallBackService(conf *config.Config) *axis3.Service {
	service := axis3.NewService(conf)
	return service
}

func InitializeKaleyraCallbackService(callbackPublisher kaleyra.KaleyraSmsCallbackPublisher, conf *config.Config) *kaleyra.Service {
	service := kaleyra.NewService(callbackPublisher, conf)
	return service
}

func InitializeNetCoreCallbackService(callbackPublisher netcore.NetCoreSmsCallbackPublisher, conf *config.Config) *netcore.Service {
	service := netcore.NewService(callbackPublisher, conf)
	return service
}

func InitializeAirtelCallbackService(conf *config.Config, callbackPublisher airtel.AirtelSmsCallbackPublisher, smsClient sms.SMSClient) *airtel.Service {
	service := airtel.NewService(conf, callbackPublisher, smsClient)
	return service
}

func InitializeAirtelWhatsappCallbackService(conf *config.Config, callbackPublisher airtel2.AirtelWhatsappCallbackPublisher) *airtel2.Service {
	service := airtel2.NewService(conf, callbackPublisher)
	return service
}

func InitializeSenseforthChatBotAuthService(conf *config.Config, authClient auth.AuthClient, rateLimiterRedisStore types.RateLimiterRedisStore) (*senseforth.Service, error) {
	senseforthAuthHelper := helper.NewSenseforthAuthHelper(authClient, conf)
	client := types.RateLimiterRedisStoreRedisClientProvider(rateLimiterRedisStore)
	slidingWindowLogWithRedisImpl := store.NewSlidingWindowLogWithRedis(client)
	rateLimitConfig := rateLimitConfigProvider(conf)
	rateLimiterImpl, err := ratelimiter.NewRateLimiter(slidingWindowLogWithRedisImpl, rateLimitConfig)
	if err != nil {
		return nil, err
	}
	ratelimitHelper := helper.NewRatelimitHelper(rateLimiterImpl, conf)
	service := senseforth.NewService(conf, senseforthAuthHelper, ratelimitHelper)
	return service, nil
}

func InitializeSenseforthLiveChatFallbackService(conf *config.Config, cxLiveChatFallbackPbClient livechatfallback.LiveChatFallbackClient, authClient auth.AuthClient, rateLimiterRedisStore types.RateLimiterRedisStore) (*senseforth2.Service, error) {
	senseforthAuthHelper := helper.NewSenseforthAuthHelper(authClient, conf)
	client := types.RateLimiterRedisStoreRedisClientProvider(rateLimiterRedisStore)
	slidingWindowLogWithRedisImpl := store.NewSlidingWindowLogWithRedis(client)
	rateLimitConfig := rateLimitConfigProvider(conf)
	rateLimiterImpl, err := ratelimiter.NewRateLimiter(slidingWindowLogWithRedisImpl, rateLimitConfig)
	if err != nil {
		return nil, err
	}
	ratelimitHelper := helper.NewRatelimitHelper(rateLimiterImpl, conf)
	service := senseforth2.NewService(conf, cxLiveChatFallbackPbClient, senseforthAuthHelper, ratelimitHelper)
	return service, nil
}

func InitializeFreshchatCallbackService(conf *config.Config, fcCallbackPub freshchat.FreshchatActionCallbackPublisher) *freshchat.Service {
	service := freshchat.NewService(conf, fcCallbackPub)
	return service
}

func InitializeFreshchatAIBotService(conf *genconf.Config, userClient user.UsersClient, irClient issue_reporting.ServiceClient, vendorMapping vendormapping.VendorMappingServiceClient) *freshchat2.Service {
	service := freshchat2.NewService(conf, irClient, userClient, vendorMapping)
	return service
}

func InitializeM2PCreditCardService(ctx context.Context, cardTransactionNotificationPublisher m2p.CCTransactionNotificationPublisher, cardStatementNotificationPublisher m2p.CCStatementNotificationPublisher, cardAcsNotificationPublisher m2p.CCAcsNotificationPublisher, dynamicConf *genconf.Config, awsConf aws.Config, conf *config.Config, switchNotificationsS3Client m2p.CcSwitchNotificationsS3Client, rawSwitchNotificationsS3Client m2p.CcRawSwitchNotificationsS3Client, ccNonFinancialNotificationPublisher m2p.CCNonFinancialNotificationPublisher) (*m2p.Service, error) {
	faaSExecutor, err := FireflyFaasExecutorProvider(ctx, awsConf, conf)
	if err != nil {
		return nil, err
	}
	service := m2p.NewService(cardTransactionNotificationPublisher, cardStatementNotificationPublisher, cardAcsNotificationPublisher, faaSExecutor, dynamicConf, conf, switchNotificationsS3Client, rawSwitchNotificationsS3Client, ccNonFinancialNotificationPublisher)
	return service, nil
}

func InitializePaisabazaarCallBackService(ffClient firefly.FireflyClient) *paisabazaar.Service {
	service := paisabazaar.NewService(ffClient)
	return service
}

func InitializeChatbotWorkflowService(conf *config.Config, authClient auth.AuthClient, cxWorkflowClient workflow.WorkflowClient) *senseforth3.Service {
	senseforthAuthHelper := helper.NewSenseforthAuthHelper(authClient, conf)
	service := senseforth3.NewService(conf, senseforthAuthHelper, cxWorkflowClient)
	return service
}

func InitializeTssWebhookCallBackService(conf *config.Config, tssWebhookCallBackPublisher tss.TssWebhookCallBackPublisher) *tss.Service {
	service := tss.NewService(conf, tssWebhookCallBackPublisher)
	return service
}

func InitializeRiskcovryCallbackService(conf *config.Config, healthInsuranceClient healthinsurance.HealthInsuranceClient, policyIssuanceCallbackPublisher riskcovry.HealthInsurancePolicyIssuanceEventPublisher) *riskcovry.Service {
	service := riskcovry.NewService(conf, healthInsuranceClient, policyIssuanceCallbackPublisher)
	return service
}

func InitializeSmallcaseWebhookService(ctx context.Context, conf *config.Config, awsConf aws.Config) *smallcase.Service {
	extendedPublisher := smallcaseWebhookPublisherProvider(ctx, conf, awsConf)
	service := smallcase.NewService(conf, extendedPublisher)
	return service
}

func InitializeLendingFederalInboundService(conf *config.Config) *federal8.Service {
	service := federal8.NewService(conf)
	return service
}

func InitializeEPANCallbackService(conf *config.Config, callbackPublisher karza3.SignalWorkflowPublisher, panClient pan.PanClient, celestialClient celestial.CelestialClient, s3Client karza3.EpanCallbackS3Client) *karza3.Service {
	service := karza3.NewService(conf, callbackPublisher, panClient, celestialClient, s3Client)
	return service
}

func InitializeMoengageService(conf *config.Config, genConf *genconf.Config, questCacheStorage types.QuestCacheStorage, vendorMapping vendormapping.VendorMappingServiceClient, rewardsServiceClient rewards.RewardsGeneratorClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient, segmentClient segment.SegmentationServiceClient, questManagerCl manager.ManagerClient, eventsBroker events.Broker, client txnaggregates.TxnAggregatesClient, savingsClient savings.SavingsClient, fireflyClient firefly.FireflyClient, connectedAccountsClient connected_account.ConnectedAccountClient, merchantClient merchant.MerchantServiceClient, piClient paymentinstrument.PiClient, categorizerClient categorizer.TxnCategorizerClient, palClient preapprovedloan.PreApprovedLoanClient, balanceClient balance.BalanceClient, ccVgClient creditcard.CreditCardClient, accountingClient accounting.AccountingClient, billingClient billing.BillingClient, depositClient deposit.DepositClient, provisioningClient provisioning.CardProvisioningClient, payClient pay.PayClient, usStocksRedisClient types.USStocksRedisStore, usStocksCatalogManagerClient catalog.CatalogManagerClient, salaryClient salaryprogram.SalaryProgramClient, ticketClient ticket.TicketClient, upiOnboardingClient onboarding.UpiOnboardingClient, accountPiClient account_pi.AccountPIRelationClient, netWorthClient networth.NetWorthClient, catalogManagerClient catalog2.CatalogManagerClient, userDeclarationClient user_declaration.ServiceClient, investAnalyticsClient investment.InvestmentAnalyticsClient, epfClient epf.EpfClient, variableGeneratorClient variables.VariableGeneratorClient) *moengage.Service {
	userRewardAttributesFetcher := userattributesfetcher.NewUserRewardAttributesFetcher(rewardsServiceClient)
	genconfConfig := questSDKClientConfProvider(genConf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventsBroker)
	userQuestAttributesFetcher := userattributesfetcher.NewUserQuestAttributesFetcher(questsdkClient, usersClient)
	actorAccountsImpl := datafetcher.NewActorAccountsImpl(connectedAccountsClient, savingsClient, fireflyClient)
	merchantsImpl := datafetcher.NewMerchantsImpl(merchantClient)
	txnAggregatesImpl := txnaggregates2.NewTxnAggregatesImpl(client, actorAccountsImpl, merchantsImpl, piClient)
	summaryGenerator := spends.NewSummaryGenerator(actorAccountsImpl, txnAggregatesImpl, categorizerClient)
	spendsAttributesFetcher := spends.NewSpendsAttributesFetcher(summaryGenerator)
	loanAttributesFetcher := userattributesfetcher.NewLoanAttributesFetcher(palClient)
	balanceAttributesFetcher := userattributesfetcher.NewBalanceAttributesFetcher(balanceClient, savingsClient)
	commsDataHelper := helper2.NewCommsDataHelper(fireflyClient, ccVgClient, actorClient, usersClient, accountingClient, billingClient, depositClient)
	creditCardAttributesFetcher := userattributesfetcher.NewCreditCardAttributesFetcher(commsDataHelper)
	debitCardAttributesFetcher := userattributesfetcher.NewDebitCardAttributesFetcher(provisioningClient, payClient, usersClient, actorClient)
	userFiStoreAttributesFetcher := userattributesfetcher.NewUserFiStoreAttributesFetcher(vendorMapping, provisioningClient)
	userSalaryAttributesFetcher := userattributesfetcher.NewUserSalaryAttributesFetcher(salaryClient)
	usStocksCacheStorage := usStocksCacheStorageProvider(usStocksRedisClient)
	defaultTime := datetime.NewDefaultTime()
	stocksAttributeProvider := stocks.NewStocksAttributeProvider(usStocksCatalogManagerClient, usStocksCacheStorage, defaultTime)
	stocksAttributeFetcher := stocks.NewStocksAttributeFetcher(stocksAttributeProvider)
	upiAttributesFetcher := userattributesfetcher.NewUpiAttributesFetcher(upiOnboardingClient, accountPiClient, savingsClient)
	portfolioTrackerAttributeFetcher := userattributesfetcher.NewPortfolioTrackerAttributeFetcher(genConf, netWorthClient, variableGeneratorClient, catalogManagerClient)
	weeklyPortfolioTrackerAttributeFetcher := userattributesfetcher.NewWeeklyPortfolioTrackerAttributeFetcher(netWorthClient, variableGeneratorClient)
	factoryFactory := factory.NewFactory(userRewardAttributesFetcher, userQuestAttributesFetcher, spendsAttributesFetcher, loanAttributesFetcher, balanceAttributesFetcher, creditCardAttributesFetcher, debitCardAttributesFetcher, userFiStoreAttributesFetcher, userSalaryAttributesFetcher, stocksAttributeFetcher, upiAttributesFetcher, portfolioTrackerAttributeFetcher, weeklyPortfolioTrackerAttributeFetcher)
	dropOffOutcallUseCaseProcessor := dropoff.NewDropOffOutcallUseCaseProcessor(palClient, ticketClient, usersClient)
	loansUseCaseProcessorFactory := loans.NewLoansUseCaseProcessorFactory(dropOffOutcallUseCaseProcessor)
	loansAreaProcessor := area.NewLoansAreaProcessor(loansUseCaseProcessorFactory)
	areaProcessorFactory := area.NewAreaProcessorFactory(loansAreaProcessor)
	service := moengage.NewService(conf, factoryFactory, vendorMapping, areaProcessorFactory)
	return service
}

func InitializeInhouseBreService(fennelClient fennel.FennelFeatureStoreClient, scienapticClient scienaptic.ScienapticClient, usersClient user.UsersClient, conf *config.Config, creditReportManagerClient creditreportv2.CreditReportManagerClient) *inhouse.Service {
	client := fennel2.NewFennelClient(fennelClient)
	scienapticFeatureStoreClient := scienaptic2.NewScienapticFeatureStoreClient(scienapticClient, usersClient)
	featurestoreFactory := featurestore.NewFactory(client, scienapticFeatureStoreClient)
	service := inhouse.NewService(featurestoreFactory, conf, creditReportManagerClient)
	return service
}

func InitialiseRecurringPaymentFederalService(federalEnachRegistrationAuthorisationCallbackPublisher federal9.EnachRegistrationAuthorisationCallbackPublisher) *federal9.Service {
	service := federal9.NewService(federalEnachRegistrationAuthorisationCallbackPublisher)
	return service
}

func InitializeIdfcKycCallbackService(kycStatusUpdatePublisher idfc.KycStatusUpdatePublisher) *idfc.Service {
	service := idfc.NewService(kycStatusUpdatePublisher)
	return service
}

func InitializeFiftyfinNotificationService(conf *config.Config, celestialClient celestial.CelestialClient) *fiftyfin.Service {
	service := fiftyfin.NewService(conf, celestialClient)
	return service
}

func InitializeFiCoinsAccountingService(genConf *genconf.Config, vendorMappingClient vendormapping.VendorMappingServiceClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, userClient user.UsersClient, fireflyClient firefly.FireflyClient) *fi_coins_accounting.Service {
	service := fi_coins_accounting.NewService(genConf, vendorMappingClient, externalVendorRedemptionClient, userClient, fireflyClient)
	return service
}

func InitializeAbflNotificationService(conf *config.Config, celestialClient celestial.CelestialClient) *abfl.Service {
	service := abfl.NewService(conf, celestialClient)
	return service
}

func InitializeSetuNotificationService(conf *config.Config, celestialClient celestial.CelestialClient) *setu.Service {
	service := setu.NewService(conf, celestialClient)
	return service
}

func InitializeMoneyviewCallbackService(conf *config.Config, celestialClient celestial.CelestialClient) *moneyview.Service {
	service := moneyview.NewService(conf, celestialClient)
	return service
}

func InitializeIdfcCallbackService(conf *config.Config, celestialClient celestial.CelestialClient) *idfc2.Service {
	service := idfc2.NewService(conf, celestialClient)
	return service
}

func InitializeExternalOfferRedemptionsService(genConf *genconf.Config, vendorMappingClient vendormapping.VendorMappingServiceClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, userClient user.UsersClient) *externalredemptions.Service {
	service := externalredemptions.NewService(genConf, vendorMappingClient, externalVendorRedemptionClient, userClient)
	return service
}

func InitializeCommsUnsubscribeService(conf *genconf.Config, vendorMappingClient vendormapping.VendorMappingServiceClient, userClient user.UsersClient) *unsubscribe.Service {
	service := unsubscribe.NewService(conf, userClient, vendorMappingClient)
	return service
}

func InitializeCredgenicsWebhookService(conf *config.Config, awsConf aws.Config, palClient preapprovedloan.PreApprovedLoanClient, collectionClient collection.CollectionClient) *credgenics.Service {
	credgenicsStreamProducers := InitializeCredgenicsCallbackStreamProducers(conf, awsConf)
	emailCallbackStreamProducer := credgenicsStreamProducers.EmailCallbackStreamProducer
	smsCallbackStreamProducer := credgenicsStreamProducers.SmsCallbackStreamProducer
	whatsappCallbackStreamProducer := credgenicsStreamProducers.WhatsappCallbackStreamProducer
	callingCallbackStreamProducer := credgenicsStreamProducers.CallingCallbackStreamProducer
	voiceMessageCallbackStreamProducer := credgenicsStreamProducers.VoiceMessageCallbackStreamProducer
	service := credgenics.NewService(emailCallbackStreamProducer, smsCallbackStreamProducer, whatsappCallbackStreamProducer, callingCallbackStreamProducer, voiceMessageCallbackStreamProducer, conf, palClient, collectionClient)
	return service
}

func InitializeWhatsappGupshupCallbackService(conf *config.Config, whatsappCallbackPub gupshup.GupshupWhatsappCallbackPublisher) *gupshup.Service {
	service := gupshup.NewService(conf, whatsappCallbackPub)
	return service
}

func InitializeRcsGupshupCallbackService(conf *config.Config, rcsCallbackPub gupshup2.GupshupRcsCallbackPublisher) *gupshup2.Service {
	service := gupshup2.NewService(conf, rcsCallbackPub)
	return service
}

func InitializeRazorpayInboundEventService(conf *genconf.Config, pgInboundEventPub razorpay.PgRazorpayInboundEventPublisher) *razorpay.Service {
	service := razorpay.NewService(conf, pgInboundEventPub)
	return service
}

func InitializeIgnosisAaService(conf *genconf.Config, aaAnalysisClient analytics.AnalyticsClient) *ignosis.IgnosisAaService {
	ignosisAaService := ignosis.NewIgnosisAaService(conf, aaAnalysisClient)
	return ignosisAaService
}

func InitializeFederalEventHandlingService(federalEventProcessingService federal10.FederalClient, genConf *genconf.Config, federalEscalationUpdateEventPublisher types2.FederalEscalationUpdateEventPublisher) *federal11.FederalEventsHandlingService {
	federalEventsHandlingService := federal11.NewFederalEventsHandlingService(genConf, federalEscalationUpdateEventPublisher)
	return federalEventsHandlingService
}

func InitializeSavenCallBackService(ffV2Client v2.FireflyV2Client, conf *config.Config, publisher saven.CcOnboardingStateUpdateEventPublisher) *saven.Service {
	service := saven.NewService(ffV2Client, conf, publisher)
	return service
}

func InitializeNuggetService(genConf *genconf.Config, authClient auth.AuthClient, orderClient order.OrderServiceClient, riskProfileClient profile.ProfileClient, vendorMappingClient vendormapping.VendorMappingServiceClient, eventBroker events.Broker) (*nugget.NuggetService, error) {
	freezeBotDataCollector := collectors.NewFreezeDataCollector(riskProfileClient, eventBroker)
	transactionDataCollector := collectors.NewTransactionDataCollector(orderClient)
	dataCollectorFactorySvc := data_collector.NewDataCollectorFactorySvc(freezeBotDataCollector, transactionDataCollector)
	nuggetService := nugget.NewNuggetService(genConf, dataCollectorFactorySvc, authClient, vendorMappingClient, eventBroker)
	return nuggetService, nil
}

func InitializeAuthService(conf *config.Config) *auth2.Service {
	service := auth2.NewService(conf)
	return service
}

func InitializeRewardService(gconf *genconf.Config, conf *config.Config, ffV2Client v2.FireflyV2Client, rewardsGenClient rewards.RewardsGeneratorClient, vendorRewardFulfillmentPublisher reward.VendorRewardFulfillmentPublisher) (*reward.Service, error) {
	service, err := reward.NewRewardService(gconf, conf, vendorRewardFulfillmentPublisher, ffV2Client, rewardsGenClient)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitializeKeyGenerator() keygen.IKeyGenerator {
	clientIdFromReqNamespaceGenerator := namespace.NewClientIdFromReqNamespaceGenerator()
	clientIdFromCtxNamespaceGenerator := namespace.NewClientIdFromCtxNamespaceGenerator()
	iFactory := namespace.NewDefaultFactory(clientIdFromReqNamespaceGenerator, clientIdFromCtxNamespaceGenerator)
	iKeyGenerator := ratelimiter2.NewKeyGenerator(iFactory)
	return iKeyGenerator
}

func InitializePushNotificationService(commsClient comms.CommsClient, fireflyV2Client v2.FireflyV2Client, userClient user.UsersClient, conf *config.Config) (*pushnotification.Service, error) {
	service, err := pushnotification.NewService(commsClient, fireflyV2Client, userClient, conf)
	if err != nil {
		return nil, err
	}
	return service, nil
}

// wire.go:

func syncRespHandlerProvider(conf *config.Config) *response.SyncRespHandler {
	return conf.SyncRespHandler
}

func upiListVaePublisherProvider(ctx context.Context, conf *config.Config, awsConf aws.Config) federal.UPIListVaePublisher {
	return wire.InitializeExtendedPublisher(ctx, awsConf, queue.NewDefaultMessage(), sqs.QueueName(conf.UPIListVaePublisher.GetQueueName()), conf.UPIListVaePublisher.GetBucketName(), sqs.ServiceName(cfg.VENDOR_NOTIFI_SERVICE))
}

func syncRespCallbackHandlerChannelsProvider() cmap.ConcurrentMap {
	return cmap.New()
}

func rateLimitConfigProvider(conf *config.Config) *cfg.RateLimitConfig {
	return conf.RateLimitConfig
}

func FireflyFaasExecutorProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) (faas.FaaSExecutor, error) {
	return faas2.NewFaaSExecutor(ctx, sqs.InitSQSClient(awsConf), namespace2.Firefly, conf.ProcrastinatorWorkflowPublisher)
}

func smallcaseWebhookPublisherProvider(ctx context.Context, conf *config.Config, awsConf aws.Config) queue.ExtendedPublisher {
	return wire.InitializeExtendedPublisher(ctx, awsConf, queue.NewDefaultMessage(), sqs.QueueName(conf.SmallcaseProcessMFHoldingsWebhookPublisher.GetQueueName()), conf.SmallcaseProcessMFHoldingsWebhookPublisher.GetBucketName(), sqs.ServiceName(cfg.VENDOR_NOTIFI_SERVICE))
}

func usStocksCacheStorageProvider(usstockRedisCl types.USStocksRedisStore) types2.USStocksCacheStorage {
	return cache.NewRedisCacheStorage(usstockRedisCl)
}

func questSDKClientConfProvider(conf *genconf.Config) *genconf2.Config {
	return conf.QuestSdk()
}

type CredgenicsStreamProducers struct {
	SmsCallbackStreamProducer          types2.SmsCallbackStreamProducer
	CallingCallbackStreamProducer      types2.CallingCallbackStreamProducer
	WhatsappCallbackStreamProducer     types2.WhatsappCallbackStreamProducer
	EmailCallbackStreamProducer        types2.EmailCallbackStreamProducer
	VoiceMessageCallbackStreamProducer types2.VoiceMessageCallbackStreamProducer
}

func InitializeCredgenicsCallbackStreamProducers(conf *config.Config, awsConf aws.Config) CredgenicsStreamProducers {
	kinesisClient := kinesis.NewKinesisClient(&awsConf)

	var smsCallbackStreamProducer types2.SmsCallbackStreamProducer
	var callingCallbackStreamProducer types2.CallingCallbackStreamProducer
	var whatsappCallbackStreamProducer types2.WhatsappCallbackStreamProducer
	var emailCallbackStreamProducer types2.EmailCallbackStreamProducer
	var voiceMessageCallbackStreamProducer types2.VoiceMessageCallbackStreamProducer

	streamConfig := conf.CredgenicsCallbackStreamProducer

	var initErr error
	smsCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.SmsStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.SmsStream)
	callingCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.CallingStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.CallingStream)
	whatsappCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.WhatsappStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.WhatsappStream)
	emailCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.EmailStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.EmailStream)
	voiceMessageCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.VoiceMessageStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.VoiceMessageStream)

	return CredgenicsStreamProducers{smsCallbackStreamProducer, callingCallbackStreamProducer, whatsappCallbackStreamProducer, emailCallbackStreamProducer, voiceMessageCallbackStreamProducer}
}

func handleStreamInitErr(err error, streamProducer *cfg.KinesisProducer) {
	if err != nil {
		logger.Panic("Error initialising producer for stream "+streamProducer.StreamName, zap.Error(err))
	}
}
