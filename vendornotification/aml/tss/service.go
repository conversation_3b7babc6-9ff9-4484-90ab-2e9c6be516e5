package tss

import (
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/aml"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/api/vendors/tss"
	amlVg "github.com/epifi/gamma/vendorgateway/aml"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	config                      *config.Config
	tssWebhookCallBackPublisher queue.Publisher
}

type TssWebhookCallBackPublisher queue.Publisher

func NewService(conf *config.Config, tssWebhookCallBackPublisher TssWebhookCallBackPublisher) *Service {
	return &Service{
		config:                      conf,
		tssWebhookCallBackPublisher: tssWebhookCallBackPublisher,
	}
}

const (
	ProcessWebhookCallBack = "ProcessWebhookCallBack"
	Accepted               = "Accepted"
	TimeFormat             = "02-Jan-2006 15:04"
)

func (s *Service) ProcessWebhookCallBack(ctx context.Context, req *tss.ProcessWebhookCallBackRequest) (*tss.ProcessWebhookCallBackResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessWebhookCallBack, req.GetRequestId(), vendorsRedactor.Config)

	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.config.TssWhitelist, s.config.NumberOfHopsThatAddXForwardedFor,
		s.config.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	decisionDetails, dErr := getDecisionDetailsList(req.GetScreeningCaseRequestTransactions())
	if dErr != nil {
		logger.Error(ctx, "error in converting to BE decision details", zap.Error(dErr))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	var owner common.Owner
	// Callbacks from TSS are observed to have the parent company as "Epifi Technologies Pvt ltd" even
	// when it is sent as "Epifi" in the screening requests.
	// Source system is observed to be the same as the one sent in the screening request.
	// Hence, source system is used to determine the owner.
	switch req.GetSourceSystem() {
	case "Epifi_tech":
		owner = common.Owner_OWNER_EPIFI_TECH
	case "LMS":
		owner = common.Owner_OWNER_STOCK_GUARDIAN_TSP
	default:
		logger.Error(ctx, "unexpected source system", zap.String("source_system", req.GetSourceSystem()))
		return nil, status.Errorf(codes.InvalidArgument, "invalid source system: %s", req.GetSourceSystem())
	}

	tssWebhookEvent := &aml.ProcessCallbackForDecisionsOnCaseRequest{
		VendorRequestId: req.GetRequestId(),
		DecisionDetails: decisionDetails,
		VendorName:      commonvgpb.Vendor_TSS,
		Owner:           owner,
	}
	sqsMsgId, err := s.tssWebhookCallBackPublisher.Publish(ctx, tssWebhookEvent)
	if err != nil {
		logger.Error(ctx, "error publishing tss webhook callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "tss webhook callback event published to queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))

	return &tss.ProcessWebhookCallBackResponse{
		RequestId:                         req.GetRequestId(),
		RequestStatus:                     Accepted,
		ScreeningCaseResponseTransactions: getScrResponseTransactionList(req.GetScreeningCaseRequestTransactions()),
	}, nil
}

func getDecisionDetailsList(records []*tss.ScreeningCaseRequestTransactions) ([]*aml.DecisionDetails, error) {
	var res []*aml.DecisionDetails
	for _, record := range records {
		decision, dErr := amlVg.ConvertToBeCaseDecision(record.GetFinalDecision())
		if dErr != nil {
			return nil, dErr
		}
		pepType, pErr := amlVg.ConvertToBePepType(record.GetPep())
		if pErr != nil {
			return nil, pErr
		}
		watchlistCategories, wErr := convertToBeWatchlistCategories(record.GetReputationalClassification())
		if wErr != nil {
			return nil, wErr
		}
		approvedOn, tErr := time.ParseInLocation(TimeFormat, record.GetApprovedOn(), datetime.IST)
		if tErr != nil {
			return nil, tErr
		}
		res = append(res, &aml.DecisionDetails{
			TransactionId:              record.GetTransactionId(),
			RecordIdentifier:           record.GetRecordIdentifier(),
			VendorCaseId:               strconv.FormatInt(record.GetCaseId(), 10),
			VendorCaseUrl:              record.GetCaseUrl(),
			CaseDecision:               decision,
			PepType:                    pepType,
			PepClassification:          record.GetPepClassification(),
			AdverseMedia:               record.GetAdverseMedia(),
			AdverseMediaClassification: record.GetAdverseMediaClassification(),
			WatchlistCategories:        watchlistCategories,
			FinalRemarks:               record.GetFinalRemarks(),
			ApprovedOn:                 timestamppb.New(approvedOn),
			ApprovedBy:                 record.GetApprovedBy(),
		})
	}
	return res, nil
}

func convertToBeWatchlistCategories(repClassification string) ([]aml.AmlWatchlistCategory, error) {
	categories := strings.Split(strings.ToLower(repClassification), ",")
	var ret []aml.AmlWatchlistCategory
	for _, category := range categories {
		switch strings.ToLower(category) {
		case "high-risk private list":
			ret = append(ret, aml.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_OTHERS)
		case "law enforcement":
			ret = append(ret, aml.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT)
		case "other bodies":
			ret = append(ret, aml.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_OTHERS)
		case "regulatory enforcement":
			ret = append(ret, aml.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT)
		case "":
			continue
		default:
			return nil, fmt.Errorf("error in converting watchlist category string to enum: %v", category)
		}
	}
	return ret, nil
}

func getScrResponseTransactionList(records []*tss.ScreeningCaseRequestTransactions) []*tss.ScreeningCaseResponseTransactions {
	var res []*tss.ScreeningCaseResponseTransactions
	for _, record := range records {
		res = append(res, &tss.ScreeningCaseResponseTransactions{
			TransactionId:          record.GetTransactionId(),
			TransactionStatus:      Accepted,
			TransactionDescription: "",
		})
	}
	return res
}
