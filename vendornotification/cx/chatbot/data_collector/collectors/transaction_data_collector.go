package collectors

import (
	"context"
	"sort"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type TransactionDataCollector struct {
	orderClient orderPb.OrderServiceClient
}

func NewTransactionDataCollector(
	orderClient orderPb.OrderServiceClient,
) *TransactionDataCollector {
	return &TransactionDataCollector{
		orderClient: orderClient,
	}
}

// CollectData collects transaction data using transaction ID
func (b *TransactionDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	externalTransactionId := inputParams.GetTransactionId()
	if externalTransactionId == "" {
		return nil, errors.New("transaction ID is required")
	}

	orderWithTxnRequest := &orderPb.GetOrdersWithTransactionsRequest{
		OrderIdentifiers: []*orderPb.OrderIdentifier{
			{
				Identifier: &orderPb.OrderIdentifier_ExternalId{
					ExternalId: externalTransactionId,
				},
			},
		}}

	orderWithTransactionDetail, err := b.orderClient.GetOrdersWithTransactions(ctx, orderWithTxnRequest)
	if rErr := epifigrpc.RPCError(orderWithTransactionDetail, err); rErr != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, errors.New("transaction not found")
		}
		return nil, err
	}

	var transactions []*customerTransactionData

	orderWithTxnList := orderWithTransactionDetail.GetOrderWithTransactions()
	for _, orderWithTxn := range orderWithTxnList {
		txns := orderWithTxn.GetTransactions()
		for _, txn := range txns {
			orderTags := orderWithTxn.GetOrder().GetTags()

			txnData := &customerTransactionData{
				PaymentProtocol:   txn.GetPaymentProtocol(),
				Provenance:        orderWithTxn.GetOrder().GetProvenance(),
				P2P_P2M:           getReceiverType(orderTags),
				ErrorCode:         getPayerErrorCode(txn.GetDetailedStatus().GetDetailedStatusList()),
				TransactionStatus: txn.GetStatus().String(),
				CreatedAt:         GetTimeInHumanReadableFormatForTxnDetails(txn.GetCreatedAt()),
				ExecutedAt:        GetTimeInHumanReadableFormatForTxnDetails(txn.GetExecutionTS()),
			}
			if len(orderTags) > 0 {
				txnData.Tags = orderTags
			}
			amount := money.ToDisplayStringInIndianFormat(txn.GetAmount(), 2, true)
			txnData.TransactionAmount = amount
			transactions = append(transactions, txnData)
		}
	}

	if len(transactions) == 0 {
		return nil, errors.New("transaction not found")
	}
	// Currently for nugget use-case we are only supporting one transaction support
	if len(transactions) > 1 {
		return nil, errors.New("transaction with multiple internal transactions are not supported yet")
	}

	return transactions[0], nil
}

func getReceiverType(orderTags []orderPb.OrderTag) string {
	if lo.Contains(orderTags, orderPb.OrderTag_MERCHANT) {
		return receiverTypeP2M
	}
	return receiverTypeP2P
}

// TransactionDetailedStatus_DetailedStatus
func getPayerErrorCode(detailedStatusList []*payment.TransactionDetailedStatus_DetailedStatus) string {
	if len(detailedStatusList) == 0 {
		return ""
	}

	sort.SliceStable(detailedStatusList, func(i, j int) bool {
		return detailedStatusList[i].CreatedAt.AsTime().After(detailedStatusList[j].CreatedAt.AsTime())
	})

	// Get error code from the latest status
	latestStatus := detailedStatusList[0]
	if latestStatus.GetStatusCodePayer() != "" {
		return latestStatus.GetStatusCodePayer()
	}
	return ""
}
