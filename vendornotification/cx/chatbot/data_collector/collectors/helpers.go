package collectors

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/risk/lea"
)

const (
	receiverTypeP2P = "P2P"
	receiverTypeP2M = "P2M"
	DateFormat      = "2 January 2006"
	TimeFormat      = "3:04 PM"
)

func GetTimeInHumanReadableFormatForFreezeDetails(timestamp *timestampPb.Timestamp) string {
	if timestamp == nil {
		return ""
	}
	timeIST := timestamp.AsTime().In(datetime.IST)
	formattedDate := timeIST.Format("02 Jan 2006")
	return formattedDate
}

// Date will be in following format: "3 March 1973 at 9:46 AM"
func GetTimeInHumanReadableFormatForTxnDetails(timestamp *timestampPb.Timestamp) string {
	if timestamp == nil {
		return ""
	}
	timeIST := timestamp.AsTime().In(datetime.IST)
	return timeIST.Format(DateFormat) + " at " + timeIST.Format(TimeFormat)
}

type FreezeBotData struct {
	ProcessedFreezeReason string                 `json:"processed_freeze_reason"`
	AccountStatus         string                 `json:"account_status"`
	FreezeType            string                 `json:"freeze_type"`
	FormId                string                 `json:"form_id,omitempty"`
	FormLink              string                 `json:"form_link,omitempty"`
	FormStatus            string                 `json:"form_status,omitempty"`
	FormExpiryDate        string                 `json:"form_expiry_date,omitempty"`
	LeaComplaintDetails   []*UnifiedLeaComplaint `json:"lea_complaint_details,omitempty"`
}

type UnifiedLeaComplaint struct {
	ComplaintId             string                      `json:"complaint_id"`
	Dates                   *Dates                      `json:"dates"`
	ReporterContactDetails  *lea.ReporterContactDetails `json:"reporter_contact_details"`
	OriginGeographicalState string                      `json:"origin_geographical_state"`
}

type Dates struct {
	DetailsReceivedDate string `json:"details_received_date,omitempty"`
	ComplaintDate       string `json:"complaint_date,omitempty"`
	AuditDate           string `json:"audit_date,omitempty"`
	AccountClosureDate  string `json:"account_closure_date,omitempty"`
}

type customerTransactionData struct {
	PaymentProtocol   any `json:"payment_protocol"`
	Provenance        any `json:"provenance"`
	CreatedAt         any `json:"created_at,omitempty"`
	ExecutedAt        any `json:"executed_at,omitempty"`
	TransactionAmount any `json:"transaction_amount"`
	Tags              any `json:"tags,omitempty"`
	P2P_P2M           any `json:"p2p_p2m"`
	ErrorCode         any `json:"error_code,omitempty"`
	TransactionStatus any `json:"transaction_status"`
}
