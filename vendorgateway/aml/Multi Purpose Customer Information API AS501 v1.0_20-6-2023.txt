TrackWizz API Document 

Multi-Purpose Customer Information API- 
AS501(v1.0) Document 

Document Version: v1.0 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
1 



 

 

Contents 

1. Overview 5 

2. Request Field Description 6 

2.1. Main Request Data Fields 6 

2.2. Customer Data Fields (Individual & Legal Entity) 7 

2.3. Related Person Data Fields (Only when Legal Entity) 36 

2.4. Relation Data Fields (Only when Legal Entity) 50 

3. Sample Decrypted Request & Response for Individual: 51 

3.1. For Initial Screening (Purpose: 01) 51 

3.1.1. Sample No Hits Found (Request & Response ) 51 

3.2. Initial & Continuous Screening (Purpose: 01 & 04) 59 

3.2.1. Sample No Hits Found (Request & Response) 59 

4. Response Field Description 68 

4.1. Main Data Fields 68 

4.2. Screening Specific Fields (Customer & Related Person) 68 

5. Steps for Encryption & Decryption 70 

5.1. Encryption 70 

5.1.1. Sample Encrypted Request 70 

5.1.2. Sample Encrypted Response 74 

5.2. Decryption 76 

6. Enumerations: 77 

6.1 Segment Enum 77 

6.2 Customer and RelatedParty Status Enum 77 

6.3 Constitution Type Enum 78 

6.4 Gender Enum 80 

6.5 Marital Status Enum 80 

6.6 Country Enum 81 

6.7 Occupation Type Enum 97 

6.8 Nature Of Business Enum 97 

6.9 Product Enum 99 

6.10 Income Range Enum 100 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
2 



 

6.11 Currency Enum 100 

6.12 PEP Enum 100 

6.13 PEP Classification Enum 101 

6.14 AdverseReputation Classification Enum 102 

6.15 Tags Enum 103 

6.16 Channel Enum 103 

6.17 RegulatoryAMLRisk Enum 104 

6.18 State Enum 104 

6.19 Qualification Enum 106 

6.20 RegAMLSpecialCategory Enum 106 

6.21 Agency Enum 107 

6.22 Prefix Enum 107 

6.23 Document Enum 108 

6.24 Purpose Enum 110 

6.24.1. Purpose-wise Mandatory Fields 111 

6.25 Relation Enum 113 

6.26 KYCAttestation Enum 115 

7. Variations 116 

7.1. Decrypted Request and Response Variations for Initial Screening (Individual) 116 

7.1.1. Confirm & Probable Hit (Request) 116 

7.1.2. Confirm & Probable Hit (Response) 122 

7.1.3. Probable Hits (Request) 125 

7.1.4. Probable Hits (Response) 132 

7.2. Decrypted Request and Response Variations for Continuous Screening (Individual) 135 

7.2.1. Confirm & Probable Hits (Request) 135 

7.2.2. Confirm & Probable Hit (Response) 141 

7.2.3. Probable Hits (Request) 145 

7.2.4. Probable Hits (Response) 151 

7.2.5. Validation Failure (Request) 154 

7.2.6. Validation Failure (Response) 160 

8. Request And Response XSD 164 

8.1 Request XSD 164 

8.2 Response XSD (Purpose 01) 182 

8.3 Response XSD (Purpose 04) 185 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
3 



 

9. Validations 188 

9.1 Main Request Validations: 188 

9.2 Request Data Validations: 189 

9.3 Relational Validations 192 

9.3.1. Applicable for Screening 192 

9.3.2. General Validations 193 
 
 
 
 
 
 
  
 
 
 
 

 
 
 
 
 
  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
4 



 

 
1. Overview 

This Restful API helps FI applications to create new and update existing individual and legal entities in 
TrackWizz. These FI applications will request AS501 API with all the valid customer details in the 
following JSON format and TrackWizz will be able perform any of the below actions for the customers 
basis the data send in the API request to TrackWizz app. All the API requests and responses will be end- 
to-end encrypted. 

1.1 Initial Screening 
1.2 Continuous Screening 

If the application encounters a request with a new combination of the SourceSystemName and 
SourceSystemCustomerCode, then the record/customer details would be added into the TrackWizz 
application. 

If the application encounters a request with a existing combination of the SourceSystemName and 
SourceSystemCustomerCode, then the record/customer details would be updated into the TrackWizz 
application 

Resource URL  

Source URL  
 

NOTE: Make sure to whitelist the above URL in your environment before using it. 
 

Resource Information 
 

Request & Response Format: JSON 
 

Request Type:  POST 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
5 



 

2. Request Field Description 
 

2.1. Main Request Data Fields 
 
 

       
       
      
  Applicable Single/    

Field Name / XML  For Data   
Tag Mandatory IND/LE/Both Multiple Type Length Remarks 

       
      Unique serial number that is used to identify 
      the record for processing. RequestId should 
      be different for every record. 
      
RequestId Yes Both Single String 200 

       
     Name of the Source System from where the 
     data is being passed. The source system name 
     mentioned in this field should be according to 
     TrackWizz Code as per the Agency Enum 
     values. 
     
SourceSystemName Yes Both Single Enum 

       
      Static Value will be shared by the TrackWizz 
      team during implementation. 
ApiToken Yes Both Single String 100 

       
      The action to be performed on the request 
      provided by the customer. Multiple purposes 
      can be passed based on the Enum values 
      provided, which will be used to customize the 
      response. 
       
      Should be according to TrackWizz Code. 
      
      
Purpose Yes Both Multiple Enum 2 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
6 



 

       
      Each request gets encrypted using two types 
      of encryption. First, the data will get 
      encrypted using a symmetric session key, 
      this key will be unique for each request. This 
      key will also be sent with the request after 
      encrypting it using an asymmetric key. 
      
      
      
SessionKey Yes Both Single String 100 

 
 
 
 

2.2. Customer Data Fields (Individual & Legal Entity) 
 
 

      

      

      

      

      

      

Field Name / JSON Applicable for Values Data Length  

Tag IND/LE/Both Allowed Type  Remarks 

      
SourceSystemCustomerCo 

     de is a unique ID assigned 
SourceSystemCustomer     by the source system to 
Code Both Single String Max the customer. 

      
     Unique number assigned to the 
     customer to uniquely identify the 
     record. 
UniqueIdentifier Both Single String Max 

      
The date on which the 

    customer record was 
    created in the clients 
    Source System or the 
    client was on boarded. 
SourceSystemCustomer     

CreationDate Both Single String Format should be "DD- 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
7 



 

     
MMM-YYYY". Should not be a Future 
Date. 

      
     If accounts are opened through EKYC or 
     biometric process , pass the values as "0" 
     or "1". 
      
     0 : No and 1: Yes 
EkycOTPbased IND Single String 100 

      
    Defines the segment of a business that 
    generates its own revenues and creates its 
    own product, product lines, or service 
    offerings. 
     
    Should be as per TrackWizz 
    Code. 
Segment Both Single Enum 

      
    List of products the customer opted 
    for. Multiple comma separated 
    values are allowed. 
     
    Should be as per TrackWizz 
    Code. 
    
SegmentStartDate Both Single String 

      
    List of products the customer opted 
    for. Multiple comma separated 
    values are allowed. 
     
    Should be as per TrackWizz 
    Code. 
    
Products Both Multiple Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
8 



 

      
     
     
    It will correspond to the current status 
    of the customer in its source system 
    whether it is Active,Inactive,Closed, 
    Dormant or Suspended. 
     
    Should be as per TrackWizz 
    Code. 
    
Status Both Single Enum 

      
    User needs to enter the specific date as 
    of which the Customer owns the status 
    mentioned in the source system. 
     
    Format should be "DD- MMM-YYYY". 
    Should not be a Future Date. 
    
    
EffectiveDate Both Single String 

      
    Defines the record type of the Customer. 
     
    Should be as per TrackWizz 
    Code. 
ConstitutionType Both Single Enum 

      
    Specify the title of the Customer 
    Name. 
     
    Should be as per TrackWizz 
Prefix IND Single Enum Code. 

      
    Specify the First Name of the Customer. 
     
    Criteria for passing First Name is as 
    follows : 
     
    First Character cannot be special 
    character. Value passed cannot have 
    only special character. 
    
FirstName Both Single String 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
9 



 

     
Consecutive Special Character are not 
allowed. The value passed should contain 
at one alphabet 

      
     Specify the Middle Name of the 
     Customer. 
      
     Criteria for passing Middle Name is as 
     follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain 
     at one alphabet 

MiddleName IND Single String Max 

      
     Specify the Last Name of the Customer. 
      
     Criteria for passing Last Name is as 
     follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain 
     at one alphabet 
     
     
LastName IND Single String Max 

      
     Specify the Alias name of the Customer 
Alias Both Single String 200 if any. 

      
    Specify the title of Customer's 
    Father. 
     
    Should be as per Trackwizz Code. 
FatherPrefix IND Single Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
10 



 

      
     Specify the First Name of the Customer's 
     Father. 
      
     Criteria for passing Father First Name is as 
     follows : 
      
      
      

     First Character cannot be special character. 
     Value passed cannot have only special 
     character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain at 
     one alphabet 
     
FatherFirstName IND Single String Max 

      
     Specify the Middle Name of the Customer's 
     Father. 
      
     Criteria for passing Father Middle Name is as 
     follows 
     : 
      
     First Character cannot be special character. 
     Value passed cannot have only special 
     character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain at 
     one alphabet 
     
FatherMiddleName IND Single String Max 

      
     Specify the Last Name of the Customer's 
     Father. 
      
     Criteria for passing Father Last Name is as 
     follows : 
      
     First Character cannot be special character. 
     Value passed cannot have only special 
     character. 
     Consecutive Special Character are not 
     allowed. 
FatherLastName IND Single String Max 

 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

11 



 

     
The value passed should contain at one 
alphabet 

      
    

Specify the title of the Customer's 
    Spouse.Should be as per TrackWizz Code. 
SpousePrefix IND Single Enum 

      
     Specify the First Name of the Customer's 
     Spouse. 
      
     Criteria for passing Spouse First Name is 
     as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain 
     at one alphabet 
     
SpouseFirstName IND Single String Max 

      
     Specify the Middle Name of the 
     Customer's Spouse. 
      
     Criteria for passing Spouse Middle Name 
     is as follows 
     : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain 
     at one alphabet 
     
SpouseMiddleName IND Single String Max 

      
     Specify the Last Name of the Customer's 
     Spouse. 
      
     Criteria for passing Spouse Last Name is as 
SpouseLastName IND Single String Max follows : 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
12 



 

      
First Character cannot be special 
character. Value passed cannot have 
only special character. 
Consecutive Special Character are not 
allowed. The value passed should contain 
at one alphabet 

      
    Specify the title of the Customer's 
    Mother. 
     
    Should be as per TrackWizz 
MotherPrefix IND Single Enum Codes. 

      
     Specify the First Name of the Customer's 
     Mother. 
      
     Criteria for passing Mother First Name is 
     as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain 
     at one alphabet 
     
MotherFirstName IND Single String Max 

      
     Specify the Middle Name of the 
     Customer's Mother. 
      
     Criteria for passing Mother Middle Name 
     is as follows 
     : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should 
     
MotherMiddleName IND Single String Max 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
13 



 

     
contain at one alphabet 

      
     Specify the Last Name of the Customer's 
     Mother. 
      
     Criteria for passing Mother Last Name is as 
     follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should contain 
     at one alphabet 
     
MotherLastName IND Single String Max 

      
    If the customer is minor( age less than 
    18) consider "Yes" or else consider "No". 
     
    Values should be passed as "0" and "1" 
     
    0 : No and 1: Yes". 
    
    
Minor IND Single String 

      
    Specify the gender of the customer. 
     
    Should be as per TrackWizz 
    Codes. 
Gender IND Single Enum 

      
    Specify the legally defined marital status 
    of the customer. 
MaritalStatus IND Single Enum 

      
    Specify the Job or Profession of 
    the customer. 
     
    Should be as per TrackWizz 
    Codes. 
OccupationType IND Single Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
14 



 

      
     User will be required to enter the value 
     under this field only if the value under 
     the Occupation Type field is passed as 
     "Oth". 
      
     This is a text field wherein the user will 
     be required to type in the Occupation 
     Type. 
     
     
OccupationTypeOther IND Text Field String 200 

      
    The nature of the business with which the 
    customer is involved with.Multiple comma 
    separated values can be passed. 
     
    Should be as per TrackWizz 
    Codes. 
    
NatureOfBusiness Both Single Enum 

      
     User will be required to enter the value 
     under this field only if the value under 
     the NatureofBusiness field is passed as 
     "Others". 
      
     This is a text field wherein the user will 
     be required to type in the Nature of 
     business. 
     
     
NatureOfBusinessOther Both Text Field String 200 

      
    The document to be submitted by the 
    customer as Proof of ID. 
     
    Should be as per TrackWizz 
    Codes. 
ProofOfIDSubmitted Both Single Enum 

      
    Date on which the customer was 
    born. 
DateofBirth/     
Incorporation Both Single String Format should be "DD- 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
15 



 

     
MMM-YYYY". 
 
For Non-Ind pass date in 
 
"Date of Incorporation" field. 

      
      
     Specify the Work email of the customer. 
WorkEmail Both Single String Max 

      
      
     Specify the Personal email of the 
PersonalEmail Both Single String Max customer. 

      
     Define ISD code of the 
     WorkMobileNumber related to the 
     customer. 
      
     Should be a 2 digit code in all the ISD 
WorkMobileISD Both Single String 2 fields . 

      
      
     Specify the Work mobile number of the 
WorkMobileNumber Both Single String 100 customer. 

      
      
      
     Define the ISD code of the 
     PersonalMobileNumber. 
      
     Should be a 2 digit code in all the ISD 
PersonalMobileISD Both Single String 2 fields 

      
     Specify the Personal Mobile Number 
     of the customer. 
PersonalMobileNumber Both Single String 100 

      
    Country where the customer 
    permanently resides. 
     
PermanentAddressCoun    Should be as per 
try Both Single Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
16 



 

     
TrackWizz Codes 

      
    Specify the ZipCode of the 
PermanentAddressZipC    PermanentAddress of the customer. 
ode Both Single String 

      
     Specify the Permanent Address details 
     of the Customer. 
      
     First Character cannot be special 
     character Value passed cannot have 
     only special character. The value passed 
     should contain at least one alphabet. 
     
     
PermanentAddressLine     
1 Both Single String Max 

      
     Specify the Permanent Address details 
     of the Customer. 
      
     First Character cannot be special 
     character Value passed cannot have 
     only special character. The value passed 
     should contain at least one alphabet. 
     
     
PermanentAddressLine     
2 Both Single String Max 

      
     Specify the Permanent Address details 
     of the Customer. 
      
     First Character cannot be special 
     character Value passed cannot have 
     only special character. The value passed 
     should contain at least one alphabet. 
     
     
PermanentAddressLine     
3 Both Single String Max 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
17 



 

      
PermanentAddressDistri     Specify the District where the Customer 
ct Both Single String 100 resides. 

      
     Specify the City where the Customer 
     permanently resides. 
PermanentAddressCity Both Single String 100 

      
    Specify the State where the customer 
    permanently resides. 
     
    Should be as per TrackWizz 
    Codes. 
PermanentAddressState Both Single Enum 

      
    Specify the State where the customer 
    resides if its other than the TrackWizz 
PermanentAddressOthe    provided Codes. 
rState Both Single 200 

      
    The Address Proof document code 
    submitted by the customer for 
    verification of permanent address. 
     
    Should be as per TrackWizz 
    Codes. 
PermanentAddressDocu    
ment Both Single Enum 

      
     Users will be required to enter the value 
     under this field only if the value under the 
     PermanentAddressDocum ent is passed 
     as "OthersPOA". 
     

PermanentAddressDocu     
mentOthersValue Both Text Field String Max 

      
    Specify the CorrespondenceAddressC 
    ountry of the Customer. 
     
    Should be as per TrackWizz 
CorrespondenceAddres    Codes. 
sCountry Both Single Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
18 



 

      
    Specify the ZipCode of Correspondence 
CorrespondenceAddres    Address of the Customer. 
sZipCode Both Single String 

      
     Specify the Correspondence Address 
     details of the Customer. 
      

     First Character cannot be special 
    

 character Value passed cannot have 
    only special character. The value passed 

     should contain at least one alphabet. 
     
     
CorrespondenceAddr     
essLine1 Both Single String Max 

      
     Specify the Correspondence Address 
     details of the Customer. 
      
     First Character cannot be special 
     character Value passed cannot have 
     only special character. The value passed 
     should contain at least one alphabet. 
     
     
CorrespondenceAddres     
sLine2 Both Single String Max 

      
     Specify the Correspondence Address 
     details of the Customer. 
      
     First Character cannot be special 
     character Value passed cannot have 
     only special character. The value passed 
     should contain at least one alphabet. 
     
     
CorrespondenceAddres     
sLine3 Both Single String Max 

      
    Specify the Correspondence Address 
CorrespondenceAddres    District of the Customer. 
sDistrict Both Single String 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
19 



 

      
    Specify the Correspondence Address 
CorrespondenceAddres    City of the Customer. 
sCity Both Single String 

      
    

Specify the Correspondence Address 
    

State of the Customer. 
    

 
 

   
 Should be as per TrackWizz 

CorrespondenceAddres    Codes. 
sState Both Single Enum 

      
     Specify the Correspondence State of the 
     Customer if other than TrackWizz 
     provided values. 
CorrespondenceAddres     
sOtherState Both Single String 200 

      
    Address Proof document code 
    submitted by the Customer for 
    verification of correspondence address. 
     
    Should be as per TrackWizz 
    Codes. 
CorrespondenceAddres    
sDocument Both Single Enum 

      
    Specify the Country for which the 
    customer holds the citizenship. Multiple 
    comma separated values can be passed. 
     
    Should be according to TrackWizz 
    Codes. 
    
Citizenship IND Multiple Enum 

      
    The Nationality of the Customer will be 
    passed. Multiple comma separated 
    values can be passed. 
     
    Should be according to TrackWizz 
    Codes. 
    
Nationality IND Multiple Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
20 



 

      
    Country in which the customer 
    resides. 
     
    Should be according to TrackWizz 
CountryOfResidence Both Single Enum Codes. 

      
    Country where the customer was 
    born. For Non-Ind pass country in the 
    "Country of Incorporation" field. 
     
    Should be as per TrackWizz 
    Codes. 
    
CountryOfBirth Both Single Enum 

      
    The city where the customer was 
    born. 
     
    For Non-Ind pass city in 
     
    "City of Incorporation" field. 
    
    
    
BirthCity Both Single String 

      
    Country for which the customer pays 
    the taxes. 
     
    Should be as per TrackWizz 
TaxResidencyCountry Both Complex Enum Code. 

      
    The date from which the customer is 
    eligible to pay Tax in the residence 
    country. 
TaxResidencyStartDate Both Complex String 

      
    The date from which the customer is 
    ending their business/ operations in the 
    residing country. 
TaxResidencyEndDate Both Complex String 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
21 



 

      
     Specify the Tax Identification Number 
TaxIdentificationNumbe     of the customer. 
r Both Complex String Max 

      
    The name of the country that issued the 
    customer's passport. 
     
    Should be as per TrackWizz 
    Code. 
PassportIssueCountry Both Single Enum 

      
     Specify the Customer's passport 
     number if passport is available with him. 
     
PassportNumber Both Single String Max 

      
    Specify the date from which the 
    Customer's passport will no longer be 
    applicable. Should be of "DD-MMM-
    YYYY" format . 
     
    Should always be a Future Date. 
    
PassportExpiryDate Both Single String 

      
     GSTIN(Goods and Services Tax 
     Identification Number) is a unique 15 digit 
     identification number assigned to every 
     taxpayer (primarily dealer or supplier or 
     any business entity) registered the GST 
     regime. 
     
     
GSTIN Both Single String 15 

      
   Specify the date on which the customer 
   was allotted the GSTIN (Goods and Tax 
   Identification Number) 
    
   Should be the DD-MMM- YYYY format. 
   
GSTINStartDate Both Single 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
22 



 

      
Should always be a Past Date or 
Current Date 

      
   Specify the end date of the GSTIN allotted 
   to the customer . 
    
   Should be the DD-MMM- YYYY format. 
    
   Should always be a Future Date. 
   
   
GSTINEndDate Both Single 

      
     Specify the Customer's Voter Id 
     number if available. 
VoterIdNumber Both Single String Max 

      
     Specify the Customer’s Driving License 
     Number if available. 
DrivingLicenseNumber IND Single String Max 

      
    Specify the date from which the 
    Customer's Driving License will no 
    longer be applicable. Should be of "DD-
    MMM- YYYY" format . Should always be 
    a Future Date. 
DrivingLicenseExpiryDat    
e IND Single Date 

      
     Aadhaar is a 12 digit individual 
     identification number issued by the 
     Unique Identification Authority of India 
     on behalf of the Government of India. 
      
     Pass 12 digit Aadhaar number 
     (123456789121) or with pass 8X with last 
     4 digits of the customer’s Aadhaar 
     number (XXXXXXXX1234) OR last 4 
     
     
     
     
AadhaarNumber Both Single String 100 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
23 



 

     
digits of the customer’s 
Aadhaar number (1234) 

      
     Aadhaar Data Vault is a centralized 
     storage for all the Aadhaar numbers 
     collected by the AUAs/KUAs/Sub-AUAs/ 
     or any other agency for specific purposes 
     under Aadhaar Act and regulation. 
     
     

AadhaarVaultReference     
Number Both Single String 100 

      
     NREGA Number of the Customer. 
NREGANumber Both Single String 100 

      
     The value of the CKYC Identification 
     type NPR Letter. 
NPRLetterNumber Both Single String 100 

      
     Director Identification Number or DIN is 
     a registration required for any person 
     who wishes to register a company. 
DirectorIdentificationNu     
mber IND Single String 100 

      
    Form for declaration to be filed by an 
    individual or a person (not being a 
    company or firm) who does not have a 
    permanent account number(PAN). 
     
    If FormSixty is passed, the value passed in 
    this field should be "1" and if FormSixty 
    is not passed, the value passed here 
    should be "0" . 
    
    
    
    
FormSixty Both Single String 

      
     Specify the Pan number of the customer. 
PAN Both Single String 100 If Pan 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
24 



 

     
number is passed the value in the 
Form Sixty field should be "0" and if 
Pan is not passed, the value passed 
here should be "0". 

      
     Identification number provided to the 
     customer who has completed the KYC 
     process. 
CKYCNumber Both Single String 100 

      
     Company Identification Number 
     issued by the Ministry of Corporate 
CompanyIdentificationN     Affairs India. 
umber LE Single String 100 

      
CompanyRegistrationNu     Provide the registration number of 
mber LE Single String 100 the company. 

      
     The name of the country where the 
CompanyRegistrationCo     company was registered. 
untry LE Single String 100 

      
     GIIN means the Global Intermediary 
     Identification Number which is a 19- 
     character identification number in the 
     format XXXXXX.XXXXX.XX.XXX 
     assigned to the reporting entity by the 
     USA. 
GlobalIntermediaryIden     
tificationNumber LE Single String 100 

      
    The type of Attestation done for the 
    customer. 
     
    Should be as per TrackWizz 
KYCAttestationType Both Single Enum Codes. 

      
    The date on which the KYC was initiated 
    for the on boarded customer by the 
KYCDateOfDeclaration Both Single String 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
25 



 

     
institution. 
 
Format should be “DD- MMM-
YYYY”. 

      
     The location where the KYC Was 
     initiated for the on boarded customer. 
KYCPlaceOfDeclaration Both Single string 100 

      
    The date when the KYC details given 
    were successfully verified by a KYC 
    Employee of the Institution. 
    
KYCVerificationDate Both Single String 

      
     The employee who verified the KYC 
     details of the on boarded customer. 
      
     Criteria for passing KYCEmployee 
     Name is as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should 
     contain at one alphabet 
     
     
     
KYCEmployeeName Both Single String Max 

      
     The designation of the verified 
     employee in the company’s hierarchy. 
      
     Criteria for passing KYC Employee 
     Designation is as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special 
     
KYCEmployeeDesignatio     
n Both Single String 100 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
26 



 

     
Character are not allowed. The value 
passed should 

      
     The branch of the institution which 
     initiated and verified the KYC Details of 
     the on boarded customer. 
      
     Criteria for passing KYC Verification 
     Branch Name is as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should 
     contain at one alphabet 
     
     
     
     
KYCVerificationBranch Both Single String 100 

      
     The Unique Identification Code of the 
     Employee who carried out the KYC 
     Verification. 
      
     Criteria for passing KYC Employee 
     Code is as follows : 
      
     First Character cannot be special 
     character. Cannot have only special 
     characters Alphanumeric 
     
     
KYCEmployeeCode Both Single String Max 

      
    Document to be submitted for 
    establishing the Identity of the 
    Customer. 
     
    Should be as per TrackWizz 
    Code. 
IdentityDocument Both Single Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
27 



 

      
    Specify if the company is Listed or Not, 
    It will be applicable for Non Ind.Values 
    passed should be "0" or "1". 
     
    0: Listed 1: Not Listed 
    
Listed LE Single String 

      
    Country where the company 
    operates from 
    .Multiple comma separated values 
    can be passed. 
     
    Should be as per TrackWizz 
    Code. 
CountryofOperation Both Multiple Enum 

      
     To recognize the application from which 
     the request is being initiated. 
ApplicationRefNumber Both Single String 500 

      
     Holder details to be passed. Eg. 
     Applicant, Co- Applicant, etc. 
DocumentRefNumber Both Single String 500 

      
    A Regulatory AML risk assessment 
    process that analyzes a customer's risk 
    of exposure to financial crime. Should 
    be as per TrackWizz Code. 
    
RegAMLRisk Both Single Enum 

      
    Define the category of AML Risk 
    assigned to the on boarding customer. 
     
    Should be as per TrackWizz 
RegAMLRiskSpecialCate    Code. 
gory Both Complex Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
28 



 

      
    The date on which the AML Risk 
    Category was assigned to the on 
    boarding customer. 
     
    Format should be “DD- MMM-
RegAMLRiskSpecialCate    YYYY” 
goryStartDate Both Complex String 

      
    The date on which the AML Risk 
    Review was done previously. It should 
    be a past date value . 
     
    Format should be “DD- MMM-
RegAMLRiskLastRiskRev    YYYY” 
iewDate Both Single String 

      
    The date on which the next AML Risk 
    Review will be done. It should be a 
    future date value. 
     
    Format should be “DD- MMM-
RegAMLRiskNextRiskRe    YYYY” 
viewDate Both Single String 

      
     Provide the Income Range of the 
     customer. 
      
     Should be as per TrackWizz 
IncomeRange Both Single Enum 100 Codes. 

      
     Provide the Exact Income earned by 
ExactIncome Both Single Decimal (13,2) the customer. 

      
    Provide the Income currency of the 
    customer. 
     
    Should be as per TrackWizz 
IncomeCurrency Both Single Enum Code. 

      
    The date from which the customer's 
    income became active. 
IncomeEffectiveDate Both Single String 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
29 



 

      
     Describe any additional details of 
     Income of the customer. 
IncomeDescription Both Single String 200 

      
    Document to be submitted that 
    contains the Income details of the 
    customer. 
     
    Should be as per TrackWizz 
    Code. 
IncomeDocument Both Single Enum 

      
     Provide the Exact Net Worth of the 
ExactNetworth Both Single Decimal (13,2) Customer. 

      
    Provide the Net Worth currency of 
    the Customer. 
     
    Should be as per TrackWizz 
NetworthCurrency Both Single Enum Codes. 

      
    The date from which the customer's 
    Net Worth Income was active.It 
    should be a Past Date value. 
     
    Format should be “DD- MMM-
    YYYY” 
    
NetworthEffectiveDate Both Single String 

      
     Describe any additional details 
     about the Net worth Income of the 
     customer. 
NetworthDescription Both Single String 200 

      
    Document to be provided that 
    contains the Net worth Income 
    details of the customer . 
     
    Multiple comma separated values 
    can be 
NetworthDocument Both Single Enum 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
30 



 

     
passed. 
 
Should be as per TrackWizz 
Code. 

      
    Specify if the customer is Politically 
    Exposed or not. Values should be 
    passed as "0" and "1" 
     
    0 : No and 1: Yes". 
PoliticallyExposed Both Single Enum 

      
    Define the classification type of PEP 
    customer. Multiple comma 
    separated values are allowed. 
     
    Should be as per TrackWizz 
    Code. 
PoliticallyExposedClassif    
ication Both Multiple Enum 

      
    In-order to capture the 
    customer’s adverse reputation value 
    needs to be passed under this field. 
     
    Values should be passed as "0" and 
    "1" 
     
    0 : No and 1: Yes". 
AdverseReputation Both Single String 

      
    Multiple comma separated values 
    can be passed. 
     
    Should be as per TrackWizz 
AdverseReputationClass    Code. 
ification Both Multiple Enum 

      
AdverseReputationDeta     Additional details of Adverse 
ils Both Text Field String 400 Reputation if any. 

      
     A screening profile is against 
ScreeningProfile Both Single String 100 which the 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
31 



 

     
screening would be performed. It is a 
set of watchlist sources/feeds against 
which the customers record would be 
screened. User can pass the value 
under this field or not. If the user 
passes the value under this field, then 
the record will be screened against 
this specified screening profile. If the 
user does not pass the value under 
this then the default profile will be 
mapped to this customers record and 
screening will be performed against 
it. 

      
     This will be used for Screening to send 
     the document in the response when 
     there are no hits. Values passed will 
     be "0" or "1". 
ScreeningReportwhenNi     
l Both Single String 100 

      
     The type of Risk Profile used for the 
RiskProfile Both Single String 100 Customer. 

      
     This will be used for RiskRating to 
     send the document in the response 
     when the risk is low. 
     
     Pass value as "0" or "1" 0:No and 
     
RiskRatingReportWhenL     1:Yes 
ow Both Single String 100 

      
     Should be as per TrackWizz 
Tags Both Multiple Enum 100 Code. 

      
     Customer FamilyCode in the Source 
FamilyCode IND Text Field String Max System is 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
32 



 

     
captured here. 

      
    The medium through which the 
    customer is getting on boarded. 
    Should be as per TrackWizz Code. 
    
Channel Both Single Enum 

      
     Specify the First Name of the 
ContactPersonFirstNam     customer's contact person. 
e1 LE Single String Max 

      
     

Specify the Middle Name of the 
    

ContactPersonMiddleN customer's contact person. 
ame1 LE Single String Max 

      
     Specify the Last Name of the 
ContactPersonLastNam     customer's contact person. 
e1 LE Single String Max 

      
     Specify the First Name of the 
ContactPersonFirstNam     customer's contact person. 
e2 LE Single String Max 

      
     Specify the Middle Name of the 
ContactPersonMiddleN     customer's contact person. 
ame2 LE Single String Max 

      
     Specify the Last Name of the 
ContactPersonLastNam     customer's contact person. 
e2 LE Single String Max 

      
     Specify the Contact Person Designation 
ContactPersonDesignati     mentioned by the customer 
on1 LE Single String Max 

      
     Specify the Contact Person of 
ContactPersonDesignati     Designation mentioned by the 
on2 LE Single String Max customer 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
33 



 

      
     Specify the Contact Person Mobile ISD of 
ContactPersonMobileIS     the customer 
D LE Single String Max 

      
     Specify the ContactPerson Mobile 
ContactPersonMobileN     number of the customer 
o 2 LE Single String Max 

      
     Specify the ContactPerson Mobile ISD of 
ContactPersonMobileIS     the customer 
D2 LE Single String Max 

      
     Specify the Contact Person Mobile 
ContactPersonMobileN     number of the customer 
o2 LE Single String Max 

      
     Specify the Contact Person Email Id of 
ContactPersonEmailId1 LE Single String Max the customer 

      
     Specify the Contact Person Email Id of 
ContactPersonEmailId2 LE Single String Max the customer 

      
    Mention the Educational background 
    details of the Customer.Multiples 
    comma separated values can be 
    passed. Should be as per TrackWizz 
EducationalQualificatio    Code. 
n IND Multiple Enum 

      
    The date on which the Business started. 
    It should be a past date value. 
     
    Format should be “DD- MMM-YYYY” 
    
CommencementDate LE Single String 

      
     Specify the Maiden First Name. 
      
     Criteria for passing Maiden First 
     Name is as 
MaidenFirstName IND Single String Max 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
34 



 

     
follows : 
 
First Character cannot be special 
character. Value passed cannot have 
only special character. 
Consecutive Special Character are not 
allowed. The value passed should 
contain at one alphabet 

      
     Specify the Maiden Middle Name. 
      
     Criteria for passing Maiden Middle 
     Name is as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should 
     contain at one alphabet 
     
     
     
MaidenMiddleName IND Single String Max 

      
     Specify the Maiden Last Name. 
      
     Criteria for passing Maiden Last 
     Name is as follows : 
      
     First Character cannot be special 
     character. Value passed cannot have 
     only special character. 
     Consecutive Special Character are not 
     allowed. The value passed should 
     contain at one alphabet 
     
     
     
MaidenLastName IND Single String Max 

      
RelatedPersonCountfor    Specify the count of Related people 
CKYC Both Text Field String related to 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
35 



 

     the customer. 

      
     Description about the customer or 
     any other additional information to 

    
Notes be described here. 

Both Text Field String 800 

 

 
2.3. Related Person Data Fields (Only when Legal Entity) 

 
     
     
     
     

 Values  Len  
Allowed Datatype gth Remarks 

Field Name / JSON Tag 
     

    SourceSystemCustomerCode is a unique 
    ID assigned by the source system to the 
    customer. 

SourceSystemCustomerCode Single String Max 
     

   The date on which the customer record 
   was created in the clients Source System 
   or the client was on boarded. 
    

   Format should be "DD-MMM- 
   YYYY". Should not be a Future Date. 
   

SourceSystemCustomerCreationDate Single String 
     

    Unique number assigned to the 
    customer to uniquely identify the 

UniqueIdentifier Single String Max record. 
     

   Defines the record type of the Customer. 
ConstitutionType Single Enum 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
36 



 

     

Should be as per TrackWizz Code. 

     

   Specify the title of the 
   Customer Name. 
    

   Should be as per TrackWizz Code. 
Prefix Single Enum 

     

    Specify the First Name of the 
    Customer. 
     

    Criteria for passing First Name is as 
    follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. The 

    value passed should contain at one 

    alphabet 

FirstName Single String Max 
     

    Specify the Middle Name of the 
    Customer. 
     

    Criteria for passing Middle Name is as 
    follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. The 

    value passed should contain at one 

    alphabet 

MiddleName Single String Max 
      

    Specify the Last Name of the 
    Customer. 
     

    Criteria for passing Last Name is as 
    follows : 
     

    First Character cannot be special 
character. Value passed cannot 

LastName Single String Max have only 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
37 



 

    special character. Consecutive Special 
Character are not allowed. The value 
passed should contain at one 
alphabet 

     

    Specify the Alias name of the 
Alias Single String 200 customer if any. 

     

   Specify the title of Customer's Father. 
    

   Should be as per Trackwizz Code. 
   

FatherPrefix Single Enum 
     

    Specify the First Name of the 
    Customer's Father. 
     

    Criteria for passing Father First 
    Name is as follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. 

    The value passed should contain at 

    one 
alphabet 

FatherFirstName Single String Max 
      

    Specify the Middle Name of the 
    Customer's Father. 
     

    Criteria for passing Father Middle 
    Name is as follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. 

    The value passed should contain at 

    one 
alphabet 

FatherMiddleName Single String Max 
     

    Specify the Last Name of the 
    Customer's Father. 
     

FatherLastName Single String Max Criteria for   passing   Father 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
38 



 

    Last Name is as follows : 
 

First Character cannot be special 
character. Value passed cannot have 
only special character. Consecutive 
Special Character are not allowed. 
The value passed should contain at 
one 
alphabet 

     

   Specify the title of the 
   Customer's Spouse. 
    

   Should be as per TrackWizz Code. 
SpousePrefix Single Enum 

     

    Specify the First Name of the 
    Customer's Spouse. 
     

    Criteria for passing Spouse First 
    Name is as follows : 

     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. 

    The value passed should contain at 

    one 
alphabet 

SpouseFirstName Single String Max 
      

    Specify the Middle Name of the 
    Customer's Spouse. 
     

    Criteria for passing Spouse Middle 
    Name is as follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. 

    The value passed should contain at 

    one alphabet 

SpouseMiddleName Single String Max 
     

    Specify the Last Name of the 
SpouseLastName Single String Max Customer's Spouse. 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
39 



 

      
Criteria for passing Spouse Last Name is 
as follows : 

 
First Character cannot be special 
character. Value passed cannot have 
only special character. Consecutive 
Special Character are not allowed. The 
value passed should contain at one 
alphabet 

     

   Specify the title of the 
   Customer's Mother. 
    

   Should be as per TrackWizz Codes. 
MotherPrefix Single Enum 

     

    Specify the First Name of the 
    Customer's Mother. 
     

    Criteria for passing Mother First Name is 
    as follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. The 

    value passed should contain at one 

    alphabet 

MotherFirstName Single String Max 
      

    Specify the Middle Name of the 
    Customer's Mother. 
     

    Criteria for passing Mother Middle 
    Name is as follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. The 

    value passed should contain at one 

    alphabet 

MotherMiddleName Single String Max 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
40 



 

     

    Specify the Last Name of the 
    Customer's Mother. 
     

    Criteria for passing Mother Last Name 
    is as follows : 
     

    First Character cannot be special 

    character. Value passed cannot have 

    only special character. Consecutive 

    Special Character are not allowed. 

    The value passed should contain at 

    one 
alphabet 

MotherLastName Single String Max 
     

   Specify the Gender of the 
Gender Single Enum customer. 

    Date on which the customer was 
   born. 
   Format should be "DD-MMM- YYYY" 
    

   For Non-Ind pass date in "Date of 
   Incorporation" field. 

DateofBirth Single String 
     

    Specify the Work email of the 
WorkEmail Single String Max customer. 

     

    Specify the Personal email of the 
PersonalEmail Single String Max customer. 

     

   Country where the customer 
   permanently resides. 
    

   Should be as per TrackWizz Codes. 
PermanentAddressCountry Single Enum 

     
PermanentAddressOtherState Single String 200 

     

   Specify the ZipCode of 
   PermanentAddress of the 

PermanentAddressZipCode Single String Customer. 
     

    Specify the Permanent 
    Address details of the 
    Customer. 
    

PermanentAddressLine1 Single String Max 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
41 



 

     
Criteria for passing 
Permanent Address Line1 is as follows 
: 

 

First Character cannot be special 
character Value passed cannot have 
only special character. The value 
passed should contain at least one 
alphabet. 

     

    Specify the Permanent 
    Address details of the 
    Customer. 
     

    Criteria for passing 
    Permanent Address Line2 is as follows 
    : 
     

    First Character cannot be special 

    character Value passed cannot have 

    only special character. The value 

    passed should contain at least one 
alphabet. 

PermanentAddressLine2 Single String Max 
      

    Specify the Permanent 
    Address details of the 
    Customer. 
     

    Criteria for passing 
    Permanent Address Line3 is as follows 
    : 
     

    First Character cannot be special 

    character Value passed cannot have 

    only special character. The value 

    passed should contain at least 
one alphabet. 

PermanentAddressLine3 Single String Max 
     

   Specify the District where the 
PermanentAddressDistrict Single String Customer resides. 

     

   Specify the City where the 
   Customer permanently resides. 

PermanentAddressCity Single String 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
42 



 

     

   Specify the State where the 
   customer permanently resides. 
    

   Should be as per TrackWizz Codes. 
   

PermanentAddressState Single Enum 
    Specify the State where the 
   customer resides if its other then 
   the TrackWizz 

PermanentAddressOtherState Single 200 provided Codes. 
     

   The Address Proof document code 
   submitted by the customer for 
   verification of permanent address. 
    

   Should be as per TrackWizz Codes. 
   

PermanentAddressDocument Single Enum 
     

    Users will be required to enter the 
    value under this field only if the 
    value under the 
    PermanentAddressDocument 

PermanentAddressDocumentOthersV Text   is passed as "OthersPOA". 
alue Field String Max 

     

   Specify the 
   CorrespondenceAddressCoun try of the 
   Customer. 
    

   Should be as per TrackWizz Codes. 
CorrespondenceAddressCountry Single Enum 

     

   Specify the ZipCode of 
   Correspondence Address of the 

CorrespondenceAddressZipCode Single String Customer. 
     

    Specify the Correspondence 
    Address details of the Customer. 
     

    Criteria for passing 
    Correspondence Address Line1 is as 
    follows : 
     

    First Character cannot be special 

    character Value passed cannot 
have only 

CorrespondenceAddressLine1 Single String Max 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
43 



 

    special character. The value passed 
should contain at least 
one alphabet. 

     

    Specify the Correspondence Address 
    details of the Customer. 
     

    Criteria for passing 
    Correspondence Address Line2 is as 

    follows : 

     

    First Character cannot be special 

    character Value passed cannot have only 

    special character. The value passed 

    should contain at least 

    one alphabet. 

CorrespondenceAddressLine2 Single String Max 
     

    Specify the Correspondence Address 
    details of the Customer. 
     

    Criteria for passing 
    Correspondence Address Line3 is as 
    follows : 
     

    First Character cannot be special 

    character Value passed cannot have only 

    special character. The value passed 

    should contain at least 

    one alphabet. 

CorrespondenceAddressLine3 Single String Max 
     

   Specify the District of Correspondence 
   Address of the Customer. 

CorrespondenceAddressDistrict Single String 
     

   Specify the City of Correspondence 
   Address the Customer. 

CorrespondenceAddressCity Single String 
     

   Specify the State of Correspondence 
   Address of the Customer. 
    

   Should be as per TrackWizz Codes. 
   

CorrespondenceAddressState Single Enum 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
44 



 

    Specify the Correspondence State of 
    the Customer if other than 
    TrackWizz provided values. 

CorrepondenceAddressStateOther Single String 200 
     

   Address Proof document code submitted 
   by the Customer for verification of 
   correspondence address. 
    

   Should be as per TrackWizz Codes. 
   

CorrespondenceAddressDocument Single Enum 
     

   Specify the Country for which the 
   customer holds the citizenship. Multiple 
   comma separated values can be passed. 
    

   Should be according to TrackWizz 

   Codes 
   

Citizenship Multiple Enum 
     

   The Nationality of the Customer will 
   be passed. Multiple comma 
   separated values can be passed. 
    

   Should be according to TrackWizz 
   Codes 

Nationality Multiple Enum 
     

   Country in which the customer 
   resides. 
    

   Should be according to TrackWizz 
CountryOfResidence Single Enum Codes 

      

   Country where the customer was born. 
   For Non-Ind pass country in the 
   "Country of Incorporation" field. 
    

   Should be as per TrackWizz Codes.. 
   

CountryOfBirth Single Enum 
    The city where the customer was born. 
   For Non-Ind pass city in "City of 
   Incorporation" field. 
   

BirthCity Single String 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
45 



 

    Should be as per TrackWizz 
Codes. 

    Country for which the customer 
   pays the taxes. 
    
   Should be as per TrackWizz Code. 

TaxResidencyCountry Complex Enum 
     

   The date from which the customer 
   becomes eligible for paying taxes. 

TaxResidencyStartDate Complex String 
     

   The end-date from which the 
   customer becomes non eligible for 

TaxResidencyEndDate Complex String paying taxes. 
     
    Specify the Tax Identification Number of 

TaxIdentificationNumber Complex String Max the customer. 
     

   The name of the country that issued the 
   customer's passport. 
    

   Should be as per TrackWizz Code.. 
   

PassportIssueCountry Single Enum 
     

    Specify the Customer's passport 
    number if its 

PassportNumber Single String 100 available. 
     

   Specify the date from which the 
   Customer's passport will no longer be 
   applicable. 
   Should be of "DD-MMM- YYYY" 
   format . 
    

   Should always be a Future Date 
PassportExpiryDate Single String value. 

    GSTIN(Goods and Services Tax 
    Identification Number) is a unique 15 
    digit identification number assigned 
    to every taxpayer (primarily dealer or 
    supplier or any business entity) 
    registered the GST 
    regime. 
    

GSTIN Single String 15 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
46 



 

    Specify the date on which the 
  customer was allotted the GSTIN 
  (Goods and Tax Identification 
  Number) Should be the DD-MMM- 
  YYYY format. 
  Should always be a Past Date 
  or Current Date 

GSTINStartDate Single 
    Specify the end date of the GSTIN 
  allotted to the 

GSTINEndDate Single customer 
     

    Specify the Customer's Voter Id number 
VoterIdNumber Single String Max if available. 

     

    Specify the Customer’s Driving 
    License Number if available 

DrivingLicenseNumber Single String Max 
     

   Specify the date from which the 
   Customer's Driving License will no 
   longer be applicable. Should be of "DD- 
   MMM-YYYY" format . Should 
   always be a Future Date. 

DrivingLicenseExpiryDate Single String 
     

    Aadhaar is a 12 digit individual 
    identification number issued by the 
    Unique Identification Authority of India 
    on behalf of the Government of India. 
     

    Pass 12 digit Aadhaar number 
    (123456789121) or with pass 8X with last 
    4 digits of the 
    customer’s Aadhaar number 
    (XXXXXXXX1234) OR last 4 
    digits of the customer’s 
    Aadhaar number (1234) 
    

AadhaarNumber Single String Max 
     

    Aadhaar Data Vault is a centralized 
    storage for all the Aadhaar numbers 
    collected by the AUAs/KUAs/Sub-AUAs/ 
    or any other agency for specific purposes 
    under Aadhaar Act and regulation. 
    

AadhaarVaultReferenceNumber Single String Max 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
47 



 

     

    NREGA Number of the Customer. 
NREGANumber Single String Max 

     

    The value of the CKYC 
    Identification type NPR 

NPRLetterNumber Single String Max Letter. 
     

    Director Identification Number or DIN is 
    a registration required for any person 
    who wishes to register 
    a company. 

DirectorIdentificationNumber Single String Max 
     

   Form for declaration to be filed by an 
   individual or a person (not being a 
   company or firm) who does not have a 
   permanent account number(PAN). 
    

   If FormSixty is passed, the value passed 
   in this field should be "1" and if 
   FormSixty is not passed, the value 
   passed here should be "0" 

   
   

FORMSixty Single String 
     

    Specify the Pan number of the 
    customer. If Pan number is passed the 
    value in the Form Sixty field should be 
    "0" and if Pan is not passed, the value 
    passed here should be "0". 
    

PAN Single String Max 
     

    Identification number provided to the 
    customer who has completed the KYC 
    process. 

CKYCNumber Single String Max 
     

    Document to be submitted for 
    establishing the Identity of the Customer. 
     

    Should be as per TrackWizz Code.. 
    

IdentityDocument Single Enum 100 
     

PoliticallyExposed Single Enum Specify if   the   customer   is 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
48 



 

    Politically Exposed or not. Values should 
be passed as "0" and "1" 

0 : No and 1: Yes". 

     

   Define the classification type of PEP 
   customer. Multiple comma separated 
   values can be passed. Should be as per 
   TrackWizz Code. 

PoliticallyExposedClassification Multiple Enum 
    

Define if the customer is known for 
   

some unfavorable act or quality.Values 
   

should be passed as "0" and "1" 
   
   0 : No and 1: Yes". 

AdverseReputation Single String 
     

   Classify the customer according to the 
   type of bad reputation customer has. 
    

   Multiple commas separated values can 

   be passed. Should be as per TrackWizz 
   Code. 

AdverseReputationClassification Multiple Enum 
     

    Additional details of customer if under 
 Text   classified as adverse reputation if any. 

AdverseReputationDetails Field String 400 
     

    The type of Screening Profile used for 
    the Customer for Screening. 

ScreeningProfile Single String 100 
     

    This will be used for Screening to send 
    the document in the response when 
    there are no hits.Values passed will be 
    "0" or "1". 

ScreeningReportwhenNil Single String 100 
     

    The type of Risk Profile used for the 
RiskProfile Single String 100 Customer 

     

    This will be used for RiskRating to 
    send the document in the response 
    when the risk is low. 

    Values should be passed as 
RiskRatingReportWhenLow Single String 100 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
49 



 

    "0" and "1" 

0 : No and 1: Yes". 
     

   Should be as per TrackWizz Code. 
Tags Multiple Enum 

     

    Description about the customer or any 
    other additional information to be 
 Text   described here. 

Notes Field String 800 
 
 
 
 
 

2.4. Relation Data Fields (Only when Legal Entity) 
 
 

     
Values    

Field Name / JSON Tag Allowed Datatype Length Remarks 
     

CustomerSourceSyste    Code of the customer in the specific source system 
mCode String Max from where the information flows in. 

Single 
     

RelatedPersonSourceS    Code of the related person in the specific source 
ystemCode String Max system from where the information flows in. 

Single 

     

RelationCode   Code that defines the relation of the related person 
Single Enum with the customer. 

     

   Date when the relation of the customer was 
established with the Related person Format should be 

RelationStartDate   
Single String "DD-MMM-YYYY". 

     
   Date when the relation of the customer ended with 

RelationEndDate String the Related person. 
Single 

     
ShareHoldingPercenta     
ge String 100 ShareHoldingPercentage for a given Relation. 

Single 
     
    Describe any additional information for relation of the 

RelationNotes String 500 customer and related person if any. 
Text 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
50 



 

 

3. Sample Decrypted Request & Response for Individual: 
Content-Type: application/json 

 

3.1. For Initial Screening (Purpose: 01) 

3.1.1. Sample No Hits Found (Request & Response ) 
Request 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 
 
"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
51 



 

"kycDateOfDeclaration": "11-Mar-2021", 

"kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "1", 

"regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", 
 

"networthEffectiveDate": "11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

52 



 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
53 



 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"constitutionTypeId": 0, 

"sourceSystemCustomerCode": "2550", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2550", 

"prefix": "Dr", 

"firstName": "Hansraj", 

"middleName": "Gitesh", 

"lastName": "Hemani", 

"fatherPrefix": "Mr", 

"fatherFirstName": "Gitesh", 

"fatherMiddleName": "Sambhaji", 

"fatherLastName": "Hemani", 

"spousePrefix": "Mrs", 

"spouseFirstName": "Sonali", 

"spouseMiddleName": "Hansraj", 

"spouseLastName": "Hemani", 

"motherPrefix": "Mrs", 

"motherFirstName": "Jayaprabha", 

"motherMiddleName": "Gitesh", 

"motherLastName": "Hemani", 

"gender": "01", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
54 



 

"dateofBirth": "11-Feb-1995", 

"workEmail": "<EMAIL>", 

"personalEmail": "<EMAIL>", 

"personalMobileISD": "91", 

"personalMobileNumber": "9950438478", 

"workMobileISD": "91", 

"workMobileNumber": "7330067911", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "Gokulnagri, Chawl no 15, Room no- 101", 

"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "Behind RK Hotel, Mumbai", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", 

"correspondenceAddressLine3": "Mahim West", 

"correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", 

"correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "IND", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
55 



 

"countryOfBirth": "IND", 

"birthCity": "Mumbai", 

"passportIssueCountry": "IND", 

"passportNumber": "PASS38142", 

"passportExpiryDate": "02-Feb-2025", 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", 

"nprLetterNumber": "NPR25689", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", 
 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "SP1", 

"screeningreportwhennil": "1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

56 



 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

} 

], 
 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null 

} 

 
Response  

{ 
"RequestId": "IND2550", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2550", 

"ValidationOutcome": "Success", 

"SuggestedAction": "Proceed", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

57 



 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

"PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "No", 

"HitsCount": 0 

"ConfirmedHit": "No", 

"ReportData": Base 64 Data, 

} 

] 

}, 
"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

], 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

] 

} 

} 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

58 



 

 
 

3.2. Initial & Continuous Screening (Purpose: 01 & 04) 
 

3.2.1. Sample No Hits Found (Request & Response) 
 Request : 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", 

"kycDateOfDeclaration": "11-Mar-2021", 

"kycPlaceOfDeclaration": "Mumbai", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

59 



 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "1", 

"regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month",  
 
"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", 

"networthEffectiveDate": "11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
60 



 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
61 



 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2550", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2550", 

"prefix": "Dr", 

"firstName": "Hansraj", 

"middleName": "Gitesh", 

"lastName": "Hemani", 

"fatherPrefix": "Mr", 

"fatherFirstName": "Gitesh", 

"fatherMiddleName": "Sambhaji", 

"fatherLastName": "Hemani", 

"spousePrefix": "Mrs", 

"spouseFirstName": "Sonali", 

"spouseMiddleName": "Hansraj", 

"spouseLastName": "Hemani", 

"motherPrefix": "Mrs", 

"motherFirstName": "Jayaprabha", 

"motherMiddleName": "Gitesh", 

"motherLastName": "Hemani", 

"gender": "01", 

"dateofBirth": "11-Feb-1995", 

"workEmail": "<EMAIL>", 

"personalEmail": "<EMAIL>", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

62 



 

"personalMobileISD": "91", 

"personalMobileNumber": "9950438478", 

"workMobileISD": "91", 

"workMobileNumber": "7330067911", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "Gokulnagri, Chawl no 15, Room no- 101", 

"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "Behind RK Hotel, Mumbai", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", 

"correspondenceAddressLine3": "Mahim West", 

"correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", 

"correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "IND", 

"countryOfBirth": "IND", 

"birthCity": "Mumbai", 

"passportIssueCountry": "IND", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
63 



 

"passportNumber": "PASS38142", 

"passportExpiryDate": "02-Feb-2025", 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", 

"nprLetterNumber": "NPR25689", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "SP1", 

"screeningreportwhenNil":"1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
64 



 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

}  
], 

 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

}  
], 

 

"GUID": null 
} 
 

Response  

{ 

"RequestId": "IND2550", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2550", 

"ValidationOutcome": "Success", 

"SuggestedAction": "Proceed", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
65 



 

"PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "No", 

"HitsCount": 0 

"ConfirmedHit": "No", 

"ReportData": Base 64 Data, 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

{ 
 

"Purpose": "Continuous Screening with email linked cases", 

"PurposeCode": "04", 

"Data": null, 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

} 

], 
 

"RelatedPersonResponse": null, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
66 



 

"RelatedPersonRelationResponse": null 

} 

] 

} 

} 
 
 
 
 
 
 
 
 
 

 
 
 
  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
67 



 

4. Response Field Description 
4.1. Main Data Fields 

 
  
Field Name Remarks 

  
RequestId 

As available in the request 
 
ValidationCode TrackWizz codes for validations 
 Description of the validation message that the user receives for a 
ValidationDescription particular validation 

  
OverallStatus Possible Values: AcceptedbyTW/RejectedbyTW 

 
4.2. Screening Specific Fields (Customer & Related Person) 

 
  
Field Name Remarks 

  
SourceSystemCustomerCode As available in the request 

  
ValidationOutcome Possible Values: Success/Failure 

  
Purpose Defines the name of the purpose 

  
PurposeCode As available in the request 

  
ValidationCode TrackWizz codes for validations 

  
 Description of the validation message that the user receives for a particular 
ValidationDescription validation 

  
ValidationFailureCount The total number of failure Count in a request 

  
 Specifies if hits are detected for a customer. Possible values are 
  
HitsDetected Yes/No 

  
 The total number of hits detected for a customer who is screened against various 
HitsCount sources. 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
68 



 

  
 A serial number generated by the system when any hits are found for a given 
CaseId customer or an related person. 

  
 A Case URL generated by the system through which specific actions can be taken 
CaseURL when any hits are found for a given customer or an related person. 

  
 Specifies whether the alert generated is a Confirmed Hit or Not. Possible values are 
  
ConfirmedHit Yes/No 

 
TrackWizz suggested action to be taken for the customer or related person basis 
the hit generated. Possible values are 
 
Stop/Review/Proceed 

  

 Stop- When it’s a confirmed hit. 
  
 Review- When it’s a probable hit. 
  

 Proceed- When there are no hits. 
SuggestedAction  

  
ReportData Will have a Base 64 Encoded Report link of the cases details. 

 
  
Source Contains the name of the Source against which the hits are detected for the customer. 

  
WatchlistSourceID TrackWizz Id for that Watchlist record. 

  
 Defines the match type of the hit detected for the customer or related party. Possible 
MatchType values are Probable/Confirmed 

  
 A system generated score depending on the configurations in the selected Screening 
Score Profile. 

  
ConfirmedMatchingAttributes List of attributes which were found under Confirmed Type Match. 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

69 



 

5. Steps for Encryption & Decryption 
5.1. Encryption  

Following are the steps to be followed for encryption: 

1. Generate a Session Key of 256 bit. 
2. Encrypt the request data using AES (Symmetric) algorithm with the above generated Session Key 
3. Encode the above encrypted data to Base64 string. 
4. Encrypt the session key using the Public Key of the Receiver, using RSA (Asymmetric Algorithm). 
5. Encode the above encrypted Session Key to Base64 string. 
6. Combine the encrypted data and encrypted session key into a string and then sign that string using 

the sender's private key. 
7. Add this encrypted and encoded data ,session Key and signature to the request. 
8. Note: Key sizes acceptable:- 512,1024,2048 bytes 

 
5.1.1. Sample Encrypted Request 

“EncryptedData”: 

“zIVIfsGjG7P/RlXY3LjkpeGs74K1RuGp/ktICXE9fTJsjDpWoPRea1n0syu5OtxjEvCzDF6/qpZz2LHyZWKrL/Yjbn 
JVdywL3z+WbKg2lOT9zgZKQ7pYCv7az5YReWNICztU/grRYAfDe1K0nBVG/biCn0C6Q64vL7PIR9yfimrkpkjh/ 
6mF8H4i0HfvldRWEBq84FZqU8QlLr+Kid6JlV0OBVnUaokb3z9EeshdbSMJtN1F2D8INCCUzlPdpWX+69rD1y 
fSzeOulmbryppl/I3jdVtgBNSjVlE6PSX0oqhOay031f6mGjLlJpOiY5qIv71WopcIzsr86vHAJP7MpZGvWciycGz 
GFPlxTcB2+FM0ulM+b4gOAcCYprm9RCDmpJ8nWi38mZPnus4yAdT/mkCEUHcqHuyNUxOzwHA2Ze7fhjS7 
/MskEpnYoWBNQJ5Td0k9zFB1915z2RSpXKdMY1FfZTh+GxGqr1p1q7WfVBnzinO5brx1jV/+UuyMTrHloFv 
M1I4TCj5wUalag+cov1PIYd83K7DL4Wxs0DLGsAwAd8oLZ1erBsK8NU1FqxTLZxz05yoo3kpHTj3bi5m6Wwl 
pVX6oM9a0f4Z68z5trnkki67Fsn2+L/FRrt3356Qq8CXtmC8Gdc5eeiwXlLVdQdzHgMDrK9sFp9/U8gRhoMAP 
2Ai+seVTt1R7Wj1syh4eGFepgaXzAgnAuV9ZyoSX/M+FbttsdtAcjP0sBxOK2mxEv7VoSFrXEAEALjMMgVMw 
6W0Cv1PgKO3jg2HEoMNy6kS/tWhIWtcQAQAuMwyBUzBmXPrrmDHD7Zmy5v8GEyAVTmlODDeVj6SwbZ 
xJOBHMA2Kv0Fs+0qzBdnpTuQfkIn8ieL3kdf9ffSFaCSD3MQJX2368FthOtWvg3jQJ86x+NrUEy6qafNTQskpsJ 
fufLpexDaeCW0HocCQ+HdbO4CEHeG3omdSg5ncGqFZ0H4XdOo4b7jszYzF87NaOQhq3pR9oBF9iDt+6INM 
xz/ZrxMnPbrnd8VtR9GIV9MdkINmG07Ncj21Iq9uFzdzwD0NbaMMOFRdv3aAGrH6O9M2UF0FMDsa94zKY 
K0tSd48PEicFpTgPmHlZ46obTZv8Z2fx+YHiLAKEZAFSwtyDE1/ykBLX4W1ep8XlcXTMkmAhZtY/Xbaq5hR9zd 
ErZQcskaSrz+dVW/DoCpYlbieElUNqiPpp4KUQbeSS9O224IVuAlK0V1Hf0TwuhqU1o5DW17e+u3JD1+6hlcX 
ZFIcSJ2xljnMzKafF+F+LKizgNcqCxpummF1RErvOMARmYV9Ndlqkgqdttbo/A70yhG6c3Kr+1h77Df5lCin5gx 
OAZ2JKsr/YohD1VjFgIZEopT04yW7q6WuKNJ8yIUeo4zQI9ArxvkbDdn/pQdcCXw/btk9xxsY/fYGLnnQbmCL 
4YSrx5qG3G1BNdVWG+c8/BqT2/aP4ebMi9BjQd/spcIONbQLKSLjfXQWZRFVVVPQ0kHkka8jNkqTBWhMdY 
wvIUHO9/ZgV02tgseXYIxuTdMStapXlSpYEnjEUh/NMlo7gsu9KAcfnDNC0IzQh850dtM+tXnMLvsZsusOum2 
WjOQi6SW4MP8Sx2ZFP2bYWQlX3Mf9nPdx3bCWylvKEhXv/Z6b3xrgbGPXuPvRZ0B+a0w8W7TJdDUBUqY3 
RPOP2tIii7oWVuv4iUbyI6xkv6yL6+FMJM+Lxoh6kTN8lCVxQgsH7e2uDmsUKxoMZfqz167FwX52706Jnp6V+ 
Uq8s2Yfa748YadTagxTbCItSeYQZnf4Vq7baH4Y42w6/8SxPVQaWCAO2NSJU17gDzpK3PzEYaFyLOSJrssyU1 
W34i0FMEwFFBQRRdGpCbR/UdkKzRfIAYvKxM14AtRzV4E5MEezEHDIwcCg8uQJhVw3bSwLN340XQUaxlU 
Vid5rhng/SW+vkiSK2z0k30bBzNwcCPelIdWSllZYWcbYdvT/2BzsAVrQ+lg29+Uk91tp3Sn4ZZH+VYqyWNGw 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
70 



 

O1fxVYUBXGt92hdilAVOAuSdDKqxOuDeuFIGlz13one04yE5RGzLzF4HvhlDUTYMpebno13BAfJSGkW3wxoP 
3WGCoEPtQBwsworDH1SeZvKorvKdQsjxcWOWVYFEwEL4gxdQFdPhjOFLVkZhxu5zrauPfdmF1wT67FocTD 
3c/i1eOn+BI2cyUpW/+0Pnoj1zE5HjQB8MVPHk4srkpLHv0jEGoB7o60iC6BgtoeF/YO+aD7uQrtFY1GL+248Z 
6T2wsVVoAkyS+v/JKXkfvwIaf7FeP9n8Qn8RQoWYNrUS6fDPdN31hqj08jp+Vk9QfQATcx6AF0TGELr1+xwpC 
6OZLtYsj403qucKZPluEOrUYIugcqcFNBR6ry6whd3nszw6EqrzUfV9T/bssyuRCKJw05m4v2PXbEWG3pG+u/ 
eYCf0dtXTLfDNvjtaW2+IxBafa9oSDxJo/GYiQdI4a5HMHZi4ZHBHiKzhJ24FpeiVsaDF8TtPwJcXKj0VMV+CHgu 
QyEkEjRbUzgwKXDTN2cXcUBxkXUNRMVgkMV1LqI+hB8wb4GOasmITom6xsh18STVMW1GAk5mwRc41Ec 
ET52KW/TPbvS8CvPCM59RlchhEW7xOV4xLSYw7LI9+LbtAhQfxfm5nRBAjOUEPHz6/NoaZYL4rN0JMTmsls3 
e8smiRFk2AUquoc6oNe3nXqPEHEPFg0tkP4WbguKNxzl+Y2eetlRZhm4oYt/fx7U+5TMQ5VE6GmyZuxtDCd 
AMbNr16ptwUeprgYEM3e72Sdaipb+UwpRlBM6nwl4AyIbc9nyzBC6KkHZUiCPVhutvhEecF+eRWJzyzH+x/N 
KykhuqSNzSgksmonhCTbJt9WMPovidOuHOBCLemaSzkdyzKpY4mPHCWDeDCMkxOayWzd7yyaJEWTYBSq 
6xv3cMXpIquI9t8Cb/dNfFPyoT0fR9EKDoUFROcvQeGuk8bnezqWZeTppnlKQnjCu4slwNp9vmXidyNFGjauK 
ykaL+6CHRo9TVx7B7pH6FRi4AW7vrqxgq3soNJlAmktvm2mVxl6JUuMzlj/nIBdlDa4dhc5VAhHepDvq81ihX 
OL8qE9H0fRCg6FBUTnL0HhrayLiebFOQwhABSy7WzuonyzeE+CpVCrIadKk9iUz/fTbebI+bAtcoUILw+5SgbC 
pFBRsuSNM46LCgMb9Bg6qkK+80qpT3nh3nvS+gQ+hZwhGJ2iVSKk04eGa18mXDRuRnTAb62hZfoR8jlDKXL 
HnY0f55OMI3fpWz8NvLajlgBIXZAQXIz/9glcfQkzF1lAtvmDdcq4M6ouZOrh3JgS6U+6CsxbmViJ8DRm9G0L 
W+CvMKBDbW011iKyb1B+lkNKThfiZIPBGHqEOJM7Mu+s493qcdeqPVhY6sd5/NmpHq4N62WeEkYTrWsM 
bbPvFwniQtujStY/Ksu/rEjIqCuaJ5q2Z/jcMVqGYVz7uDn9VHEhjWq3sbobL1llDJEa3a+4501GGS5QWMI4tOF 
mrRWB/bK3omx7H33KEuFs9f5KW8HVvptaF9DqODvn5p9wFap/SGyRnXfnwPnXJQuQvSzmZc8xXZKdpw7g 
SJq/PVICreNHgBbyPY6Qc8uPTPonsovAVzbFVID9/4x8Zl+ZH88d4gU3ms5pAv68T1SqwOXra89w8497SBC+ 
o8GEQYGf0zRd6N+c5lCPZX4KHXg2MiOzmqf3DJCzfXZKFKsK7SvsIwfU6udJtG/ZxrhQauJ7OkDv3FLzvQXkYSI 
cdQ49irlgqwFux+KP7mzES5nGj3MQ4cpGVbSjmCPIIz3rhBUNc2bMMc4TdyDPth7qgsrasXN5d8foi0sTnedfj 
N0lR+B3jFJzLOIs+NsIOFTnRvFYjAKiLvtUnn5do2oovvN5odGY5nK9V0yDykKvoee6MdwMZh64+mrV4Mu+2 
tb7PwfE+yKdU++OybKONWlhrcK7orC7CsD6atXgy77a1vs/B8T7Ip1Sf1WRWPwJf/ehzg/Q3NjxFrrPUbF8D9S 
gj2ExZ5SLYD1obEG5mLHOrWhG6uU56Fo9mq+EkythH2gfYvDE8FnKL9f113Sy3+ZaX97li1aWc/3EMRt0bTZt 
ZCYbiMoxSFdqGklENA0FqJ/JLlxoQ/Onz+XfoQ4aEZfKZxfg19QS4eqjEBICRPgl4VP3hv8Eod1AY7QHW86Wd4 
qNzBVZkXI6N9iDRrQLxUFLi2wLlnRFgeYq1yuiaVRQEnl8Os1j3bZz6vLJCBjFBLK4uRpe+nJX+M4naEGdtxS+kq 
fue0i2VekHXSUWeft0zI0HljWJokovAvos2BPeOMais39nwJxUMwOrpfOck1oEwcKJAnbrM6ranVpdLu2MJi 
AXBKjqhiYx7Jr6OrINmzXnQ0k3RUTXsMqFwvhTA9afR0nBeH+EGwWWi8fodGeKIyEkrPL9SNl34bPq5VHdb2 
JAPsz91ZzTjflKvLNmH2u+PGGnU2oMU22qoQ4LX18y9fEZgWvy+UvzTGsl0HhslF9wHG24efpqSMQgjXPYk 
m9uib/diB9XV5rygBXpXKPqXJR9cWaQmwPTJQRNiiDDfz9lxjO69OQtNFBJ8dQDygwudJO2q9nZl/N0CJYc51 
RhIBPhS1DP0QMHfjQdG7g+MwvA01GRNYpSEC/oqPNDZGZqHaIP5DxiGzTYzVElKERHJ8nOPdWC4hesG4V 
PwTgRmZk/iGevC1TavC5++wZ0M4KBtXdcPtjJ4+u6Wf41KRQ1go4A4QMJ7jgNgpLWAuK7L/t6De/PFHioMY 
fICONPllZh/wdgZToiOoahq1f3XT7RKxV4LcoYVewX/9L4+u774kAsQu+ri0gBci56Vrqlm1YP2rHopguBKHs6m 
EygTAjxvGdroSYooh2hqs91YPkcAEhNPkUpJ7esb7NHCacPvzbvmRUCB6vl7T5a7UVOvGjsmATnNjl8LNHDB 
AtcaclOKSJXNT9Bh5evjkpWYPcYMu26jHkx8W5MIeD3j1ROEqDVMzn8QeuW8lNZYdciA5rvSaxWtwoJWIkT 
V07l3xyBBw0ZSns4bXKfZz2yIEOMAe9CPl4sU6odo1qbYN+cvTcMp4PdWwOT4MVNk0j84Xr5XCyDhMi5bt
B58DEDurdTGnABC8AFlpI1fFcly5DdsPUEzhOnP3YGDo9zUPePVE4SoNUzOfxB65byU1rEmqttqJxS9gDekxjjI 
xVYKLhVNtBjJ6adQCgodLkfbtGdFw1lHmsLisBvMoSg1/lov7WJ+yY8K+doeBdM1WWyUAtCzVEkefaineY/U
TDxARdHEZIFrbBFTYu716JBAmpMuKO72SF3egAxHl/7PjKcmxro4VtOXu/P4CkjYkDQRI2dCSKcHhySilYcE73 
h4ckYaF6El/kv3GgWHYxs8YhxJZms6MHYSl+dQ8lg/oxEBTAEv71XkI99cPCTm3CfWlzMH5nBD3zJdT19Qa1V 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

71 



 

WbVJJFtx6oZgdVuIGzydv7CA2cI3Bmvs9MgU720/IHba/XwrX0ae+65b7o/VmN/f0fkaSdzl4jA9ooVps4CwDg 
hFTdnD2mYxSRf1FoQdHKPtVnF9moT3CQiOdAPL+jC1CLu9YD3e83PvYFV9W6Q1d5xMcX2ahPcJCI50A8v6 
MLUIu0KE4oAn1B2QteeCN5So6IhW6l3r6qkLiCNEEXmPWxP616lv/BpNFe/3uDrJZlr4IZkNJuUsj64vhayYYY 
Hbvgj0eXOz/QnndftMXTf/KLZ/fYnnTX5rY7pTTsFozGW3393sfu03cHa2YLjsIcuVx5lm/ItXaExwKhP9b/oaVX 
3maaMs1sHQm0znOrjIFSu+l32J501+a2O6U07BaMxlt9+CcykTauJL+MvsDzekVqOl84XVRR7pFyriijJjvNYKiS 
OBbPPTtYPmgZulZUDXaGYvwZjysl/whRaG7cCyXY47r0A5zga8BnlGNMAYBSwF5nUdXmlYIUYoibnvRDAyLv 
F9F8TexRh1Iom2drXIpEjUtuZFskwRaLrjGT/3Ly3EApIcd6ayf1FdE/mDQIE15UA5ZVdkzXu/zi268XmoIDpuqC 
p+1j1ifp2zBwiJdGBs2DllV2TNe7/OLbrxeaggOm5tw4KZVtCrxp7IAzK+kO2xfZ1ybywtsT7V2GizI3m+wGq4J 
W+UbbovRRZUfJxYfmOYuapEsZDeJuZiXIa2JlVYCr3EvCJjYEe5nemGJyB8MYOsGmbyhdJaSWQDi99qMOYgA 
Vy9sD5S4RrscHD5vZG9OWVXZM17v84tuvF5qCA6bhMhTIBirR7g66sFFDpTvrgSi8N65eqSm5fJNgYiR3cJon 
YKb2urvkAaoZpxraPSwa+Lr2BK2fCrS5b7Jmndnf8abSDFUH3RdpeHuW3maehRHJ+wZac47Gjhj3yxePNbdE 
pMGHhyNMnfOIFxoshCbUEpScbztXt6FzuqEH0UsUOH8bLjfEOk2LseWswn3OuHtLY9K7VApM/Ddj0f2Pgi0v 
", 

"EncryptionKey": 
"heevc+FOSSf4CBwmkkvU11KFXljS3u+mxMbPewez/8QU30hVo96db44KMRYPuENvNYGCWGqJSdltNQxB 
/k3gA3eBkDtCYmgYn0ptAUiGY/N5VSID13U6zjqWLmWcDLfgitzaguPmcjSGyVTQ99aNNVBvmxx+Jq54V2Pa 
2cCUODee3l7Efz3xMIZ3PqXKW3Dzb1UFPoyZROdekXa9U7NGDsSssPNL+6KLDLf+WqgZQnOb+x/wnHkBV3 
GQtR00edh6Wyioo5Rf7VZs69+L2CNAcu9ERw621cIrh+GNxaOcbQE67bjPorGkSojW5GLBFvoGByeC5ojViPZ 
wsyzfkCHfJ0BIZyMaKYL7Dsh/1Sb0qDlKDW0OlJNHCh7vrzyDLscfoxVULVYoPItJ9VSiXa3GQThtBNLTfElqGsg 
GK2V/KM7C6rnc+pnaNZVC7WgzsJczYzcB9EUvfK1US2aaKokTKvjLg3KyPSHaS0MkXfPePzfD0yrqfo+jgXagQ 
yik+jQUj6bXF03/roYZBPhO6XI2mK5dmfH/JmIYHgd5XDwXlwCxhAshwtLjRgqwE4JLHW+an8JzL3kn7dmNRf 
XTuvyrYr0yvPGg0ClqlhDLgxPjSw7NAt1aR+fvrtsFihJ8XeqNWCbt3TncYTISv0AHL0SurLSTpaJUY0FmQvjK6ol 
BA1W/l5pDacmJrt7qlLr75TEvLOb5AFDuhAtM3z44mrytLLbbI/miq1OeZRQB/j9wfUXzWZ9DGUGrzg95g8Q 
QyGYHWPM9wYODF6kvS5bYrSmcgBqMBATR59yosdPOf3mAeO+Ap274jNWfGmXw+XMsrTUGZOrSQ+yTj 
tbhVNAewwTECYtc+iKF62Z1zAZInCV05N6YhDQp6A6i3uxUYurV3yDSWrj6Wkw2azLAWyB/1zbemZbeJ4YQ 
9sjcNaDpT64obrTwLRbsz+yBetX9chXduYcMDkN3kD4HnkTuGpba49uBCzYxp0N5NonzMa7UoYVv6L/SnKlj 
OueyZEph5V8YrTFVEGL05hoVORWFDOq/oAA1FjdkeQojFXiwwFOuPPK5M0VBbfp0P9Z62BldXdbHZQHQH 
DaySEPck9ln3HG5uDvzX7606eYOBMl0NfmrR80+RzALKJgwCckAgHx1V/gM9yPtoMgonSgBTxG8dElAsfzvE 
H/Q7GqHLEpxFskK2M21R66IKTQlfv1/dv+/MS8G2FbSuvB6rG/4/3H/Au+Qff4ZaNDcrzaHvASaXWYQ/7F8si 
kZ96a10rhEsV1xISK75S0XmgUbwYXDx8zxqQ1Jsgk+iJp0fX8A2JMZcIt8TZg1i0aNRctZWIsSUNbtKMZoHKOX 
4WRsb4je15h1E0148P0VhXSZrqlCel84dHttruaQKtMQTaM8GV5xjUHw3Ij7mYeLLdaeaa0QOLZY28SShkh2 
Q4RIHxUkeG41vy1Rv2HLxox2QuZCeRW+tjesCfA2bfaBoDm1wQYET+tCFpuMtbxNs9p4EiNjp0rQ4UyRRtd6 
ej4UahOqjdDQfMYltUS2wdzt7WQfyOQ2ISQPj1o8ikW8hA6L5C6uTojIbO9a4O6ilqldvNSaWag7d6+cDxebIn 
wMgfSqstcH/ixoC5XH3MhaWRHxRkUW2n0jW90LGbKu2YYDBYW8/hS1UaEfqzRzgnIH7+1iOlD+vrC3rnwgf 
+q4B8+MAtT3111hPArQ5lbnRxmEnKwhpn/hxPGYBeXxNJIXJ8HQV2McIdlLcjcOS50bR0vJusj+Ddk8zrM2qk 
Y7vQ/gErcvkI2tAoU6X+Fj8Qp7FlhNbCVZ/jdbJLcVfU01b8R1Kqw7zj4XThEJGx4UEOnOMoEVN7vlX5uOH3p 
GxzfPBZCGldNFojSjDRSU9GPPDWWpEyemjl7jy83455Dfr5ZXEoouJNDwtsJScORyzNijX0ibOgSLzysRbSII/EB 
wkE3VrHIe07zuwK+XsrjmmLZrHqErhJyswr5IwGpyxSXJFKCueuFXGwcbuNIpKiINsTDNlPbYlS5pr7iSFN3KofD 
r8NrSBwtoXrlmnJxEfxseF2NdqDGBODjAcLaBiQbzrEvkyoMPV9JHMlqHBgz8ZGiGwREflHxg16dx4gKnUdQys 
Sy94zyChY7RUw1GM8Tsm0E1+GCAaDXKQ8Mnl/slE3jKgl1Uat6SXpY6mW0wx6pqwN1y/lvMwzjs856uxXD 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
72 



 

qLDAnUXgJkK+T2E2Y0CoNVLP1L3HfYPmvYFa9jM/IRxoHxz8xEmTFhxtPE42R8R0jntEXznTEaIr6E52vcN+mFI 
XAm3+4hrmevb+R7/0CUDmo7sJvGcfy1ihB3QZJsZN/IxTNRaTz2y60/HBMw+eI9MqYBm6VPpnDrXtzoRzax 
nPEGcZjgbwuIJnemTPEYIl5pkRzKWwYc2+eTqhRB9KA0irvqyIij6bYDtfhFOgNiF7B0h88nVA7LcYB85hiHtZaEr 
YZMZB3SmrcCaWLWbYeBeXD6ta2jOg7GXnG+7Ah/vhQwkNWvZQ6i64KJuByCpyIVw34b49/G3yiS7KALTrp 
GZ+hpkylh9p4rtdYO76nGsFUfqFRGTFmzdhs0xZNWb+lOjFJR/lVCJ3SLgt7yor21vA5e1WRpDwm/y/Dmhnoz 
vVDGbM5Y461vD7y1Coti+AntKIQ9aX+hpuBjZeorg28HyW/7MhZgdFxQurKayTczTuIWfJSZaVDfMwwqY6v 
OOTDDND/p/GCovegukbCeiY/fXk=", 

"Signature": 
"qYCNmwVm3UprXwNemK67r8ImjzlS1O8Rp7QYqRy/at9E2EjVEq9SDcyMCXkzTr3z8TYkDemvs8tNxPIIzOF 
b+1ZKvCVkosqo63MOUb4bZPRNVb4ZuaTKpsA7WILuuDUQxoOVEzo9zgEzXOrk3+MW+uaQqp45XhG7Uz 
meEOyjz6dOlWLj7JcxZWszkEbMh/1ELZSb2G/BtADgE41YxwJYZC8cnh2Q1D3DDm82nwQl8D0BsRm7mc5Y 
DZClef40PyGrXr42nwx3+7NOFbrxLHww8fUmhzsogqCueETGBVo0nwXUQTJakr2uqkZlgwY8F8yj5kFVUbYn 
n6qvzKDzxfSww3DfuJz3y0Ex16mCrft+7RTSDyWk4zYHgVaD+qTO+v7GSFmpY/i9UDHHWSpzttlflf+1hY5o05 
pe9slKnmiodBIV2jst0g++LAGGR3zVSXdwfGKIepougVAe1/IcDcM+TeOvkrjqWevBOeqJzJg6hAH5Sz+Rz4iEa 
DhDF6z5Uh1p4sxiHEg4P/mUG7kSAH0wv0R7qomzshsIEnSH4xiLl3YZmugzaVLQ0auHToXxOOZo/eqXbtev6 
D2sQogJKtDGN6JwTRXzPFdI7r0vpPKmJAXMue7aIm+fUwqjCCE8Gp/csKZLkBO5xVqrEzfYsIhBNmQv6YzYX9 
9KuaWBpSaiL39EOZBOGZtXsEzF/mU/GOHtro3v3MZkS8vHjoSuyRLUEWV3IJ5DP/nRgi+daWyWzpGCfs6w3 
xz79YcCJXyS/WFXkciGD4vUfMC/Ve4n7VBfLgq6pIKj43fLIADlHjdBRnNjgqFySZPMDuY35snVTdQR/QaCOCQ 
qspXZbsnPkMC1/mW7WswamfVLadV0LPNSW0gDXorWtYAUjXxHvTvHCROENXdieDVtnzESa5h4HLu0nyaP 
y88AeeMeGWcWc9ZIVYf5n9mRpu5gH1iax1h+WkS5olwSfuANOkqIOMxoQD/Gmyqn7Tb4TrP3vwxbKBnw 
Tlqj86ooH5yt3HNG8x+56pM3sP1dcXZmqZ25pwPylz58D/RRZAxZ71iiKR83BMOshFawai9Xv1BngJ0sG8h+rI 
S1n+XnBHDFXKUbYegbZh3g1bv1BdO8DHK+iSTCXmeK1rZVpNRwrrwvQS31iTnIRp90HQVlveqj51MhPHHul 
X8m2KRbU9rtT6AucqQhnIP2ymwHXnwFkc+Odbs9ZJMdVuf3f0ARjBBUCcn2wWFYXlKVrcL5erc9B9PQrF2b 
Ue6gNlI+CA0TI3RLFz5wScl0/IcVh75xkgWBFb0Qw8pq3fWTixmJE4EX3efUXS+zWzz7Sm/ve7f+vSbWLNOcT 
BoXExi8Ean4FLXRVGYuKGgOEmWe4MiicAsSi3NjQMOYxe5JDN9J64KXap0bQodeKYGEHW/bsStvv6A22v0f 
8kmc/nVPvjucHXtk0T/KVc2iupnxd84x7AOvSOzGIm09e8T+AHTl/YVqlMByAMVx+yhmzKn2MpH+bp3T5UG 
8CFFqgVK2yTsKEYHgirMb2zeuHwgvR9XYuVqozqjOlAYeMIOCCJIV+aS7xviOWWOOUj4Z1DRU4IG4qIqqANl 
MBO4DQmYeG/kvIjSn8nP912VZy/wqbyDfIB+dL57JFEsYSM1gUslZWz7Pndm768NJDoytbqLzUTa4q6gQM 
O6Bst4M4NYMX6mzVUe6y2vhL1XomqQMTq1Tii0ozjWcjZHbmY7Y/pwE52aR+Kqw6M7bxXQHyr3tCr2kY2 
m+rzgRMu2n6nO0c4Tq/ohbivthwTU1GVbcj5wmdYq8RuDTUuqH4SfWsQE8Rc69dPwa9SSCnqArVU0Ukss 
dr55u2IaFFjZZ0aD529TgmBoPHOHvMIluRzAVnVYJSF1DhXNHERulb9U1BEL4VH5Ezo5pQN6JZgLlfi3juDnLX 
/Vl7pesI5MJHIU1KdHwP7pn7Vp/ZG5+jpuj2vXKQtc828VG/g3FwEqrcyQuqPzyMNeSZ4IbzUOOwJtuiKoVhO 
XkugXpVKNnv4auPkgNfz2wPEq0CdrbhQEiFTNpf4YD6Jr0xDwUEUpehIr3xizJOG2QyFzt4LWxt5wcyNUqkhQ 
fYNHLn+PvY7Qa8XEv0MPQpdTX9YdBvDT9ObQFGrl2G5HlcC+hcGI5Xg3YA8Pri6qMl5tLZR2Ozyr1AOB55W 
7UfEnY1ZIuEFfLiZX9qEfoQl9bFJB2ECDSzej/nIaBhvu/wsDN4AnhUxGOaoldI3xOzvKzlkp/q8iTb/kqpjjereBeIh 
gMHY6Rfrs5u65gmFR57elcwVeZSOYfs0oFhulpkyNpiveoCuAJ8UggpVPTYERZcWsZlVr2+HmL7UEiBMq01lk 
Ph+5765xGzuFOSf9kGXqTQUd+KYROyU+HFxYZ4e5QY+T0NC+ogJnQvu2YqvtjMDJ5jbuCuMqeEsSh3gM3u 
3qfW/bSw7+eK6Rw841XPZdLwxl9YRWuopcxXBOTXNVPg29kVm8MQ6Pa33N9k7Ya+kQYNVJ16QmJyN4n 
CkzPs4N29kkRr9X3jrCylRBfWUIDjT1avxQyJDsyT/xsKrEi8n3qwCnwV9W7y3EH0Ne4iTEYQCg0twgntsAWYh 
UWWm1yPr9p+P6JZOyEFzJcSZMVlqAAd/EnLRa5Ve5xKHWhyO778/w8/yFs88NMFWoFrZi1Ej6GLyaOgMt3 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
73 



 

g+cMMtf0iXYLqpLuSraieeguFiTFkKPGwRaqpGX91qktXjd1NigLN3w=" 
 

 
5.1.2. Sample Encrypted Response 

"EncryptedData": 
"JnurJtyWpzvSzBgCcVzX6KhAMHXlgqB0WrA85OVciMJNIRx2S25URNaWujRQYbnHQgg57RnQaSRWEIN8q 
FX/W+r0lS41g8dzTd92VDmShQoDQU/pq6vD9mxkkRWEG/seSvNkFJiZpcDrOXFqDTy++3p25OSF/0WUpQc 
+iS2/po1qjnNvN3Uw5k+onZTV8f3DrpeKWqoqg4TiVlgV51dHrhdTTGgAGBDPTmsBo+2Onmxmc6oeJQu7jC 
U3HOpFw6yjPQIDgf4ogVfmdfqKnojEsyNMUGxS6zam9fqTI24AQ4Ox3Ni8IyI+PtXttn8JgKr4+ajKxrdmZuUQ 
+0/GsuEBzIOdaiwaLSWefqaFSiS+BdBCINrW7ZzIhGMOdRbLXqMx38cP8b71jUKhgWbkd3Gcxclxd5Ueepb/y 
baMbUgZwuZpIpJPmaMbZpTMMjCLSh/c3oEERygOCXLbr4e66srMs1vBL77QG3hhf8idO9xW545FpUGU/j4 
48ca8+4ykSsI8tNvIu500xT2mpiC1cij257Qfqc9lM12r7TRYcPPJr5G466+Owl56fWPpdopCZjIwOX+Q1kF7Ay 
dF9avPVZzRnERjUYYJx4C7i29jjGXLLm6crEq+hQX8lEfkYyiGwNP0UUEDVY2cxOIaoLI1noz2sYga5Cfg+JpkIvc 
KPIYYruus+7VQgNA14vzTN50BB8RpKbAp/zTBDSQ0lXnW+iYLFvVfSDk5tNlMnWlGFPraG6VYMvqPN8oG1v 
CzKTAngaj6WOa1JVBuSmvvOVqHiQSJdA==", 

"EncryptionKey": 
"GB9XHuWfTdyz/f6yhrKLQg6oO20lFlpsGa64DKO5rbAzvQXB4ArKdqvR5WI4VR9CT+hHzyqvCSNyWZY6no2 
7xWsywlATvWku0tNsW7dwFurr188jIj68RIXsmfYfMjFbeBwWV4cwlDvnJXEfaTYs+Q6t5cIINXk0ZhYx/656pd 
OZHsleX0ghdCcyLdYBoe/xzrrvPNK2djIApkEN5tQc58BxOD5HSlt3rTzSLhv4vWi4OlK2jeXk2uzH/ffK3WAmR7 
CojnEqXw59AHrXnt00syT1XkCVJX/yI4YXEis+fQoU3PqnoexlG+APh1ZpD9DhqbhdmmukHA/zLuhuuQCFW0 
3d7sjXxJymJdz4eQux610T65DTGN0quf2mLcuioNrLrEAlE+t0rlTUwPnEBoKsl5BCtWGaC7ZRs89e5yd9P8Rgy 
cvWJ4XDy3gRGtd4ZR7C/MrdP13xb3Kd90XMHj1s/n6YTEfUZ841rL/0opJwqQTD8Mq2gxkb5ApPneKQo8yU 
UD2wZG0OthKlWQdJPh/UNDWaJW1kZG6GkKF1edGHg9+Z4ATFmFNpuY1lD1bFiOKd46xLlBFtGqdyCXFOH 
ni1cTc+pr9SB9kzpmyj+A57wtASsFK+R3pvy59GOy55q8vDF2EmTqsNpVbI2I8EYgHl04jVK95fBfrcHIEOLofzx 
aaZlskugw2XBGzpzGLbfaGIOhXemHcPyhE88WjJ2t/9hiXTCRRASDiWhHFFh+0f3pzfy9uaGidrI7O1GAf5t0PH 
m5I/Yc3T0/FPkK1Nfp44LS0hgHPJ91Q47m0iVsMFPlZFOcSMFRezV6+lLRjHxLxlkV/5llsdjvxJI/1qpAAPwUUZn 
PfLfzlsyIyRvfGYoiEkb5GQTCzwoRO/tddaGPJxVc6vSSpHMSKSUMpw3GiFsawuPpXI6cwlOEKWBGAL0rKqgx 
nakOyF5+a9UfZHtUYsBQhZG28cs3LO5kuWZafuB5Vcu7RemSzKY92xQwkuQicVU87Hmhq/juYa09CaxCEHo 
b4Ojh0aUjkimsfOsjhnGa+7UkSLZDig4n2yMLxfnkvf80Kmn65gSffAsGJloy3EW7tb4AWEiG5k4uYh+VVOYov 
1foo7yuUcnXFJbXnrUYc/65RrSBdhqerCyJmzxaT4s17kV0uxUWBU6T+EQ9cfRaNub5+kxoSJWUWqoMjDEB 
mgQNx5ezeOccQfNpMHPe4M34oh2p6ccsSdjHAudS0Z83Wh9tPUCoh8vPnvUFUYNOQqnNIB0Z3yUWoVsl 
ERaAvVjhzpxNV2GGJHP8dkSMjtZ5a+TSrBh3wcwNW/DdW4KBLm4+hH0vi7Wn/dxH7VXeLAunfGviILdhDJU 
M2V7UO3/JVHFVH9lNDGwqpvrvSPOeJjWofKdhK9+JEQVLVa42QtK19JWZGc54S1cWjxNXgDFuVAyborI5B 
C/1Jpo3O0ckTOMN/OrOuxfpnZnAL+WPDHd6J9c7Ev5TieVjVBs6hUtfhUjYdmMgPZZktYEcmxsGyMIdB+wC 
qOfVkYkuZyn0TjFWN2XhpVLDBKW5eGy4CJeoy1+V5RIjbIzB2jUPib9JqbOANBzPlQQfRCV5YjVhfaEpLaB1gk 
JTkHCldj8NXIUZgfK+6/d9J+n+cdCF6IhL8vn7j5w6JACfXteG3ff4Kz6yJiIGaCOfMeIkR7o0z6bi1NndybwK4cwV 
3pR2PxXOJnkD8OTM2nXayicMVzQajyGhVqS3qC95zTtsa1JVRrdCujOawE185tafU6NpZ/1qOz5cOeaZFH3hc
SWUtveKYjp1Vsl3ZJ8tbQtlR5IRoLpDQc8p+leYNIFdpl7MtmKNnrMx3/uW+azts+V8x5+5Y3xygjzEXyIyez9gd 
9okrx+zKYicTq4X6De1R8VL7kVpgEXtrguO9YaPV/AplJZ6tfhPkYCmk9RG7SAWunWAJ8paNv+n0Rn4eRQylA 
NPvyqxpmEwA4Dl5G9W35+MsF2ect16bODAh4Rvs2YwR3OfVdF4v+PNaqvBgwY+fY9KymKdczwaewlfA6vS 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
74 



 

dqLaJ4IE0+ypmC2+DhsoSdhWNuQ6iGUH0PCzvo0y4qn2v+YAB/VHggYTZECg3qGsXZl2hb0Z63OPKL6IEbak 
OqFIbFCsyI9kQKwcFrU89Hhcs6f2sEfuY4YutdlH0sHuQnY+jWzkuGfvc12ItW95DL1AetY3NzXYzV+gl3rODD+ 
GUycJ4lrJy17BMJ0JdGRdDfopnJiqJkcDGWgwu/s/hGtcYe7oKcTGj8bh3Ipqiwlnlrew3Xq5w8LRPMyJvGwTEp 
TgILi1U/oVdXvSfbz+vgyZ4Edip4v7A24Om8iVOvXdOn7CeJ7J2oNene9MtiA2sZ1wMIK2jFItpAIElEyr3pywLrj 
dJHX1rzViSQ8117B8omGCb5ugiLtpaBx1MCGHBfw4qBK/SND28vZ6JctZ/lcpXntm7rUgUU2qsTdV4ln6XEG2 
7j2NLW46uryM+wkoU+4gcZU+ZIwCtsUFImpHj2EL1TSgvWPUQw/0YDRWrNQbStg6dlQQA7CiDCTLDD/ZKE 
LQ5WrE6JSwgO6CLhfsnw3nFFvPu7W3t0IucupBSWjN1tO5JLGIVrKTNN7hyORHC1ry/3a+v+GOGpmzHL/dq 
9rLytC1yJg+JZRfK+eqorZlpbKFyoWjU+iik8yGZQ115g4n9DJQ2kR+oLfuHWAoyudrWQmuFd4A8dcizaw5OsB 
NQIhyy+/cQ=", 

"Signature": 
"HTItYvIPL2bvYhP4zyO/BJhHBRu1hS9Yigj2CUBoS2aysyiXFePGIyGB1ZoTerjJYerg/R3k5Xj9U/hl9JC76kAuey
jkGae3a7GT2qIXha3CSuwdYjlVSscFw9yVbSCGEIgn6eB7epUC3aKwwO3Vjaf9GEbogh/DBxBmju7CrsrZRHy
iX5Y3EiWWh6aUvu97W/+DnVnlEdMKS8rTF6SM1i/Ku5caXOI3cgGKzGW+omUOOKaTi9bRalYfVQOjQRDl
MBds0FAAolBjPUMta+uSr675yuY/T1A/xRhna2AZQbvMqQ/E92EM6K4pEqBQDaGb4l/Q5cowjsIVY+23Vu
Dd6gB8GlY7hGmxxw9jtpSQIR7UUx+wTaAqR84nLH7nSroqDbUGJkJX1/P8jk7MJfvAA0Jgh4ri9MPb9Z+WC
+I8T80wtjUT1N4cUVeMVpYZS1hLr+AhjKJtZn5y7jAzNAAeN/vIcYRUcVrgmvVTUe5RpBiU5WchLdxCRhqzM
+S0wzvja85Nb1nHu8+0gboXUThK64h/sSTiC+ez6dbPYjlknW2pb+UUbMmGFe1JZ7M0Y7Z/pXkaHKgepx8
bqbKEltZaOu0fKRU0FxoSw0E9J4DhHZHRRnwPd7HwZPMbDBKUM6sb175ivxF1rUdHLcr95I1gGYRms9faE
VBuuAjtaBs88IoDyBszXobo9iFahmHVvu0jLy5/AZjXQLPadAH+5atzrwE9S3+6WZm4GJ1UYP5J35rFInb9ze9
KyQH3dxhP3GPFZC2kKuP1Fj+kGkJF3cFkh5TUWAuiwlfWIBLqWnqXVCYYGWIqyBlnljoYAX7+DBjMI9qNxh
bSOqeb2gGMdocNaesd90+ZxKCS/hLqQU94L7n+JMwaZI7Ofh2itcHERYj/AykK9wiMipzW3+AY2J51qiWi/K
UrEvp757NSRgPc1oDLII+qYIuNxnCki9ecFc5EEfZocI/lyf4fWIhg9SlDwJxzQoHCkWUIf7UNa2t3grkECjSq/Ns
HVKwH9EzfsVY2bQC+pMJORPFV9ZlgGsYNzzE+LbFZdt+FUrGXLnZChl+98X6q+feJ0XFf/v17iRSirB5aLThwq
n6UDoOr8SWQM6EIyAMelbfipXzfTOK84P76Co+XwFhXakv3bRhoQuhKWLoj85vEgtoPM1dFNkdlhUU4vF
rlNokueCIRwuKuCkevurod55XCp4tqzmFIoSyRKaX4KiEKTnx1YPbC6Apz8n5sGOAwOL78hMSTXGyNMo26
Dd/y9Gzb0o7B49tB4XzLI/a/8di7pp/rf3u0pM5WNBXyQXasE3zk7O4Rd1eN5Dn+X/8c/imi4RyTGSd61GjRe
QhZcY6vMEUMZ4EERNw1aY37v1p2r3+1TZOBZ71pv50HrfJhKTpteTwk2YbBKmTz57bpxLj3Jf/i03fr0MRIN
GN59/2NTfzTq62VyrEhfPR2CtQN0uoUTYk/rAyHbU9dihE3fGhEdZ+UdSh5qsO7w9NxUblvOYAik4OdoZRV
Ny4MPtzlyJGiXiW3Wk+fR1kAf4wP54ulC/32KNPyutB2iVYkutpMlX2mbuYFZgAkQzbWrFQtV+X+E8YGDYb
NmFx5fm0PuFTdGCmE0tbY5xfXiTPJX+/u621BZ0q3cOTN9EQB7cSU9nWAfx3t+qH/pX/znHr1d9r+VIJqIzlf0
8Sy2Mef/oAT3iMWJjrVd6gBW9wTwncV1riUeYca2P0pA58SrP5Ha3O7NmsziGGCt8T2tHjGL5sNg8oxyQQL
z8ftwrdzltAkU+z7pAYZpyaU+YjsgfYompqwvC4O2cRY32EXQwYhABF6PbBwIPRhaB9EPdLJOW78kftV/U02
Ji7Zr3fHgYDEXdxwVfVHycZc4oG6cGq1l4/BYzfkU2BCZ9pJofA0EgxsP5secjh2Um+MppzKRKM1vdh+LqcRb
mwREJZpHS0MPPnJMKJGlb/XfLr8caLlIIxTQsotxYAS2+pY+NoenbcsuT9XASfkHsmU+eZt1O30Cnp43itN+PC
CsQBosfvd5Fgd3IiiR4QiwMpilPgMThbjH1xDVJKZ4rQf1OJzHCnHKToNOyJJhgPVvOaRJDnd4h+iiIIfbKxl25y
3iFQ007GNUxxsv0OBWNl6UeC8uSygu9qR22j+WJaE8f+TO6DmtT1FoPA3JmSEPzE9HFrENuqOU6MDV8T
4eiWl/DnLt094KPJ0sZ/U0yaLOjSb546zFeEE7bnXe+ZW3fldnuDDVU9DH+o0XXRj9R6c76hIc3o2A3pWxlJ3z
Kr1uKUTvQa7gKBB6jZXaNox3XKh6t1ymb3yZH1JDKuZqYjUTIFkVzi1oPuiHU2LdvguaNGjRSl/HtRcW5QA75uQA0V
e+tO7vM3crutMRgKh6nuciD2RBYRkmt5nW/SGwKq7GO/ePubJpBbifRrVzzbQsg62tfR0rWSUWSh/eNDXGUB2UWo
RzyoWATEyw522taIYQ+joyw6TvCvm74oy+tYk+Vt10AwraclTaHrQBkaqyGl1JBrQhIb7wFi81I7HpuTvs/HIKAtcyjeUlO
yxfarX2AFX95XvqoFteGN3Ohc/dqswVaxKkZu59cRQOmkSah12lylzZVTEvb2PlRzHBeiSdNOonLvFkYGzKTQE0vJZnFnR
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

75 



 

oWuGf1IzvsGD8hszmiWGrnrwmpQqau3dpWiG0F3Z9DeQNO59VW6naYeffeV/IXRLdG9imD0cm1qZ19idnWZ/MI+
Fto=" 

 
5.2. Decryption 

Following are the steps to decrypt and read the response: 
1. Verify the signature in Response using the Public Key of the Sender. 
2. Decode the Session Key from the Response. 
3. Decrypt the above Session Key by Receiver’s Private Key using RSA (Asymmetric Algorithm) 
4. Decode the data in Response. 
5. Decrypt the data by Session Key (derived from above) using AES (Symmetric Algorithm). 

  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
76 



 

6. Enumerations: 
6.1 Segment Enum 

 

  
  
Code Name 

  
1 Retail 

  
2 Wholesale 

  
3 Institutional 

  
4 PrivateBanking 

  
99 Categorized 

 
6.2    Customer and RelatedParty Status Enum 

 
  
Code Customer Status 

  
Active Active 

  
Closed Closed 

  
Dormant Dormant 

  
InActive 

InActive 
  
Suspended Suspended 

 

 

 

 

 

 

 

 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

77 



 

6.3    Constitution Type Enum 

 
  
Code Name 

  
1 Individual 

  
2 Hindu Undivided Family 
  
5 Non Resident Indian 
  
7 Foreign National 
  
8 Mutual Fund 
  
9 Trust 
  
10 Public Sector Banks 
  
11 Limited Liability Partnership 
  
13 Foreign portfolio investment Ind 
  
14 Foreign Portfolio Investors LE 
  
17 Partnership Firm 
  
18 Association of Persons (AOP)/ Body of Individuals (BOI) 
  
25 Not Specified but Ind 
  
26 Society 
  
27 Person of Indian Origin 
  
29 Public Limited Company 
  
30 Private Limited Company 
  
81 Scheduled Cooperative Bank 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
78 



 

  
83 Venture Capital Fund 
  
101 Sole Proprietorship 
  
102 Section 8 Company India 
  
103 Foreign Embassy or Counselor Office 
  
104 International Organization or Agency 
  
58 Central/State Government Departments/Agency 
  
59 Non Profit Organization 

  
60 Not Specified but LE 
  
68 Liquidator 
  
69 Artificial Juridical Person 
  
75 Foreign Bank 
  
78 Private Sector Bank 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
79 



 

 
6.4    Gender Enum 

                          
  
Code Name 

  
01 Male 

  
02 Female 

  
03 Transgender 

 
6.5    Marital Status Enum 

                              
  
Code Name 

  
M Married 

  
U UnMarried 

  
O Others 

  
W Widow / Widower 

  
SP Separated 

  

D Divorcee 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
80 



 

 
6.6    Country Enum 

                         
  
Iso3DigitCode Name 

  
AFG Afghanistan 

  
ALA Aland Islands 

  
ALB Albania 

  
DZA Algeria 

  
ASM American Samoa 

  
AND Andorra 

  
AGO Angola 

  
AIA Anguilla 

  
ATA Antarctica 

  
ATG Antigua and Barbuda 

  
ARG Argentina 

  
ARM Armenia 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
81 



 

  
ABW Aruba 

  
AUS Australia 

  
AUT Austria 

  
AZE Azerbaijan 

  
BHS Bahamas 

  
BHR Bahrain 

  
BGD Bangladesh 

  
BRB Barbados 

  
BLR Belarus 

  
BEL Belgium 

  
BLZ Belize 

  
BEN Benin 

  
BMU Bermuda 

  
BTN Bhutan 

  
BOL Bolivia 

  
BES Bonaire, Sint Eustatius and Saba 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
82 



 

  
BIH Bosnia and Herzegovina 

  
BWA Botswana 

  
BVT Bouvet Island 

  
BRA Brazil 

  
IOT British Indian Ocean Territory 

  
BRN Brunei Darussalam 

  
BGR Bulgaria 

  
BFA Burkina Faso 

  
BDI Burundi 

  
CPV Cape Verde 

  
KHM Cambodia 

  
CMR Cameroon 

  
CAN Canada 

  
CYM Cayman Islands 

  
CAF Central African Republic 

  
TCD Chad 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
83 



 

   
CHL Chile 

  
CHN China 

  
CXR Christmas Island 

  
CCK Cocos Keeling Islands 

  
COL Colombia 

  
COM Comoros 

  
COD The Democratic Republic of the Congo 

  
COG The Republic of the Congo 

  
COK Cook Islands 

  
CRI Costa Rica 

  
HRV Croatia 

  
CUB Cuba 

  
CUW Curacao 

  
CYP Cyprus 

  
CZE Czech Republic 

  
CIV Ivory Coast 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
84 



 

  
DNK Denmark 

  
DJI Djibouti 

  
DMA Dominica 

  
DOM Dominican Republic 

  
ECU Ecuador 

  
EGY Egypt 

  
SLV El Salvador 

  
GNQ Equatorial Guinea 

  
ERI Eritrea 

  
EST Estonia 

  
SWZ Eswatini 

  
ETH Ethiopia 

  
FLK Falkland Islands 

  
FRO Faroe Islands 

  
FJI Fiji 

  
FIN Finland 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
85 



 

  
FRA France 

  
GUF French Guiana 

  
PYF French Polynesia 

  
ATF French Southern Territories 

  
GAB Gabon 

  
GMB Gambia 

  
GEO Georgia 

  
DEU Germany 

  
GHA Ghana 

  
GIB Gibraltar 

  
GRC Greece 

  
GRL Greenland 

  
GRD Grenada 

  
GLP Guadeloupe 

  
GUM Guam 

  
GTM Guatemala 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
86 



 

   
GGY Guernsey 

  
GIN Guinea 

  
GNB Guinea-Bissau 

  
GUY Guyana 

  
HTI Haiti 

  
HMD Heard Island and McDonald Islands 

  
VAT Holy See 

  
HND Honduras 

  
HKG Hong Kong 

  
HUN Hungary 

  
ISL Iceland 

  
IND India 

  
IDN Indonesia 

  
IRN Iran 

  
IRQ Iraq 

  
IRL Ireland 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
87 



 

  
IMN Isle of Man 

  
ISR Israel 

  
ITA Italy 

  
JAM Jamaica 

  
JPN Japan 

  
JEY Jersey 

  
JOR Jordan 

  
KAZ Kazakhstan 

  
KEN Kenya 

  
KIR Kiribati 

  
PRK North Korea 

  
KOR South Korea 

  
KWT Kuwait 

  
KGZ Kyrgyzstan 

  
LAO Lao People's Democratic Republic 

  
LVA Latvia 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
88 



 

  
LBN Lebanon 

  
LSO Lesotho 

  
LBR Liberia 

  
LBY Libya 

  
LIE Liechtenstein 

  
LTU Lithuania 

  
LUX Luxembourg 

  
MAC Macao 

  
MDG Madagascar 

  
MWI Malawi 

  
MYS Malaysia 

  
MDV Maldives 

  
MLI Mali 

  
MLT Malta 

  
MHL Marshall Islands 

  
MTQ Martinique 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
89 



 

  
MRT Mauritania 

  
MUS Mauritius 

  
MYT Mayotte 

  
MEX Mexico 

  
FSM Micronesia 

  
MDA Moldova 

  
MCO Monaco 

  
MNG Mongolia 

  
MNE Montenegro 

  
MSR Montserrat 

  
MAR Morocco 

  
MOZ Mozambique 

  
MMR Myanmar 

  
NAM Namibia 

  
NRU Nauru 

  
NPL Nepal 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
90 



 

  
NLD Netherlands 

  
NCL New Caledonia 

  
NZL New Zealand 

  
NIC Nicaragua 

  
NER Niger 

  
NGA Nigeria 

  
NIU Niue 

  
NFK Norfolk Island 

  
MKD North Macedonia 

  
MNP Northern Mariana Islands 

  
ANT Netherlands Antilles 

  
NOR Norway 

  
OMN Oman 

  
PAK Pakistan 

  
PLW Palau 

  
PSE Palestine 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
91 



 

  
PAN Panama 

  
PNG Papua New Guinea 

  
PRY Paraguay 

  
PER Peru 

  
PHL Philippines 

  
PCN Pitcairn 

  
POL Poland 

  
PRT Portugal 

  
PRI Puerto Rico 

  
QAT Qatar 

  
ROU Romania 

  
RUS Russian Federation 

  
RWA Rwanda 

  
REU Reunion 

  
BLM Saint Barthelemy 

  
SHN Saint Helena, Ascension and Tristan da Cunha 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
92 



 

  
KNA Saint Kitts and Nevis 

  
LCA Saint Lucia 

  
MAF Saint Martin 

  
SPM Saint Pierre and Miquelon 

  
VCT Saint Vincent and the Grenadines 

  
WSM Samoa 

  
SMR San Marino 

  
STP Sao Tome and Principe 

  
SAU Saudi Arabia 

  
SEN Senegal 

  
SRB Serbia 

  
SYC Seychelles 

  
SLE Sierra Leone 

  
SGP Singapore 

  
SXM Sint Maarten 

  
SVK Slovakia 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
93 



 

   
SVN Slovenia 

  
SLB Solomon Islands 

  
SOM Somalia 

  
ZAF South Africa 

  
SGS South Georgia and the South Sandwich Islands 

  
SSD South Sudan 

  
ESP Spain 

  
LKA Sri Lanka 

  
SDN Sudan 

  
SUR Suriname 

  
SJM Svalbard and Jan Mayen 

  
SWE Sweden 

  
CHE Switzerland 

  
SYR Syrian Arab Republic 

  
TWN Taiwan 

  
TJK Tajikistan 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
94 



 

   
TZA Tanzania 

  
THA Thailand 

  
TLS Timor-Leste 

  
TGO Togo 

   
TKL Tokelau 

  
TON Tonga 

  
TTO Trinidad and Tobago 

  
TUN Tunisia 

  
TUR Turkey 

  
TKM Turkmenistan 

  
TCA Turks and Caicos Islands 

  
TUV Tuvalu 

  
UGA Uganda 

  
UKR Ukraine 

  
ARE United Arab Emirates 

  
GBR United Kingdom 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
95 



 

  
UMI United States Minor Outlying Islands 

  
USA United States of America 

  
URY Uruguay 

  
UZB Uzbekistan 

  
VUT Vanuatu 

  
VEN Venezuela 

  
VNM Vietnam 

  
VGB British Virgin Islands 

  
VIR U.S. Virgin Islands 

  
WLF Wallis and Futuna 

  
ESH Western Sahara 

  
YMD Yemen 

  
ZMB Zambia 

  
ZWE Zimbabwe 

 

 

 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
96 



 

6.7    Occupation Type Enum 

    
  

Code Name 
  
SE Business or Self employed 

  
SAL Salaried 

  
Prof Professional 

  
Agri Agriculturist 

  
Ret Retired 

  
HWF Housewife 

  
STUD Student 

  
Oth Others 

            
6.8   Nature Of Business Enum 

                           
Code Name 

AdultIndustries Adult Industries (pornography, online dating etc,) 

AdvertisingBroadcastingMarketingPollingPublishing Advertising, broadcasting, marketing, polling and 
publishing 

AlcoholTobaccoProducts Alcohol/tobacco products 

ABPs Alternative Banking Platforms (ABPs) 

ArchitectureDesignandLandscapping Architecture, design and landscapping 

Auctioneers Auctioneers 

Launderettes Cash Intensive - Launderettes 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
97 



 

ParkingGarages Cash Intensive - Parking garages 

PrivatelyownedleasedATMs Cash Intensive - Privately owned/leased ATMs 

RetailConvenienceStoresMarketstalls Cash Intensive - Retail & convenience stores 

SecondhandGoods/PawnBrokers Cash Intensive - Second Hand goods/Pawn Brokers 

TaxiFirms Cash Intensive - Taxi firms 

VendingMachineOperators Cash Intensive - Vending machine operators 

BarsPublicHouseNightclubsTakeawayrestaurants Cash Intensive - Bars, Public House, Nightclubs, 
Takeaway restaurants 

BeautyMassageParloursHairdressingSalons Cash Intensive - Beauty/Massage Parlours/Hair 
dresser 
salons/Nail bars 

FuelStationsGaragesCarwashfacilities Cash Intensive - Fuel Stations/Garages/Car 
wash facilities 

ChemicalProduction Chemical Production and distribution 

CollectionorTreatmentofHazardousorNonhazardous Collection & /or treatment of hazardous or non- 
Waste hazardous 

waste 
ComputerHightechnologyTelecomMobilePhonesales Computer / High technology / Telecom / Mobile 
&distribution Phone 

sales & distribution 
ConstructionBuildingCivilengineeringPublicworks Construction /Building/ Civil engineering 

CulinaryIndividuals Culinary individuals (e.g. chef/sommeliers, dentist) 

DealerinusedCarsTrucksBoatsPlanesMachinemotorP Dealer in used cars, trucks, boats, planes, 
artsorMachinerydealer machine/motor parts 

or machinery dealers 
ArtsAntiquesdealers Dealers in high value precious goods - Arts & 

Antiques dealers 
GemPreciousMetalsOrStoneDealers Dealers in high value precious goods - Gem & 

Precious metals 
or stone dealers 

 
 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
98 



 

6.9    Product Enum 

 
  
Code Name 

  
Depository Depository 

  
EQ Equities 

  
CDX Currency Derivatives 

  
EDX Equity Derivatives 

  
COMDX Commodities 

  
Loan Loan 

  
MF Mutual Funds 

  
IA Investment Advisory 

  
 Portfolio Management - 
PMSD Discretionary 

  
 Portfolio Management - 
PMSND Non Discretionary 

  
CC Credit Card 

  
 Current or Saving 
CASA Account 

  
LI Life Insurance 

  
GI General Insurance 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
99 



 

 
6.10 Income Range Enum 

                       
Code Name 

1 Upto 1 lac 

2 1 - 5 lacs 

3 5 - 10 lacs 

4 10 - 25 lacs 

5 25 lacs - 1 Crore 

6 Above 1 Crore 

 
6.11 Currency Enum 

                        
  
Code Name 

  
INR Indian Rupees 

  
USD US Dollar 

  
ZAR South African Rand 

 

6.12    PEP Enum                      
  
Code Name 

  
PEP PEP 

  
NotAPEP Not a PEP 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
100 



 

  
RelatedToPEP Related to PEP 

 
6.13   PEP Classification Enum 

                        
  
Code Name 

  
1 Domestic PEP 

  
2 Foreign PEP 

  
3 UBO / AP is domestic PEP 

  
4 UBO / AP is foreign PEP 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

101 



 

6.14   AdverseReputation Classification Enum 

 
  
Code Name 

  
1 Corruption 

  
2 Financial crime 

  
3 Organised crime 

  
4 Trafficking 

  
5 Terrorism 

  
6 Terrorist Financing 

  
7 Tax crime 

  
8 War crimes 

 

 

 

 

 

 

 

 

 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
102 



 

6.15   Tags Enum 

 
  
Code Name 

  
1 IUGuarantor 

  
2 STR 

  
3 Section 28 

  
4 Section 34 

  
5 Section 35 

  
6 SOW Available 

 
6.16   Channel Enum 

                        
  
Code Name 

  
1 Direct 

  
2 Intermediary 

 

 

 

 

 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
103 



 

6.17   RegulatoryAMLRisk Enum 

                         
  
Code Name 

  
1 Low 

  
2 Medium 

  
3 High 

  
4 Very High 

 
6.18   State Enum 

                       
  
Code Name 

AN Andaman & Nicobar 

AP Andhra Pradesh 

AR Arunachal Pradesh 

AS Assam 

BR Bihar 

CH Chandigarh 

CG Chhattisgarh 

DNHDD Dadra and Nagar Haveli And Daman and Diu 

DL Delhi 

GA Goa 

GJ Gujarat 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
104 



 

HR Haryana 

  
HP Himachal Pradesh 

JK Jammu & Kashmir 

JH Jharkhand 

KA Karnataka 

KL Kerala 

LD Lakshadweep 

MP Madhya Pradesh 

MH Maharashtra 

MN Manipur 

ML Meghalaya 

MZ Mizoram 

NL Nagaland 

OR Odisha 

PY Puducherry 

PB Punjab 

RJ Rajasthan 

SK Sikkim 

TN Tamil Nadu 

TR Tripura 

TS Telangana 

UP Uttar Pradesh 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
105 



 

UA Uttarakhand 

WB West Bengal 

LA Ladakh 

 

                         
6.19   Qualification Enum 

 
  
Code Name 

  
1 B.Com 

  
2 BCA 

  
3 Graduate 

  
4 MBA 

  
5 MCA 

  
6 Post Graduate 

 
6.20 RegAMLSpecialCategory Enum 

      
         

  
Code Name 

  
1 NRI 

  
2 HNI 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
106 



 

  
3 Trust, Charity, NGO 

  
4 Close family shareholdings or Beneficial Ownership 

  
5 Politically Exposed person 

  
6 Company Offering foreign exchange offerings 

  
7 Client in high risk Country 

  
8 Non Face to face client 

  
9 Client with dubious public reputation 

 
6.21   Agency Enum 

                         
  
Code Name 

  
FINACLE Finacle 

  
FLEXCUBE FlexCube 

  
FINNONE FINNONE 

 
6.22    Prefix Enum 

                          
  
Code Name 

  
Mr Mr 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
107 



 

  
Ms Ms 

  
Mrs Mrs 

  
Miss Miss 

  
Dr Dr 

 
6.23    Document Enum 

                          
  
Code Name 

  
Photograph Photograph 

  
PAN PAN 

  
CIN Company ID Number 

  
CRN Registration Certificate 

  
OthersPOA Others 

  
Passport Passport 

  
DrivingLicence Driving Licence 

  
VoterID Voter ID 

  
NREGA NREGA 

  
NPRLetter NPR Letter 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
108 



 

  
AadhaarOffline Aadhaar Offline 

  
MOA MOA 

  
PD PD 

  
TRUSTDEED TRUST DEED 

  
BoardResolution Board Resolution 

  
POATOM POATOM 

  
OVDIRP OVDIRP 

  
AadhaarCard Aadhaar Card 

  
EKYCAuthImage EKYC Auth Image 

  
UtilityBill2m Utility Bill 2m 

  
PropertyTax Property Tax 

  
PensionOrder Pension Order 

  
EmployerHouseLetter Employer House Letter 

  
CKYCSelfDeclaration CKYC Self Declaration 

  
SMA-AttestedPhoto SMA-Attested Photo 

  
BankStatement Bank Statement 

  
ForeignEmbassyLetter Foreign Embassy Letter 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
109 



 

  
AP1 AP1 

  
AP2 AP2 

  
SMA-IDCard SMA-ID Card 

  
PublicKey Public Key 

  
PrivateKey Private Key 

  
NetworthCertificate Networth Certificate 

  
SelfCertification Self Certification 

  
BalanceSheet Balance Sheet 

  
TaxReturns Tax Returns 

  
SalarySlip Salary Slip 

  
CashFlowStatement Cash Flow Statement 

  
ProfitandLossStatement Profit and Loss Statement 

  
AnnualReport Annual Report 

 
6.24    Purpose Enum  

                          
  
Code Name 

  
01 Initial Screening with API Response and No Storage 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
110 



 

  
03 Initial Screening with API Response and TW Workflow 

  
04 Continuous Screening with TW Workflow. 

 
6.24.1. Purpose-wise Mandatory Fields 

While passing purpose code in the request user can either pass a single purpose or combination of purpose 
i.e 

 Purpose 01 & Purpose 04 or 

 Purpose 03 & Purpose 04 
 

At given point user cannot pass Purpose 01 ,03 together. 

Purpose: 01-Initial Screening with API Response and No Storage. 
 

  
Description List of Mandatory Fields 

  

 1. Source System 
 Customer Code 
Purpose 01 will be used when the user wants to perform just initial 2. Source System Name 
screening on the Customer. Along with the 3. Purpose 
SourceSystemCustomerCode and SourceSystemName. 4. Constitution Type 

  

User can pass value in either one of this fields. AND 
  

For IND: Name, Mobile Number , Email Id ,PAN , Passport, DIN , 1. Name OR 
Driving License 2. Mobile Number OR 

 3. Email-Id OR 
For LE: Name, Mobile Number , Email ID , PAN, CIN, DIN, Date of 4. PAN OR 
Incorporation. 5. Passport OR 

 6. Driving License (DL) OR 
Basis the data passed in these fields the user will receive a response 7. Director Identification 
for the Initial Screening requested for customer. Number (DIN) OR 
Data of the screened customer will not be stored in the TW System. 8. Company Identification 

Number (CIN) 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
111 



 

Purpose: 03-Initial Screening with API Response and TW Workflow 
 

  
Description List of Mandatory Fields 

  

 1. Source System Customer 
 Code 
 2. Source System Name 

 3. Purpose 
4. Constitution Type 

Purpose 03 will be used when the user wants to perform initial 
 

screening on customer and receive an email for hits detected with the AND 
cases linked to these hits.  
This email would consist URL of the case mapped to the hit detected, 1. Name OR 
which would redirect the user to the TW workflow on which the user 2. Mobile Number OR 
will be able to take necessary actions of approving and rejecting a 3. Email ID OR 
customer. 4. PAN OR 
Only the details regarding case would be saved in TW system. 5. Passport OR 

6. Driving License (DL) OR 
7. Director Identification 

Number (DIN) OR 
8. Company Identification 

Number (CIN) 
 

Purpose: 04-Continuous Screening with TW Workflow. 
 

  
Description List of Mandatory Fields 

  

 1. Source System Customer 
 Code 

2. Source System Name 
Purpose 04 will be used when the user wants to perform continuous 3. Constitution Type 
screening on customer and receive an email for hits detected with the 4. Purpose 
cases linked to these hits. AND 
This email would consist of link to the TW case manager 1. Name OR 
corresponding to the case. The user will be able to take relevant 2. Mobile Number OR 
actions on the case. 3. Email ID OR 

 4. PAN OR 
Customers data is saved in TW system only once after all the 5. Passport OR 
validations are passed. Customer Data is saved to perform continuous 6. Driving License (DL) OR 
screening on customers data. 7. Director Identification 

Number (DIN) OR 
8. Company Identification 

Number (CIN) 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

112 



 

6.25    Relation Enum 

                         
Code Name 

101 Accountant 

171 Appointer 

Assignee Assignee 

AuthorisedRepresentative Authorised Representative 

103 Authorized Signatory 

163 Beneficial Owner 

159 Board Member - Foundation 

133 Compliance Officer 

161 Controller 

140 Coparcener 

106 Director 

165 Donor 

138 Employee 

166 Founder 

155 Fund Administrator 

152 Fund Manager 

136 Group Concern 

Guardian Guardian 

111 Karta 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
113 



 

112 KeyRepresentative 

144 Other 

119 Partner 

139 Principal Officer 

120 Promoter 

151 Protector 

137 Reference person 

170 Security Provider 

122 Settlor 

123 Shareholder 

135 Sister Company 

156 Sub-advisor of Fund 

154 Sub-manager of fund 

160 Tax Controlling Person 

157 Transfer Agent 

129 Trustee 

130 UBO 

162 Whole Time Director 

215 Court Appointed Official 

Beneficiary Beneficiary 

 

 

 

 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

114 



 

6.26    KYCAttestation Enum 

 
  
Code Name 

  
1 Certified Copies 
  
2 E-KYC Data received from UIDAI 
  
3 Data received from offline verification 
  
4 Digital KYC Process 
  
5 Equivalent E-Document 
  
6 Video KYC 

 
 
 
 
 
 
  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
115 



 

7. Variations 
7.1. Decrypted Request and Response Variations for Initial Screening (Individual) 

7.1.1. Confirm & Probable Hit (Request) 
{ 

 
"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", 

"kycDateOfDeclaration": "11-Mar-2021", 

"kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
116 



 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "1", 

"regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", 

"networthEffectiveDate": "11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
117 



 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, SRI", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
118 



 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2551", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2551", 

"prefix": "Mr", 

"firstName": "Thuraisamy ", 

"middleName": "", 

"lastName": " Selvakumar ", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "", 

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName": "", 

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName": "", 

"motherLastName": "", 

"gender": "01", 

"dateofBirth": "11-Feb-1995", 

"workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

119 



 

"personalMobileNumber": "", 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "BHARAT DIAMOND BOURSE, TOWER – A(EAST) – 4050”, 
"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "BANDRA KURLA COMPLEX, BANDRA (EAST), MUMBAI", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", 

"correspondenceAddressLine3": "Mahim West", 

"correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", 

"correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "IND", 

"countryOfBirth": "IND", 

"birthCity": "Mumbai", 

"passportIssueCountry": "IND", 

"passportNumber": " ********* ", 

"passportExpiryDate": "02-Feb-2025", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

120 



 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", 

"nprLetterNumber": "NPR25689", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "STDSP1", 

"screeningreportwhenNil": "1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
121 



 

} 

], 
 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null  
} 
 

7.1.2. Confirm & Probable Hit (Response) 

{ 

"RequestId": "IND2551", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2551", 

"ValidationOutcome": "Success", 

"SuggestedAction": "Stop", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

"PurposeCode": "01", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
122 



 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "Yes", 

"HitsCount": 2 

"ConfirmedHit": "Yes", 

"ReportData": 
"JVBERi0xLjMNCjEgMCBvYmoNClsvUERGIC9UZXh0IC9JbWFnZUIgL0ltYWdlQyAvSW1hZ2VJXQ0KZ 
W5kb2JqDQo3IDAgb2JqDQo8PCAvTGVuZ3RoIDU4NDMgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4g 
c3RyZWFtDQpYCbVdbW/cOJL+vsD8B37cPbgVkRQlyrg7wOMkEyOJk409OzjcHhZytxxr0y+efok3++u  
vKPFNEtWkWj0YIElzqvg8VawiKb5IP/3pdxSjJI9ixPI8ylKU5Rn82JboN7T+Cf4vZhFDnKRRkhPEUh5hw 
hDOIhZzLfXzPXr1liJMozzP0f0jVCn+235FKaURjxm6/4AITSPOEac4opig+wX68918W5brav31H/flbg9/ 
wXd/xPd/wd6c//Tn/4q0EmcRQlGPOZRlhCEOQU2GEcZa4MniPewE6GbpDU2BuwUhDCJKBjZAkdf 
yufNdn+JcDy7OnydkZgAErlk+SWjHUZxFCcUxREFJ8QR4wIHzE5ycBzPIoxYRsBDCQJA+BM4PtZmYC 
mSC5FUihApYpuBY2UHrv/r2hExwqGNoD7QFWb8XOyqOXpd7otquev5D1qvBgbIjIP70iiDdiPgBdJFz 
o85UNQDuFkaxbhx35fy9wM0Grp5DY5ztBvYHyfTgZuKutDzzXaBbhblel89VuX2EuWPPEuzBZ7RhzK 
dJY95ObM5jUJKk4izBonPy3IW8/SBlkWSMd43lOZplEM4Tza0roimLUOvnp+X1bzYV5s1BOkjut1cosE 
mThKbAE6sLB7RwtAVZFQmyOawnZfo7sduX67QbbEqL9Hbm9ur6w9vhtt7IgvdCBaPz9vN4jDfo7vy 
6wpafOfwgW6FifC6ESz4++JrH7LXD3z5RfyNXtD//h/UuhCdQgZ18phGcY5WiHEGPY/6vUR3J9Qhf2d 
gXQ4d4GAlGKzuVtL0TppH8zOMRpu6VcUxGqpfBDnIBcLzYz2jEEoaoel9IwxVqewbrw+7/WZVbtH7/ 
7k+3kUCAxF5wGBqAsHgRGT6Ninz+uq3T59eo5ufv1y9u/mI3l/dvb/6gvox3IQ+m05E55BF5brYl1832 
x+X6Ob29UD60OnQOn8s6Jv1vtyuykVVbH+g682iHOrDMpRyfo42SHOq0T9f3TrwlK8nAypf25Cvb1y 
QyseTIZWPW5Db6ruYyXyo5uV6d8zDWXwWD2fMeLjY7Zr507Cbp6JqN1u4t/W4WCyrPUT15+Jbtds 
X62G3T6Wg3W5R+KVcL8Ts42OxLAc9zshZPM4y09qQzGjziH6utvunY26fCq3dboFfbw7rPWTyEXzt8 
6n42ucWfg2KrutWH/Q5Tc7ic5pr2DcrGDyO+VpC0kw8sKQTfG2BXm/W+wJmPreH1YOIs0F7cSpHT 
96MnifaS0yv/Rn67GIN0y10tVhsy51r1iXRWc7Pgh5Ty/AtYD5vIL3WMAUdohA2g4KxnQLDzMxdsC4 
ZMRGzq1EFMLXB8Fg5YiZm5kMWGVkyZjqGXUYdpdPMyBjMw8QcWc3HxKM8YfZ0TEikjUTaSFApM 
Xouxngc4biZi72r9jsxByvn+3KB/guxgLbsug+o5SSKqWwCY8uy9kFKHAVSYYl2AoOTDNl/msd3xoTRH 
FF4/IrBIt4Ece0V8FEjwhuRuBHpxTl1xLm1AqKqSWkEs4DmGWsLD3U9TzhYwgOJzZLD02act1ky2mIp 
GlGIjGWpqrFZ1k+CITQxJXV3qHhCOELwdN1JUynUMMXwdy002qGiIuhxbK6ft9VKzDQ/Fvv5UwhlA 
oNMwoxrYVpVd+A2YxrHUqZhzIc6eQ9hXY9FuCaK7n88BzmY8jziGTFsxUpcx78UTGhkGrY5bmRGsx 
X1JI5IQL+uq98PJbpZBIUueItm1FDm/ZBIgGMjIymT0yJC1NNmfL8t5t9eqn//G930H3xURkLUJqlO7B 
OWx2Q9CdO4/aUilVgSTObneDBVjwX26+3d9T8wSbO+hSrXJKrKtRNsVMlm4V6RGM3Qm3/VkxR42 
kVX6x/o0xYmxWjs8puuHiZSYuzQqeFaFtZ5JK2S+XjCqpuqyDLq83bzUDw4ZvQ6rySqzKtT1vqaxLJA// 
q6ijDtDopWYihMcmLzycywIAnGGRlMCZiO4uQMKYFh4Iol4GBKSLDJKWGB/Xr1+Wo4GyTgGbLBgv 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
123 



 

wjsiEGgkloNkirpmeDZZQ/GyTq5GywQJN0OBEU3MREsNAymJazfn8tIzjJYMQl0zMhATNjua/Q3VbS 
maDApmaCDQaDNUy530PkLDaru7GRqCrkOOJ5U2Gxnoullw/Vbj+YYcqQ6Rlmm/IHZJhtmDfDlFWT 
M8w2ypthCnVqhtmgGU/7QahyTANOyzEbj5AsZoOjTQJRxuJseo6lkDdyfzcZzDEJNjnHLLBPb6+uh3N 
BAp4hFyzIPyIXGMwOcGguSKum54JllD8XJOrkXLBA84zx4VxQgBNzwcLDSUrzwVQgWYT5GVIhifUZg 
f60UoWwBJucChbYm8N281wWaxh2NuuThxuSRyRN7OFmOL2kEWdIL8uMPyK9LKP86SWtmp5ell 
H+9JKok9PLAsVx1o92nV4KcGJ6WXgk4znpJnSjOrDDLlYNGeVoJZbZUoJ1QbOSGqaqClgapSntqLq25 
NV6iEGVBUdU1TKgTVitC/ZRW4RBUiwS24TB2XTA1gmoXdUaVduqUP226rXEFmO1uuixVojC9N7oi 
mU+xoeadhJwT1kAa4M1sN9ivRTZYq0WJz0WC1FK7QYWa4W52KDxmzwWuadcI2ubNbLfZr2g2aKt 
ljg9NgtRsadiRaboL3MeYvNY5J5yjWySWCEHZLFaEW3RVmukvjwGUbEkYJSb/Z8kKJNHIveUa2Rts0Z 
22uzYtkpctM/dYzr7adbsSZt+GiZYNAlCBUmx9GZQZUFAj6lRVYEftbPT01JtUHVv1C/R/th5O7dWj9yu 
Tud+v8QP0BVdOarTidYv8QN0RVeO6nRU90v8AGPj3J1hpuktLr62V8g65DSyM+bcGaaRbeWzxrozw 
0CS5XaGxUkUkyBUkBRregZVFgSkiUZVBX7UTkK1VBtUnRL9Eu0Pf4bp+vvV6ZTol/gBuqIrR3U6Jfolfo 
Cu6MpRnY7qfokfYGycuzPMNL3Fxdf2ClmHnEZ2xpw7wzSyrXzWWHdmGEiSzMqwJMvguT0EVUiKF 
T2NqgoC0kSjyoIA1E5CtVQbVJUS/RLjD3+G6fr71amU6JcEAHRFV47qVEr0SwIAuqIrR3UqqvslAQBj49 
ydYabpLS6+tpfIJuQUsjvm3BmmkW3ls8a6K8OEZM7sDGM4opyEoDJcrxMaVFngTxODqgr8qJ2Eaqk 
2qDol+iXaH94MM/X3q9Mp0S/xA3RFV47qdEr0S/wAXdGVozod1f0SP8DYOHdmmNX0Fhdf2ytkHXI 
a2RlzzgwzyLbyWWPdmWEgmVJiZRhhUUKDVKWkQXWqOtNEo6qCIFWxcmVslStXI/I6DNW1SmdsV 
ah+wnaGKhfLkjDl2A4ptWwVYO5YYNdCnTFYA/tJ211GA6xLwpTFqpUxWa1aBZg8Ftm5Umds1sh+2 
nYv1iDrkjBlsWplJbBctQqweSyyc6XOSmKFHJCKVt8oE0qVhCmP6S0nITtX6ozNGtmvbJNskN3KwxeSr 
yAxNC4wLjAuMCkNCi9Qcm9kdWNlciAoTWljcm9zb2Z0IFJlcG9ydGluZyBTZXJ2aWNlcyBQREYgUmV 
uZGVyaW5nIEV4dGVuc2lvbiAxNC4wLjAuMCkNCi9DcmVhdGlvbkRhdGUgKEQ6MjAyMjA4MTAxMj 
U5NTUrMDUnMzAnKQ0KPj4NCmVuZG9iag0KeHJlZg0KMCAyNw0KMDAwMDAwMDAwMCA2NTU 
zNSBmDQowMDAwMDAwMDEwIDAwMDAwIG4NCjAwMDAwMDU5ODYgMDAwMDAgbg0KMDA 
wMDExNzYwNiAwMDAwMCBuDQowMDAwMTE3NzExIDAwMDAwIG4NCjAwMDAxMTc4MTEgM 
DAwMDAgbg0KMDAwMDAwNjE4MSAwMDAwMCBuDQowMDAwMDAwMDY1IDAwMDAwIG4NC 
jAwMDAxMTgzNzcgMDAwMDAgbg0KMDAwMDAyMDgzNSAwMDAwMCBuDQowMDAwMDE0Mz 
A3IDAwMDAwIG4NCjAwMDAwMjcxODMgMDAwMDAgbg0KMDAwMDAyMTAyMSAwMDAwMCB 
uDQowMDAwMDM0OTkwIDAwMDAwIG4NCjAwMDAwMjczODAgMDAwMDAgbg0KMDAwMDA0 
MjEzNyAwMDAwMCBuDQowMDAwMDM1MTc3IDAwMDAwIG4NCjAwMDAwNDU2MTUgMDAw 
MDAgbg0KMDAwMDA0MjMzNCAwMDAwMCBuDQowMDAwMDQ5MDc0IDAwMDAwIG4NCjAw 
MDAwNDU3OTIgMDAwMDAgbg0KMDAwMDA0OTI2MSAwMDAwMCBuDQowMDAwMTE3MjcwI 
DAwMDAwIG4NCjAwMDAxMTc5NTMgMDAwMDAgbg0KMDAwMDExODE4MCAwMDAwMCBuD 
QowMDAwMTE4NDgwIDAwMDAwIG4NCjAwMDAxMTg1MzMgMDAwMDAgbg0KdHJhaWxlciA8P 
CAvU2l6ZSAyNyAvUm9vdCAyNSAwIFIgL0luZm8gMjYgMCBSID4+DQpzdGFydHhyZWYNCjExODgzN 
g0KJSVFT0Y=", 

"HitResponse": [ 

{ 
 

"Source": " FBI Wanted Person ", 

"WatchlistSourceId": "20", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

124 



 

"MatchType": "KeyActivity", 

"Score": 100.0, 

"ConfirmedMatchingAttributes": "Passport" 

}, 

{ 
 

"Source": " Sri Lanka Terrorist Sanctions ", 

"WatchlistSourceId": "7", 

"MatchType": "Probable", 

"Score":65, 

"ConfirmedMatchingAttributes": "" 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

], 

} 

] 
 

}  
} 

 
7.1.3. Probable Hits (Request) 

 

                                   { 

"requestId": "IND2552", 

"sourceSystemName": "FINACLE", 

"purpose": "01", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
125 



 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Diamond Merchant", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", 

"kycDateOfDeclaration": "", 

"kycPlaceOfDeclaration": "", 

"kycVerificationDate": "", 

"kycEmployeeName": "", 

"kycEmployeeDesignation": “", 

"kycVerificationBranch": "", 

"kycEmployeeCode": "", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
126 



 

"regAMLRisk": "", 

"regAMLRiskLastRiskReviewDate": ", 

"regAMLRiskNextRiskReviewDate": "", 

"incomeRange": "", 

"exactIncome": , 

"incomeCurrency": "", 

"incomeEffectiveDate": "", 

"incomeDescription": "", 

"incomeDocument": "", 

"exactNetworth": , 

"networthCurrency": "", 

"networthEffectiveDate": "", 

"networthDescription": "", 

"networthDocument": "", 

"familyCode": "", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
127 



 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "", 

"countryOfOperations": "", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "", 

"regAMLRiskSpecialCategoryStartDate": "" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2552", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2552", 

"prefix": "Mr", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
128 



 

"firstName": " RIYAZ ISMAIL SHAHBANDR", 

"middleName": "", 

"lastName": "", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "",  

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName":""     

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName":""

,"motherLastName": "", 

"gender": "01", 

"dateofBirth": "19-May-1976", 

"workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 

"personalMobileNumber": "", 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "", 

"permanentAddressZipCode": "", 

"permanentAddressLine1": "", 

"permanentAddressLine2": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
129 



 

"permanentAddressLine3": "", 

"permanentAddressDistrict": "", 

"permanentAddressCity": "Lahore", 

"permanentAddressState": "", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "",  

"correspondenceAddressCountry": "", 

"correspondenceAddressZipCode": "", 

 "correspondenceAddressLine1": "", 

 "correspondenceAddressLine2": "",  

"correspondenceAddressLine3": "",  

"correspondenceAddressDistrict": "",  

"correspondenceAddressCity": "", 
 
 "correspondenceAddressState": "", 
 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "", 

"countryOfBirth": "", 

"birthCity": "", 

"passportIssueCountry": "", 

"passportNumber": "", 

"passportExpiryDate": "", 

"voterIdNumber": "", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
130 



 

"nregaNumber": "MH-02-12038/90", 

"nprLetterNumber": "NPR25689", 

"directorIdentificationNumber": "00190509", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "", 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "STDSP3", 

"screeningreportwhenNil":"1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "", 

"taxIdentificationNumber": "", 

"taxResidencyStartDate": "", 

"taxResidencyEndDate": "” 

} 

], 
 

"politicallyExposedClassification": "", 
 

 
"citizenships": "IND, GBR", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
131 



 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null 

} 

 
7.1.4. Probable Hits (Response)  

 

{ 

"RequestId": "IND2552",  

"OverallStatus": "AcceptedByTW",  

"ValidationCode": "null",  

"ValidationDescription": "null",  

"CustomerResponse":  

[ 

 { 

  "SourceSystemCustomerCode": "2552",  

  "ValidationOutcome": "Success",  

  "SuggestedAction": "Review",  

  "PurposeResponse": 

   [ 

   { 

    "Purpose": "Initial Screening with API Response and No Storage", 

    "PurposeCode": "01", 

    "ValidationCode": "",  

    "ValidationDescription": "", 

    "ValidationFailureCount": 0  
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

132 



 

    "Data": 

    { 

     "HitsDetected": "Yes",  

     "HitsCount": 2,  

     "ConfirmedHit": "No",  

     "ReportData": "Base 64 Data", 

     "HitResponse" 

      { 

      "Source": "MHA_Designated",  

      "WatchlistsourceID": "68",  

      "MatchType":"Probable",  

      "Score": 80, 

      "ConfirmedMatchingAttributes": "", 

      } 

      { 

      "Source": " MHA_Designated ", 

      "WatchlistsourceID": "72", 

      "MatchType":"Probable", 

      "Score": 80, 

      "ConfirmedMatchingAttributes": "", 

     } 

    } 

   }   

  ] 

 }, 

 

"ValidationCode": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
133 



 

"ValidationDescription": "",  

"ValidationFailureCount": 0 

}, 

], 

 

"RelatedPersonResponse": null,  

"RelatedPersonRelationResponse": null 

} 

] 

} 
 
                   
 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
134 



 

7.2. Decrypted Request and Response Variations for Continuous Screening (Individual) 
7.2.1. Confirm & Probable Hits (Request) 

 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", 

"kycDateOfDeclaration": "11-Mar-2021", 

"kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
135 



 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 
 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "1", 

"regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", 

"networthEffectiveDate": "11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
136 



 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

"relatedPersonList": [], 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
137 



 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2551", 
 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2551", 

"prefix": "Mr", 

"firstName": " Thuraisamy Selvakumar ", 

"middleName": "", 

"lastName": "", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "", 

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName": "", 

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName": "", 

"motherLastName": "", 

"gender": "01", 

"dateofBirth": "11-Feb-1995", 

"workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 

"personalMobileNumber": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
138 



 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "BHARAT DIAMOND BOURSE, TOWER – A(EAST) – 4050”, 
"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "BANDRA KURLA COMPLEX, BANDRA (EAST), MUMBAI", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", 

"correspondenceAddressLine3": "Mahim West", 

"correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", 

"correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "IND", 

"countryOfBirth": "IND", 

"birthCity": "Mumbai", 

"passportIssueCountry": "IND", 

"passportNumber": " ********* ", 

"passportExpiryDate": "02-Feb-2025", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
139 



 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", 

"nprLetterNumber": "NPR25689", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "STDSP1", 

"screeningreportwhenNil": "1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
140 



 

} 
 

], 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null 

} 
7.2.2. Confirm & Probable Hit (Response)  

{ 

"RequestId": "IND2551", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2551", 

"ValidationOutcome": "Success", 

"SuggestedAction": "Stop", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 
 

"PurposeCode": "01", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
141 



 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "Yes", 

"HitsCount": 2 

"ConfirmedHit": "Yes", 

"ReportData": Base 64 
"JVBERi0xLjMNCjEgMCBvYmoNClsvUERGIC9UZXh0IC9JbWFnZUIgL0ltYWdlQyAvSW1hZ2VJXQ0KZ 
W5kb2JqDQo3IDAgb2JqDQo8PCAvTGVuZ3RoIDU4NDMgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4g 
c3RyZWFtDQpYCbVdbW/cOJL+vsD8B37cPbgVkRQlyrg7wOMkEyOJk409OzjcHhZytxxr0y+efok3++u 
vKPFNEtWkWj0YIElzqvg8VawiKb5IP/3pdxSjJI9ixPI8ylKU5Rn82JboN7T+Cf4vZhFDnKRRkhPEUh5hw 
hDOIhZzLfXzPXr1liJMozzP0f0jVCn+235FKaURjxm6/4AITSPOEac4opig+wX68918W5brav31H/flbg9/ 
/wXd/xPd/wd6c//Tn/4q0EmcRQlGPOZRlhCEOQU2GEcZa4MniPewE6GbpDU2BuwUhDCJKBjZAkdf 
yufNdn+JcDy7OnydkZgAErlk+SWjHUZxFCcUxREFJ8QR4wIHzE5ycBzPIoxYRsBDCQJA+BM4PtZmYC 
mSC5FUihApYpuBY2UHrv/r2hExwqGNoD7QFWb8XOyqOXpd7otquev5D1qvBgbIjIP70iiDdiPgBdJFz 
o85UNQDuFkaxbhx35fy9wM0Grp5DY5ztBvYHyfTgZuKutDzzXaBbhblel89VuX2EuWPPEuzBZ7RhzK 
dJY95ObM5jUJKk4izBonPy3IW8/SBlkWSMd43lOZplEM4Tza0roimLUOvnp+X1bzYV5s1BOkjut1cosE 
mThKbAE6sLB7RwtAVZFQmyOawnZfo7sduX67QbbEqL9Hbm9ur6w9vhtt7IgvdCBaPz9vN4jDfo7vy 
6wpafOfwgW6FifC6ESz4++JrH7LXD3z5RfyNXtD//h/UuhCdQgZ18phGcY5WiHEGPY/6vUR3J9Qhf2d 
gXQ4d4GAlGKzuVtL0TppH8zOMRpu6VcUxGqpfBDnIBcLzYz2jEEoaoel9IwxVqewbrw+7/WZVbtH7/ 
7k+3kUCAxF5wGBqAsHgRGT6Ninz+uq3T59eo5ufv1y9u/mI3l/dvb/6gvox3IQ+m05E55BF5brYl1832 
x+X6Ob29UD60OnQOn8s6Jv1vtyuykVVbH+g682iHOrDMpRyfo42SHOq0T9f3TrwlK8nAypf25Cvb1y 
QyseTIZWPW5Db6ruYyXyo5uV6d8zDWXwWD2fMeLjY7Zr507Cbp6JqN1u4t/W4WCyrPUT15+Jbtds 
X62G3T6Wg3W5R+KVcL8Ts42OxLAc9zshZPM4y09qQzGjziH6utvunY26fCq3dboFfbw7rPWTyEXzt8 
6n42ucWfg2KrutWH/Q5Tc7ic5pr2DcrGDyO+VpC0kw8sKQTfG2BXm/W+wJmPreH1YOIs0F7cSpHT 
96MnifaS0yv/Rn67GIN0y10tVhsy51r1iXRWc7Pgh5Ty/AtYD5vIL3WMAUdohA2g4KxnQLDzMxdsC4 
ZMRGzq1EFMLXB8Fg5YiZm5kMWGVkyZjqGXUYdpdPMyBjMw8QcWc3HxKM8YfZ0TEikjUTaSFApM 
Xouxngc4biZi72r9jsxByvn+3KB/guxgLbsug+o5SSKqWwCY8uy9kFKHAVSYYl2AoOTDNl/msd3xoTRH 
FF4/IrBIt4Ece0V8FEjwhuRuBHpxTl1xLm1AqKqSWkEs4DmGWsLD3U9TzhYwgOJzZLD02act1ky2mIp 
GlGIjGWpqrFZ1k+CITQxJXV3qHhCOELwdN1JUynUMMXwdy002qGiIuhxbK6ft9VKzDQ/Fvv5UwhlA 
oNMwoxrYVpVd+A2YxrHUqZhzIc6eQ9hXY9FuCaK7n88BzmY8jziGTFsxUpcx78UTGhkGrY5bmRGsx 
X1JI5IQL+uq98PJbpZBIUueItm1FDm/ZBIgGMjIymT0yJC1NNmfL8t5t9eqn//G930H3xURkLUJqlO7B 
OWx2Q9CdO4/aUilVgSTObneDBVjwX26+3d9T8wSbO+hSrXJKrKtRNsVMlm4V6RGM3Qm3/VkxR42 
kVX6x/o0xYmxWjs8puuHiZSYuzQqeFaFtZ5JK2S+XjCqpuqyDLq83bzUDw4ZvQ6rySqzKtT1vqaxLJA// 
q6ijDtDopWYihMcmLzycywIAnGGRlMCZiO4uQMKYFh4Iol4GBKSLDJKWGB/Xr1+Wo4GyTgGbLBgv 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
142 



 

wjsiEGgkloNkirpmeDZZQ/GyTq5GywQJN0OBEU3MREsNAymJazfn8tIzjJYMQl0zMhATNjua/Q3VbS 
maDApmaCDQaDNUy530PkLDaru7GRqCrkOOJ5U2Gxnoullw/Vbj+YYcqQ6Rlmm/IHZJhtmDfDlFWT 
M8w2ypthCnVqhtmgGU/7QahyTANOyzEbj5AsZoOjTQJRxuJseo6lkDdyfzcZzDEJNjnHLLBPb6+uh3N 
BAp4hFyzIPyIXGMwOcGguSKum54JllD8XJOrkXLBA84zx4VxQgBNzwcLDSUrzwVQgWYT5GVIhifUZg 
f60UoWwBJucChbYm8N281wWaxh2NuuThxuSRyRN7OFmOL2kEWdIL8uMPyK9LKP86SWtmp5ell 
H+9JKok9PLAsVx1o92nV4KcGJ6WXgk4znpJnSjOrDDLlYNGeVoJZbZUoJ1QbOSGqaqClgapSntqLq25 
NV6iEGVBUdU1TKgTVitC/ZRW4RBUiwS24TB2XTA1gmoXdUaVduqUP226rXEFmO1uuixVojC9N7oi 
mU+xoeadhJwT1kAa4M1sN9ivRTZYq0WJz0WC1FK7QYWa4W52KDxmzwWuadcI2ubNbLfZr2g2aKt 
ljg9NgtRsadiRaboL3MeYvNY5J5yjWySWCEHZLFaEW3RVmukvjwGUbEkYJSb/Z8kKJNHIveUa2Rts0Z 
22uzYtkpctM/dYzr7adbsSZt+GiZYNAlCBUmx9GZQZUFAj6lRVYEftbPT01JtUHVv1C/R/th5O7dWj9yu 
Tud+v8QP0BVdOarTidYv8QN0RVeO6nRU90v8AGPj3J1hpuktLr62V8g65DSyM+bcGaaRbeWzxrozw 
0CS5XaGxUkUkyBUkBRregZVFgSkiUZVBX7UTkK1VBtUnRL9Eu0Pf4bp+vvV6ZTol/gBuqIrR3U6Jfolfo 
Cu6MpRnY7qfokfYGycuzPMNL3Fxdf2ClmHnEZ2xpw7wzSyrXzWWHdmGEiSzMqwJMvguT0EVUiKF 
T2NqgoC0kSjyoIA1E5CtVQbVJUS/RLjD3+G6fr71amU6JcEAHRFV47qVEr0SwIAuqIrR3UqqvslAQBj49 
ydYabpLS6+tpfIJuQUsjvm3BmmkW3ls8a6K8OEZM7sDGM4opyEoDJcrxMaVFngTxODqgr8qJ2Eaqk 
2qDol+iXaH94MM/X3q9Mp0S/xA3RFV47qdEr0S/wAXdGVozod1f0SP8DYOHdmmNX0Fhdf2ytkHXI 
a2RlzzgwzyLbyWWPdmWEgmVJiZRhhUUKDVKWkQXWqOtNEo6qCIFWxcmVslStXI/I6DNW1SmdsV 
ah+wnaGKhfLkjDl2A4ptWwVYO5YYNdCnTFYA/tJ211GA6xLwpTFqpUxWa1aBZg8Ftm5Umds1sh+2 
nYv1iDrkjBlsWplJbBctQqweSyyc6XOSmKFHJCKVt8oE0qVhCmP6S0nITtX6ozNGtmvbJNskN3KwxeSr 
yAxNC4wLjAuMCkNCi9Qcm9kdWNlciAoTWljcm9zb2Z0IFJlcG9ydGluZyBTZXJ2aWNlcyBQREYgUmV 
uZGVyaW5nIEV4dGVuc2lvbiAxNC4wLjAuMCkNCi9DcmVhdGlvbkRhdGUgKEQ6MjAyMjA4MTAxMj 
U5NTUrMDUnMzAnKQ0KPj4NCmVuZG9iag0KeHJlZg0KMCAyNw0KMDAwMDAwMDAwMCA2NTU 
zNSBmDQowMDAwMDAwMDEwIDAwMDAwIG4NCjAwMDAwMDU5ODYgMDAwMDAgbg0KMDA 
wMDExNzYwNiAwMDAwMCBuDQowMDAwMTE3NzExIDAwMDAwIG4NCjAwMDAxMTc4MTEgM 
DAwMDAgbg0KMDAwMDAwNjE4MSAwMDAwMCBuDQowMDAwMDAwMDY1IDAwMDAwIG4NC 
jAwMDAxMTgzNzcgMDAwMDAgbg0KMDAwMDAyMDgzNSAwMDAwMCBuDQowMDAwMDE0Mz 
A3IDAwMDAwIG4NCjAwMDAwMjcxODMgMDAwMDAgbg0KMDAwMDAyMTAyMSAwMDAwMCB 
uDQowMDAwMDM0OTkwIDAwMDAwIG4NCjAwMDAwMjczODAgMDAwMDAgbg0KMDAwMDA0 
MjEzNyAwMDAwMCBuDQowMDAwMDM1MTc3IDAwMDAwIG4NCjAwMDAwNDU2MTUgMDAw 
MDAgbg0KMDAwMDA0MjMzNCAwMDAwMCBuDQowMDAwMDQ5MDc0IDAwMDAwIG4NCjAw 
MDAwNDU3OTIgMDAwMDAgbg0KMDAwMDA0OTI2MSAwMDAwMCBuDQowMDAwMTE3MjcwI 
DAwMDAwIG4NCjAwMDAxMTc5NTMgMDAwMDAgbg0KMDAwMDExODE4MCAwMDAwMCBuD 
QowMDAwMTE4NDgwIDAwMDAwIG4NCjAwMDAxMTg1MzMgMDAwMDAgbg0KdHJhaWxlciA8P 
CAvU2l6ZSAyNyAvUm9vdCAyNSAwIFIgL0luZm8gMjYgMCBSID4+DQpzdGFydHhyZWYNCjExODgzN 
g0KJSVFT0Y=", 

"HitResponse": [ 

{ 
 

"Source": " FBI Wanted Person ", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
143 



 

"WatchlistSourceId": "20", 

"MatchType": "KeyActivity", 

"Score": 100.0, 

"ConfirmedMatchingAttributes": "Passport" 

}, 

{ 
 

"Source": " Sri Lanka Terrorist Sanctions ", 

"WatchlistSourceId": "7", 

"MatchType": "Probable", 

"Score":65, 

"ConfirmedMatchingAttributes": "" 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

{ 

"Purpose": "Continuous Screening with email linked cases", 

"PurposeCode": "04", 

"Data": null, 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

} 
 

], 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
144 



 

} 

] 

} } 

 

 
7.2.3. Probable Hits (Request) 

 

{ 

"requestId": "IND2552", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Diamond Merchant", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
145 



 

"kycDateOfDeclaration": "", 

"kycPlaceOfDeclaration": "", 

"kycVerificationDate": "", 

"kycEmployeeName": "", 

"kycEmployeeDesignation": “", 

"kycVerificationBranch": "", 

"kycEmployeeCode": "", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "", 

"regAMLRiskLastRiskReviewDate": ", 

"regAMLRiskNextRiskReviewDate": "", 

"incomeRange": "", 

"exactIncome": , 

"incomeCurrency": "", 

"incomeEffectiveDate": "", 

"incomeDescription": "", 

"incomeDocument": "", 

"exactNetworth": , 

"networthCurrency": "", 

"networthEffectiveDate": "", 

"networthDescription": "", 

"networthDocument": "", 

"familyCode": "", 

"channel": "2", 

"contactPersonFirstName1": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
146 



 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "", 

"countryOfOperations": "", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 

"regAMLRiskSpecialCategory": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
147 



 

"regAMLRiskSpecialCategoryStartDate": "" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2552", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2552", 

"prefix": "Mr", 

"firstName": " RIYAZ ISMAIL SHAHBANDR", 

"middleName": "", 

"lastName": "", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "", 

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName": "", 

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName": "", 

"motherLastName": "", 

"gender": "01", 

"dateofBirth": "19-May-1976", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
148 



 

"workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 

"personalMobileNumber": "", 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "", 

"permanentAddressZipCode": "", 

"permanentAddressLine1": "", 

"permanentAddressLine2": "", 

"permanentAddressLine3": "", 

"permanentAddressDistrict": "", 

"permanentAddressCity": "Lahore", 

"permanentAddressState": "", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "", 

"correspondenceAddressZipCode": "", 

"correspondenceAddressLine1": “", 

"correspondenceAddressLine2": "", 

"correspondenceAddressLine3": "", 

"correspondenceAddressDistrict": "", 

"correspondenceAddressCity": "", 

"correspondenceAddressState": "", 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "", 

"countryOfBirth": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
149 



 

"birthCity": "", 

"passportIssueCountry": "", 

"passportNumber": "", 

"passportExpiryDate": "", 

"voterIdNumber": "", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", 

"nprLetterNumber": "NPR25689", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "", 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "SP3", 

"screeningreportwhenNil":"1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "", 

"taxDetailDtoList": [ 

{ 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
150 



 

"taxResidencyCountry": "", 

"taxIdentificationNumber": "", 

"taxResidencyStartDate": "", 

"taxResidencyEndDate": "” 

} 

], 
 

"politicallyExposedClassification": "", 
 

 
"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null 

} 

 
7.2.4. Probable Hits (Response) 

 

{ 
 

"RequestId": "IND2552", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2552", 

"ValidationOutcome": "Success", 

"SuggestedAction": "Review", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

151 



 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

"PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{  
 
 "HitsDetected": "Yes", 

 "HitsCount": 2, 
 
 "ConfirmedHit": "No", 
 

"ReportData": "Base 64 Data", 
 

"Hit": 

{

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
152 



 

 

"Source": "MHA_Designated", 

"WatchlistsourceID": "68", 

"MatchType":"Probable", 

"Score": 80, 

"ConfirmedMatchingAttributes": "", 

} 
 

{ 

"Source": " MHA_Designated ", 

"WatchlistsourceID": "72", 

"MatchType":"Probable", 

"Score": 80, 

"ConfirmedMatchingAttributes": "", 

} 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

{ 
 

"Purpose": "Continuous Screening with email linked cases", 

"PurposeCode": "04", 

"Data": null, 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

153 



 

} 

], 
 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

] 
    

7.2.5. Validation Failure (Request) 
 

{ 
 

"requestId": "IND2241", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", 

"segmentStartDate": "11-Feb-2022", 

"status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

154 



 

"kycAttestationType": "1", 

"kycDateOfDeclaration": "11-Mar-2021", 

"kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45569", 

"documentRefNumber": "DOCREF5726", 

"regAMLRisk": "1", 

"regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", 

"networthEffectiveDate": "11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
155 



 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers\n,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
156 



 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019", 

"regAMLRiskSpecialCategoryStartDateDateTime": null 

} 

], 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"constitutionTypeId": 0, 

"sourceSystemCustomerCode": "2241", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2241", 

"prefix": "Mr", 

"firstName": "Ganesh", 

"middleName": "Gitesh", 

"lastName": "Hemani", 

"fatherPrefix": "Mr", 

"fatherFirstName": "Gitesh", 

"fatherMiddleName": "Sambhaji", 

"fatherLastName": "Hemani", 

"spousePrefix": "Mrs", 

"spouseFirstName": "Gita", 

"spouseMiddleName": "Hansraj", 

"spouseLastName": "Hemani", 

"motherPrefix": "Mrs", 

"motherFirstName": "Jayaprabha", 

"motherMiddleName": "Gitesh", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
157 



 

"motherLastName": "Hemani", 

"gender": "01", 

"dateofBirth": "11-Feb-2024", 
 
"workEmail": "<EMAIL>", 

"personalEmail": "<EMAIL>", 

"personalMobileISD": "91", 

"personalMobileNumber": "9950238478", 

"workMobileISD": "91", 

"workMobileNumber": "7330067912", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "Gokulnagri, Chawl no 15, Room no- 101", 

"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "Behind RK Hotel, Mumbai", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", 

"correspondenceAddressLine3": "Mahim West", 

"correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", 

"correspondenceAddressState": "", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
158 



 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "IND", 

"countryOfBirth": "IND", 
 

"birthCity": "Mumbai", 

"passportIssueCountry": "IND", 

"passportNumber": "PASS38147", 

"passportExpiryDate": "02-Feb-2023", 

"voterIdNumber": "VOTE78455", 

"drivingLicenseNumber": "DL935156", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398605", 

"nregaNumber": "NREGA6965@", 

"nprLetterNumber": "1234567891234567890ABCDEFGHIJKLMONPQRSTUVWXYZ112233", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "ADDPP95475", 

"ckycNumber": "80080070068542", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationstring": null, 

"adverseReputationDetails": "Involved in Money Laundering Crimes", 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": null, 

"riskProfile": null, 

"adverseReputation": "1", 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

159 



 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2241", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026", 

"taxResidencyEndDateDateTime": null 

} 

], 
 

"politicallyExposedClassification": "1, 3", 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null 

} 
7.2.6. Validation Failure (Response) 

 

{ 

"RequestId": "IND2241", 

"OverallStatus": "RejectedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 
"SourceSystemCustomerCode": "2241", 

"ValidationOutcome": "Failure", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
160 



 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

"PurposeCode": "01", 

"ValidationCode": "DateofBirth - FDV, Gender - SV, NPRLetterNumber - Max-L, 
NREGANumber - SC, Pan - FOR, PassportExpiryDate - PDV", 

"ValidationDescription": "DateofBirth - FDV: Future Date or Current Date not allowed, Gender - 
SV: Value passed should be as per specified values only, NPRLetterNumber - Max-L: Length should not be 
greater than 50 characters, NREGANumber - SC: Only Specific Characters are allowed, Pan - FOR: Invalid 
PAN as the pan provided does not fulfills the criteria of having length of 10 characters, first 5 characters 
as alphabets, 4th character as 'P' and last character is an alphabet., PassportExpiryDate - PDV: Past Date 
or Current Date is not allowed", 

"ValidationFailureCount": 6 

"Data": "null", 

{ 

}, 
 

 
}, 

{ 
 

"Purpose": "Continuous Screening with TW 

Workflow.", "PurposeCode": "04", 

"ValidationCode": "CorrespondenceAddressState - M, DateofBirth - FDV, Gender - SV, 
NPRLetterNumber - Max-L, NREGANumber - SC, Pan - FOR, PassportExpiryDate - PDV", 

"ValidationDescription": "CorrespondenceAddressState - M: is mandatory, DateofBirth - FDV: 
Future Date or Current Date not allowed, Gender - SV: Value passed should be as per specified values 
only, NPRLetterNumber - Max-L: Length should not be greater than 50 characters, NREGANumber - SC: 
Only Specific Characters are allowed, Pan - FOR: Invalid PAN as the pan provided does not fulfills the  
criteria of having length of 10 characters, first 5 characters as alphabets, 4th character as 'P' and last 
character is an alphabet., PassportExpiryDate - PDV: Past Date or Current Date is not allowed", 

"ValidationFailureCount": 7 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
161 



 

"Data":"null", 

} 

], 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

] 

} 

} 
 
 
 
 
 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
Email :<EMAIL> 

 
Tel : +91-22–6551 4191 / 92 

162 



 

  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
163 



 

8. Request And Response XSD 
8.1 Request XSD 

 
{ 
  "$schema": "http://json-schema.org/draft-04/schema#", 
  "type": "object", 
  "properties": { 
    "requestId": { 
      "type": "string" 
    }, 
    "sourceSystemName": { 
      "type": "string" 
    }, 
    "purpose": { 
      "type": "string" 
    }, 
    "customerList": { 
      "type": "array", 
      "items": [ 
        { 
          "type": "object", 
          "properties": { 
            "ekycOTPbased": { 
              "type": "string" 
            }, 
            "ekycOTPbasedId": { 
              "type": "null" 
            }, 
            "segment": { 
              "type": "string" 
            }, 
            "segmentId": { 
              "type": "null" 
            }, 
            "segmentStartDate": { 
              "type": "string" 
            }, 
            "segmentStartDateDateTime": { 
              "type": "null" 
            }, 
            "status": { 
              "type": "string" 
            }, 
            "statusId": { 
              "type": "null" 
            }, 
            "effectiveDate": { 
              "type": "string" 
            }, 
            "effectiveDateDateTime": { 
              "type": "null" 
            }, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
164 



 

            "minor": { 
              "type": "string" 
            }, 
            "minorId": { 
              "type": "null" 
            }, 
            "maritalStatus": { 
              "type": "string" 
            }, 
            "maritalStatusId": { 
              "type": "null" 
            }, 
            "occupationType": { 
              "type": "string" 
            }, 
            "occupationTypeOther": { 
              "type": "string" 
            }, 
            "occupationTypeId": { 
              "type": "null" 
            }, 
            "natureOfBusinessOther": { 
              "type": "string" 
            }, 
            "companyIdentificationNumber": { 
              "type": "string" 
            }, 
            "companyRegistrationNumber": { 
              "type": "string" 
            }, 
            "companyRegistrationCountry": { 
              "type": "string" 
            }, 
            "companyRegistrationCountryId": { 
              "type": "null" 
            }, 
            "globalIntermediaryIdentificationNumber": { 
              "type": "string" 
            }, 
            "kycAttestationType": { 
              "type": "string" 
            }, 
            "kycAttestationTypeId": { 
              "type": "null" 
            }, 
            "kycDateOfDeclaration": { 
              "type": "string" 
            }, 
            "kycDateOfDeclarationDateTime": { 
              "type": "null" 
            }, 
            "kycPlaceOfDeclaration": { 
              "type": "string" 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
165 



 

            }, 
            "kycVerificationDate": { 
              "type": "string" 
            }, 
            "kycVerificationDateDateTime": { 
              "type": "null" 
            }, 
            "kycEmployeeName": { 
              "type": "string" 
            }, 
            "kycEmployeeDesignation": { 
              "type": "string" 
            }, 
            "kycVerificationBranch": { 
              "type": "string" 
            }, 
            "kycEmployeeCode": { 
              "type": "string" 
            }, 
            "listed": { 
              "type": "string" 
            }, 
            "listedId": { 
              "type": "null" 
            }, 
            "applicationRefNumber": { 
              "type": "string" 
            }, 
            "documentRefNumber": { 
              "type": "string" 
            }, 
            "regAMLRisk": { 
              "type": "string" 
            }, 
            "regAMLRiskId": { 
              "type": "null" 
            }, 
            "regAMLRiskLastRiskReviewDate": { 
              "type": "string" 
            }, 
            "regAMLRiskLastRiskReviewDateDateTime": { 
              "type": "null" 
            }, 
            "regAMLRiskNextRiskReviewDate": { 
              "type": "string" 
            }, 
            "regAMLRiskNextRiskReviewDateDateTime": { 
              "type": "null" 
            }, 
            "incomeRange": { 
              "type": "string" 
            }, 
            "incomeRangeId": { 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
166 



 

              "type": "null" 
            }, 
            "exactIncome": { 
              "type": "null" 
            }, 
            "incomeCurrency": { 
              "type": "string" 
            }, 
            "incomeCurrencyId": { 
              "type": "null" 
            }, 
            "incomeEffectiveDate": { 
              "type": "string" 
            }, 
            "incomeEffectiveDateDateTime": { 
              "type": "null" 
            }, 
            "incomeDescription": { 
              "type": "string" 
            }, 
            "incomeDocument": { 
              "type": "string" 
            }, 
            "incomeDocumentId": { 
              "type": "null" 
            }, 
            "exactNetworth": { 
              "type": "null" 
            }, 
            "networthCurrency": { 
              "type": "string" 
            }, 
            "networthCurrencyId": { 
              "type": "null" 
            }, 
            "networthEffectiveDate": { 
              "type": "string" 
            }, 
            "networthEffectiveDateDateTime": { 
              "type": "null" 
            }, 
            "networthDescription": { 
              "type": "string" 
            }, 
            "networthDocument": { 
              "type": "string" 
            }, 
            "networthDocumentId": { 
              "type": "null" 
            }, 
            "familyCode": { 
              "type": "string" 
            }, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
167 



 

            "channel": { 
              "type": "string" 
            }, 
            "channelId": { 
              "type": "null" 
            }, 
            "contactPersonFirstName1": { 
              "type": "string" 
            }, 
            "contactPersonMiddleName1": { 
              "type": "string" 
            }, 
            "contactPersonLastName1": { 
              "type": "string" 
            }, 
            "contactPersonDesignation1": { 
              "type": "string" 
            }, 
            "contactPersonFirstName2": { 
              "type": "string" 
            }, 
            "contactPersonMiddleName2": { 
              "type": "string" 
            }, 
            "contactPersonLastName2": { 
              "type": "string" 
            }, 
            "contactPersonDesignation2": { 
              "type": "string" 
            }, 
            "contactPersonMobileISD": { 
              "type": "string" 
            }, 
            "contactPersonMobileNo": { 
              "type": "string" 
            }, 
            "contactPersonMobileISD2": { 
              "type": "string" 
            }, 
            "contactPersonMobileNo2": { 
              "type": "string" 
            }, 
            "contactPersonEmailId1": { 
              "type": "string" 
            }, 
            "contactPersonEmailId2": { 
              "type": "string" 
            }, 
            "commencementDate": { 
              "type": "string" 
            }, 
            "commencementDateDateTime": { 
              "type": "null" 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
168 



 

            }, 
            "maidenPrefix": { 
              "type": "string" 
            }, 
            "maidenPrefixId": { 
              "type": "null" 
            }, 
            "maidenFirstName": { 
              "type": "string" 
            }, 
            "maidenMiddleName": { 
              "type": "string" 
            }, 
            "maidenLastName": { 
              "type": "string" 
            }, 
            "relatedPersonCountforCKYC": { 
              "type": "integer" 
            }, 
            "proofOfIdSubmitted": { 
              "type": "string" 
            }, 
            "proofOfIdSubmittedId": { 
              "type": "null" 
            }, 
            "products": { 
              "type": "string" 
            }, 
            "productsId": { 
              "type": "null" 
            }, 
            "natureOfBusiness": { 
              "type": "string" 
            }, 
            "natureOfBusinessId": { 
              "type": "null" 
            }, 
            "educationalQualification": { 
              "type": "string" 
            }, 
            "educationalQualificationId": { 
              "type": "null" 
            }, 
            "countryOfOperations": { 
              "type": "string" 
            }, 
            "countryOfOperationsId": { 
              "type": "null" 
            }, 
            "personalMobileISD": { 
              "type": "string" 
            }, 
            "personalMobileNumber": { 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
169 



 

              "type": "string" 
            }, 
            "workMobileISD": { 
              "type": "string" 
            }, 
            "workMobileNumber": { 
              "type": "string" 
            }, 
            "regAMLRiskSpecialCategoryDtoList": { 
              "type": "array", 
              "items": [ 
                { 
                  "type": "object", 
                  "properties": { 
                    "Id": { 
                      "type": "null" 
                    }, 
                    "regAMLRiskSpecialCategory": { 
                      "type": "string" 
                    }, 
                    "regAMLRiskSpecialCategoryId": { 
                      "type": "null" 
                    }, 
                    "regAMLRiskSpecialCategoryStartDate": { 
                      "type": "string" 
                    }, 
                    "regAMLRiskSpecialCategoryStartDateDateTime": { 
                      "type": "null" 
                    } 
                  }, 
                  "required": [ 
                    "Id", 
                    "regAMLRiskSpecialCategory", 
                    "regAMLRiskSpecialCategoryId", 
                    "regAMLRiskSpecialCategoryStartDate", 
                    "regAMLRiskSpecialCategoryStartDateDateTime" 
                  ] 
                } 
              ] 
            }, 
            "relatedPersonList": { 
              "type": "array", 
              "items": {} 
            }, 
            "customerRelationDtoList": { 
              "type": "array", 
              "items": {} 
            }, 
            "constitutionType": { 
              "type": "string" 
            }, 
            "constitutionTypeId": { 
              "type": "integer" 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
170 



 

            }, 
            "sourceSystemName": { 
              "type": "string" 
            }, 
            "sourceSystemCustomerCode": { 
              "type": "string" 
            }, 
            "sourceSystemCustomerCreationDate": { 
              "type": "string" 
            }, 
            "sourceSystemCustomerCreationDateDateTime": { 
              "type": "null" 
            }, 
            "uniqueIdentifier": { 
              "type": "string" 
            }, 
            "SystemGeneratedId": { 
              "type": "null" 
            }, 
            "prefix": { 
              "type": "string" 
            }, 
            "prefixId": { 
              "type": "null" 
            }, 
            "firstName": { 
              "type": "string" 
            }, 
            "middleName": { 
              "type": "string" 
            }, 
            "lastName": { 
              "type": "string" 
            }, 
            "fatherPrefix": { 
              "type": "string" 
            }, 
            "fatherPrefixId": { 
              "type": "null" 
            }, 
            "fatherFirstName": { 
              "type": "string" 
            }, 
            "fatherMiddleName": { 
              "type": "string" 
            }, 
            "fatherLastName": { 
              "type": "string" 
            }, 
            "spousePrefix": { 
              "type": "string" 
            }, 
            "spousePrefixId": { 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
171 



 

              "type": "null" 
            }, 
            "spouseFirstName": { 
              "type": "string" 
            }, 
            "spouseMiddleName": { 
              "type": "string" 
            }, 
            "spouseLastName": { 
              "type": "string" 
            }, 
            "motherPrefix": { 
              "type": "string" 
            }, 
            "motherPrefixId": { 
              "type": "null" 
            }, 
            "motherFirstName": { 
              "type": "string" 
            }, 
            "motherMiddleName": { 
              "type": "string" 
            }, 
            "motherLastName": { 
              "type": "string" 
            }, 
            "gender": { 
              "type": "string" 
            }, 
            "genderId": { 
              "type": "null" 
            }, 
            "dateofBirth": { 
              "type": "string" 
            }, 
            "dateofBirthDateTime": { 
              "type": "null" 
            }, 
            "workEmail": { 
              "type": "string" 
            }, 
            "personalEmail": { 
              "type": "string" 
            }, 
            "permanentAddressCountry": { 
              "type": "string" 
            }, 
            "permanentAddressCountryId": { 
              "type": "null" 
            }, 
            "permanentAddressZipCode": { 
              "type": "string" 
            }, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
172 



 

            "permanentAddressZipCodeId": { 
              "type": "null" 
            }, 
            "permanentAddressLine1": { 
              "type": "string" 
            }, 
            "permanentAddressLine2": { 
              "type": "string" 
            }, 
            "permanentAddressLine3": { 
              "type": "string" 
            }, 
            "permanentAddressDistrict": { 
              "type": "string" 
            }, 
            "permanentAddressDistrictId": { 
              "type": "null" 
            }, 
            "permanentAddressCity": { 
              "type": "string" 
            }, 
            "permanentAddressCityId": { 
              "type": "null" 
            }, 
            "permanentAddressState": { 
              "type": "string" 
            }, 
            "permanentAddressOtherState": { 
              "type": "string" 
            }, 
            "permanentAddressStateId": { 
              "type": "null" 
            }, 
            "permanentAddressDocument": { 
              "type": "string" 
            }, 
            "permanentAddressDocumentId": { 
              "type": "null" 
            }, 
            "permanentAddressDocumentOthersValue": { 
              "type": "string" 
            }, 
            "correspondenceAddressCountry": { 
              "type": "string" 
            }, 
            "correspondenceAddressCountryId": { 
              "type": "null" 
            }, 
            "correspondenceAddressZipCode": { 
              "type": "string" 
            }, 
            "correspondenceAddressZipCodeId": { 
              "type": "null" 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
173 



 

            }, 
            "correspondenceAddressLine1": { 
              "type": "string" 
            }, 
            "correspondenceAddressLine2": { 
              "type": "string" 
            }, 
            "correspondenceAddressLine3": { 
              "type": "string" 
            }, 
            "correspondenceAddressDistrict": { 
              "type": "string" 
            }, 
            "correspondenceAddressDistrictId": { 
              "type": "null" 
            }, 
            "correspondenceAddressCity": { 
              "type": "string" 
            }, 
            "correspondenceAddressCityId": { 
              "type": "null" 
            }, 
            "correspondenceAddressState": { 
              "type": "string" 
            }, 
            "correspondenceAddressOtherState": { 
              "type": "string" 
            }, 
            "correspondenceAddressStateId": { 
              "type": "null" 
            }, 
            "correspondenceAddressDocument": { 
              "type": "string" 
            }, 
            "correspondenceAddressDocumentId": { 
              "type": "null" 
            }, 
            "countryOfResidence": { 
              "type": "string" 
            }, 
            "countryOfResidenceId": { 
              "type": "null" 
            }, 
            "countryOfBirth": { 
              "type": "string" 
            }, 
            "countryOfBirthId": { 
              "type": "null" 
            }, 
            "birthCity": { 
              "type": "string" 
            }, 
            "birthCityId": { 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
174 



 

              "type": "null" 
            }, 
            "passportIssueCountry": { 
              "type": "string" 
            }, 
            "passportIssueCountryId": { 
              "type": "null" 
            }, 
            "passportNumber": { 
              "type": "string" 
            }, 
            "passportExpiryDate": { 
              "type": "string" 
            }, 
            "passportExpiryDateDateTime": { 
              "type": "null" 
            }, 
            "voterIdNumber": { 
              "type": "string" 
            }, 
            "drivingLicenseNumber": { 
              "type": "string" 
            }, 
            "drivingLicenseExpiryDate": { 
              "type": "string" 
            }, 
            "drivingLicenseExpiryDateDateTime": { 
              "type": "null" 
            }, 
            "aadhaarNumber": { 
              "type": "string" 
            }, 
            "aadhaarVaultReferenceNumber": { 
              "type": "string" 
            }, 
            "nregaNumber": { 
              "type": "string" 
            }, 
            "nprLetterNumber": { 
              "type": "string" 
            }, 
            "directorIdentificationNumber": { 
              "type": "string" 
            }, 
            "formSixty": { 
              "type": "string" 
            }, 
            "formSixtyId": { 
              "type": "null" 
            }, 
            "pan": { 
              "type": "string" 
            }, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
175 



 

            "ckycNumber": { 
              "type": "string" 
            }, 
            "identityDocument": { 
              "type": "null" 
            }, 
            "identityDocumentId": { 
              "type": "null" 
            }, 
            "politicallyExposed": { 
              "type": "string" 
            }, 
            "politicallyExposedId": { 
              "type": "null" 
            }, 
            "adverseReputationstring": { 
              "type": "null" 
            }, 
            "adverseReputationDetails": { 
              "type": "string" 
            }, 
            "notes": { 
              "type": "string" 
            }, 
            "tags": { 
              "type": "string" 
            }, 
            "tagsId": { 
              "type": "null" 
            }, 
            "screeningProfile": { 
              "type": "string" 
            }, 
            "screeningReportWhenNil": { 
              "type": "string" 
            }, 
            "screeningReportWhenNilId": { 
              "type": "null" 
            }, 
            "riskProfile": { 
              "type": "null" 
            }, 
            "adverseReputation": { 
              "type": "string" 
            }, 
            "adverseReputationId": { 
              "type": "null" 
            }, 
            "adverseReputationClassification": { 
              "type": "string" 
            }, 
            "adverseReputationClassificationId": { 
              "type": "null" 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
176 



 

            }, 
            "taxDetailDtoList": { 
              "type": "array", 
              "items": [ 
                { 
                  "type": "object", 
                  "properties": { 
                    "Id": { 
                      "type": "integer" 
                    }, 
                    "taxResidencyCountry": { 
                      "type": "string" 
                    }, 
                    "taxResidencyCountryId": { 
                      "type": "null" 
                    }, 
                    "taxIdentificationNumber": { 
                      "type": "string" 
                    }, 
                    "taxResidencyStartDate": { 
                      "type": "string" 
                    }, 
                    "taxResidencyStartDateDateTime": { 
                      "type": "null" 
                    }, 
                    "taxResidencyEndDate": { 
                      "type": "string" 
                    }, 
                    "taxResidencyEndDateDateTime": { 
                      "type": "null" 
                    } 
                  }, 
                  "required": [ 
                    "Id", 
                    "taxResidencyCountry", 
                    "taxResidencyCountryId", 
                    "taxIdentificationNumber", 
                    "taxResidencyStartDate", 
                    "taxResidencyStartDateDateTime", 
                    "taxResidencyEndDate", 
                    "taxResidencyEndDateDateTime" 
                  ] 
                } 
              ] 
            }, 
            "politicallyExposedClassification": { 
              "type": "string" 
            }, 
            "politicallyExposedClassificationId": { 
              "type": "null" 
            }, 
            "citizenships": { 
              "type": "string" 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
177 



 

            }, 
            "citizenshipsId": { 
              "type": "null" 
            }, 
            "nationalities": { 
              "type": "string" 
            }, 
            "nationalitiesId": { 
              "type": "null" 
            }, 
            "documents": { 
              "type": "null" 
            } 
          }, 
          "required": [ 
            "ekycOTPbased", 
            "ekycOTPbasedId", 
            "segment", 
            "segmentId", 
            "segmentStartDate", 
            "segmentStartDateDateTime", 
            "status", 
            "statusId", 
            "effectiveDate", 
            "effectiveDateDateTime", 
            "minor", 
            "minorId", 
            "maritalStatus", 
            "maritalStatusId", 
            "occupationType", 
            "occupationTypeOther", 
            "occupationTypeId", 
            "natureOfBusinessOther", 
            "companyIdentificationNumber", 
            "companyRegistrationNumber", 
            "companyRegistrationCountry", 
            "companyRegistrationCountryId", 
            "globalIntermediaryIdentificationNumber", 
            "kycAttestationType", 
            "kycAttestationTypeId", 
            "kycDateOfDeclaration", 
            "kycDateOfDeclarationDateTime", 
            "kycPlaceOfDeclaration", 
            "kycVerificationDate", 
            "kycVerificationDateDateTime", 
            "kycEmployeeName", 
            "kycEmployeeDesignation", 
            "kycVerificationBranch", 
            "kycEmployeeCode", 
            "listed", 
            "listedId", 
            "applicationRefNumber", 
            "documentRefNumber", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
178 



 

            "regAMLRisk", 
            "regAMLRiskId", 
            "regAMLRiskLastRiskReviewDate", 
            "regAMLRiskLastRiskReviewDateDateTime", 
            "regAMLRiskNextRiskReviewDate", 
            "regAMLRiskNextRiskReviewDateDateTime", 
            "incomeRange", 
            "incomeRangeId", 
            "exactIncome", 
            "incomeCurrency", 
            "incomeCurrencyId", 
            "incomeEffectiveDate", 
            "incomeEffectiveDateDateTime", 
            "incomeDescription", 
            "incomeDocument", 
            "incomeDocumentId", 
            "exactNetworth", 
            "networthCurrency", 
            "networthCurrencyId", 
            "networthEffectiveDate", 
            "networthEffectiveDateDateTime", 
            "networthDescription", 
            "networthDocument", 
            "networthDocumentId", 
            "familyCode", 
            "channel", 
            "channelId", 
            "contactPersonFirstName1", 
            "contactPersonMiddleName1", 
            "contactPersonLastName1", 
            "contactPersonDesignation1", 
            "contactPersonFirstName2", 
            "contactPersonMiddleName2", 
            "contactPersonLastName2", 
            "contactPersonDesignation2", 
            "contactPersonMobileISD", 
            "contactPersonMobileNo", 
            "contactPersonMobileISD2", 
            "contactPersonMobileNo2", 
            "contactPersonEmailId1", 
            "contactPersonEmailId2", 
            "commencementDate", 
            "commencementDateDateTime", 
            "maidenPrefix", 
            "maidenPrefixId", 
            "maidenFirstName", 
            "maidenMiddleName", 
            "maidenLastName", 
            "relatedPersonCountforCKYC", 
            "proofOfIdSubmitted", 
            "proofOfIdSubmittedId", 
            "products", 
            "productsId", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
179 



 

            "natureOfBusiness", 
            "natureOfBusinessId", 
            "educationalQualification", 
            "educationalQualificationId", 
            "countryOfOperations", 
            "countryOfOperationsId", 
            "personalMobileISD", 
            "personalMobileNumber", 
            "workMobileISD", 
            "workMobileNumber", 
            "regAMLRiskSpecialCategoryDtoList", 
            "relatedPersonList", 
            "customerRelationDtoList", 
            "constitutionType", 
            "constitutionTypeId", 
            "sourceSystemName", 
            "sourceSystemCustomerCode", 
            "sourceSystemCustomerCreationDate", 
            "sourceSystemCustomerCreationDateDateTime", 
            "uniqueIdentifier", 
            "SystemGeneratedId", 
            "prefix", 
            "prefixId", 
            "firstName", 
            "middleName", 
            "lastName", 
            "fatherPrefix", 
            "fatherPrefixId", 
            "fatherFirstName", 
            "fatherMiddleName", 
            "fatherLastName", 
            "spousePrefix", 
            "spousePrefixId", 
            "spouseFirstName", 
            "spouseMiddleName", 
            "spouseLastName", 
            "motherPrefix", 
            "motherPrefixId", 
            "motherFirstName", 
            "motherMiddleName", 
            "motherLastName", 
            "gender", 
            "genderId", 
            "dateofBirth", 
            "dateofBirthDateTime", 
            "workEmail", 
            "personalEmail", 
            "permanentAddressCountry", 
            "permanentAddressCountryId", 
            "permanentAddressZipCode", 
            "permanentAddressZipCodeId", 
            "permanentAddressLine1", 
            "permanentAddressLine2", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
180 



 

            "permanentAddressLine3", 
            "permanentAddressDistrict", 
            "permanentAddressDistrictId", 
            "permanentAddressCity", 
            "permanentAddressCityId", 
            "permanentAddressState", 
            "permanentAddressOtherState", 
            "permanentAddressStateId", 
            "permanentAddressDocument", 
            "permanentAddressDocumentId", 
            "permanentAddressDocumentOthersValue", 
            "correspondenceAddressCountry", 
            "correspondenceAddressCountryId", 
            "correspondenceAddressZipCode", 
            "correspondenceAddressZipCodeId", 
            "correspondenceAddressLine1", 
            "correspondenceAddressLine2", 
            "correspondenceAddressLine3", 
            "correspondenceAddressDistrict", 
            "correspondenceAddressDistrictId", 
            "correspondenceAddressCity", 
            "correspondenceAddressCityId", 
            "correspondenceAddressState", 
            "correspondenceAddressOtherState", 
            "correspondenceAddressStateId", 
            "correspondenceAddressDocument", 
            "correspondenceAddressDocumentId", 
            "countryOfResidence", 
            "countryOfResidenceId", 
            "countryOfBirth", 
            "countryOfBirthId", 
            "birthCity", 
            "birthCityId", 
            "passportIssueCountry", 
            "passportIssueCountryId", 
            "passportNumber", 
            "passportExpiryDate", 
            "passportExpiryDateDateTime", 
            "voterIdNumber", 
            "drivingLicenseNumber", 
            "drivingLicenseExpiryDate", 
            "drivingLicenseExpiryDateDateTime", 
            "aadhaarNumber", 
            "aadhaarVaultReferenceNumber", 
            "nregaNumber", 
            "nprLetterNumber", 
            "directorIdentificationNumber", 
            "formSixty", 
            "formSixtyId", 
            "pan", 
            "ckycNumber", 
            "identityDocument", 
            "identityDocumentId", 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
181 



 

            "politicallyExposed", 
            "politicallyExposedId", 
            "adverseReputationstring", 
            "adverseReputationDetails", 
            "notes", 
            "tags", 
            "tagsId", 
            "screeningProfile", 
            "screeningReportWhenNil", 
            "screeningReportWhenNilId", 
            "riskProfile", 
            "adverseReputation", 
            "adverseReputationId", 
            "adverseReputationClassification", 
            "adverseReputationClassificationId", 
            "taxDetailDtoList", 
            "politicallyExposedClassification", 
            "politicallyExposedClassificationId", 
            "citizenships", 
            "citizenshipsId", 
            "nationalities", 
            "nationalitiesId", 
            "documents" 
          ] 
        } 
      ] 
    } 
  }, 
  "required": [ 
    "requestId", 
    "sourceSystemName", 
    "purpose", 
    "customerList" 
  ] 
} 

8.2 Response XSD (Purpose 01) 
{ 
  "$schema": "http://json-schema.org/draft-04/schema#", 
  "type": "object", 
  "properties": { 
    "RequestId": { 
      "type": "string" 
    }, 
    "ValidationCode": { 
      "type": "null" 
    }, 
    "ValidationDescription": { 
      "type": "null" 
    }, 
    "DecryptedData": { 
      "type": "object", 
      "properties": { 
        "OverallStatus": { 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
182 



 

          "type": "string" 
        }, 
        "CustomerResponse": { 
          "type": "array", 
          "items": [ 
            { 
              "type": "object", 
              "properties": { 
                "SourceSystemCustomerCode": { 
                  "type": "string" 
                }, 
                "ValidationOutcome": { 
                  "type": "string" 
                }, 
                "PurposeResponse": { 
                  "type": "array", 
                  "items": [ 
                    { 
                      "type": "object", 
                      "properties": { 
                        "Purpose": { 
                          "type": "string" 
                        }, 
                        "PurposeCode": { 
                          "type": "string" 
                        }, 
                        "Data": { 
                          "type": "object", 
                          "properties": { 
                            "SuggestedAction": { 
                              "type": "string" 
                            }, 
                            "HitsDetected": { 
                              "type": "string" 
                            }, 
                            "HitsCount": { 
                              "type": "integer" 
                            }, 
                            "ConfirmedHits": { 
                              "type": "string" 
                            }, 
                            "ReportData": { 
                              "type": "string" 
                            }, 
                            "HitResponse": { 
                              "type": "array", 
                              "items": [ 
                                { 
                                  "type": "object", 
                                  "properties": { 
                                    "Source": { 
                                      "type": "string" 
                                    }, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
183 



 

                                    "WatchlistSourceId": { 
                                      "type": "string" 
                                    }, 
                                    "MatchType": { 
                                      "type": "string" 
                                    }, 
                                    "Score": { 
                                      "type": "number" 
                                    }, 
                                    "ConfirmedMatchingAttributes": { 
                                      "type": "string" 
                                    } 
                                  }, 
                                  "required": [ 
                                    "Source", 
                                    "WatchlistSourceId", 
                                    "MatchType", 
                                    "Score", 
                                    "ConfirmedMatchingAttributes" 
                                  ] 
                                } 
                              ] 
                            } 
                          }, 
                          "required": [ 
                            "SuggestedAction", 
                            "HitsDetected", 
                            "HitsCount", 
                            "ConfirmedHits", 
                            "ReportData", 
                            "HitResponse" 
                          ] 
                        }, 
                        "ValidationCode": { 
                          "type": "string" 
                        }, 
                        "ValidationDescription": { 
                          "type": "string" 
                        }, 
                        "ValidationFailureCount": { 
                          "type": "integer" 
                        } 
                      }, 
                      "required": [ 
                        "Purpose", 
                        "PurposeCode", 
                        "Data", 
                        "ValidationCode", 
                        "ValidationDescription", 
                        "ValidationFailureCount" 
                      ] 
                    } 
                  ] 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
184 



 

                }, 
                "RelatedPersonResponse": { 
                  "type": "null" 
                }, 
                "RelatedPersonRelationResponse": { 
                  "type": "null" 
                } 
              }, 
              "required": [ 
                "SourceSystemCustomerCode", 
                "ValidationOutcome", 
                "PurposeResponse", 
                "RelatedPersonResponse", 
                "RelatedPersonRelationResponse" 
              ] 
            } 
          ] 
        } 
      }, 
      "required": [ 
        "OverallStatus", 
        "CustomerResponse" 
      ] 
    } 
  }, 
  "required": [ 
    "RequestId", 
    "ValidationCode", 
    "ValidationDescription", 
    "DecryptedData" 
  ] 
} 
 

8.3 Response XSD (Purpose 04) 
 
 
{ 
  "$schema": "http://json-schema.org/draft-04/schema#", 
  "type": "object", 
  "properties": { 
    "RequestId": { 
      "type": "string" 
    }, 
    "ValidationCode": { 
      "type": "null" 
    }, 
    "ValidationDescription": { 
      "type": "null" 
    }, 
    "DecryptedData": { 
      "type": "object", 
      "properties": { 
        "OverallStatus": { 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
185 



 

          "type": "string" 
        }, 
        "CustomerResponse": { 
          "type": "array", 
          "items": [ 
            { 
              "type": "object", 
              "properties": { 
                "SourceSystemCustomerCode": { 
                  "type": "string" 
                }, 
                "ValidationOutcome": { 
                  "type": "string" 
                }, 
                "PurposeResponse": { 
                  "type": "array", 
                  "items": [ 
                    { 
                      "type": "object", 
                      "properties": { 
                        "Purpose": { 
                          "type": "string" 
                        }, 
                        "PurposeCode": { 
                          "type": "string" 
                        }, 
                        "Data": { 
                          "type": "null" 
                        }, 
                        "ValidationCode": { 
                          "type": "string" 
                        }, 
                        "ValidationDescription": { 
                          "type": "string" 
                        }, 
                        "ValidationFailureCount": { 
                          "type": "integer" 
                        } 
                      }, 
                      "required": [ 
                        "Purpose", 
                        "PurposeCode", 
                        "Data", 
                        "ValidationCode", 
                        "ValidationDescription", 
                        "ValidationFailureCount" 
                      ] 
                    } 
                  ] 
                }, 
                "RelatedPersonResponse": { 
                  "type": "null" 
                }, 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
186 



 

                "RelatedPersonRelationResponse": { 
                  "type": "null" 
                } 
              }, 
              "required": [ 
                "SourceSystemCustomerCode", 
                "ValidationOutcome", 
                "PurposeResponse", 
                "RelatedPersonResponse", 
                "RelatedPersonRelationResponse" 
              ] 
            } 
          ] 
        } 
      }, 
      "required": [ 
        "OverallStatus", 
        "CustomerResponse" 
      ] 
    } 
  }, 
  "required": [ 
    "RequestId", 
    "ValidationCode", 
    "ValidationDescription", 
    "DecryptedData" 
  ] 
} 
  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
187 



 

 

9. Validations  
9.1 Main Request Validations: 

 
 

Validation Code Description 

MRV1 API Token is mandatory 

MRV2 Invalid API Token or API Token is not recognized. 

MRV3 The User does not have access to API in Subscription Master 

MRV4 API Subscription is not proper in API token. 

MRV5 API Subscription details not found in API token. 

MRV6 Encryption Details are not configured properly 

MRV7 Purpose 01, 02 and 03 together is not allowed in one single request 

MRV8 Purpose 04 and 05 together is not allowed in one single request 

MRV9 Validations are not configured for purpose "X" 

MRV10 SourceSystemName is mandatory 

MRV11 SourceSystemCustomerCode is mandatory 

MRV12 At least one purpose should be passed 

MRV13 Purpose Code passed is unavailable 

MRV14 ConstitutionType is mandatory 

MRV15 Agency in API token is not matching with Agency in Header Request 

 RelatedPersonSourceSystemCode, CustomerSourceSystemCode and 
 RelationCode in Relations are mandatory if 
MRV16 SourceSystemCustomerCode is passed in Related Person 

 RelatedPersonSourceSystemCode in Relations should match with 
MRV17 SourceSystemCustomerCode of Related Person 

MRV18 
SourceSystemCustomerCode in Relations should match with 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
188 



 

 SourceSystemCustomerCode of Customer 

MRV19 RelatedPerson SourceSystemCustomerCode is mandatory 

MRV20 ConstitutionType of RelatedPerson should be as per specified values 

MRV21 ConstitutionType of RelatedPerson is mandatory 

MRV22 ConstitutionType of Customer should be as per specified values 

MRV23 Symmetric key for publishing to Kafka not found 

MRV24 An Unexpected Error Occurred <exception>. Contact TSS Support 

 
 
 
 

9.2 Request Data Validations: 
 
 

    
Validation Type Validation Code Validation Description Examples 

    

   For ex : The field “First 
   Name” is a Mandatory 
   field and if the user 
  If the user does not input leaves it blank the user 
  

anything in a required field will receive a validation 
  then Mandatory validation as "FirstName-M” is 

M 
Mandatory will occur mandatory 

    

   For ex : The field “PAN” 

   minimum length defined is 1 

  and maximum length 
If the user does not input the defined is 12 and if the user 

  
  data as specified in inputs 16 characters, then 

  Minimum length or exceeds the user will get the 
following validation 

  the value beyond the defined 
message "PAN-Max-L"- 

  maximum length,Minimum Length should not be 
MinLength Min-L or Maximum Length greater than 12 

Max-L 
MaxLength validation will occur. characters 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
189 



 

    

   For ex: The permitted 
   allowed characters in 
   VoterIdNumber is / 
   [ ] { } ( ) - but the user 
   

inputs special 
   

characters &*% in the 
   
  data then the user will 

If the user inputs any special 
  get the following 
  character in the data other validation 
  than the Special characters  
  allowed, then Special “VoterIdNumber-SC"- 
  Characters validation will Only Specific 

SC 
SpecialCharacters occur. Characters are allowed 

    

   For ex : The field 
   DateofBirth does not 
   accept a future date, if 
   the user inputs a date 
   

such as 09-Jun-2027” 
   

the user will get the 
   
  following validation 

If the user inputs a future 
  message. 
  date value in a field which 

  doesn't accept a Future date , “DateofBirth -FDV"- 

 then Future Date validation Future Date or Current 
FDV 

FutureDateValue will occur. Date is not allowed 
    

   For ex : Format for the 
   field PAN is First 5 
   characters as 
   alphabets, 4th 
   character as 'P' and last 
   

character is an 
   
   alphabet, if the user 

  If the user does not input the does not input the data 
  data in a specified format , according to the 

  Format Check validation will specified format he will 
FOR 

FormatCheck occur. get the following 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
190 



 

   validation message 
“PAN-FOR”-First 5 
characters as 
alphabets, 4th 
character as 'P' and 
last character is an 
alphabet. 

    

   For ex : The field 
   SourceSystemName 
   will only accept values 
   from the Agency Enum, 
   

if the user inputs values 
   

other than the values 
   
   these, the user will get 

   the following validation 
   message 
  If the user inputs data that’s  
  not according to the list of "SourceSystemName- 
  specific values mentioned, SV"-Value passed 
  Specified Values validation should be as per 

SV 
SpecifiedValues will occur. specified values only 

    

   For ex: The field 
   DateofBirth does not 
   accept a future date, if 
   the user inputs a date 
   

such as 09-Jun-2027” 
   

the user will get the 
   
  following validation 

If the user inputs a past date 
  message 
  value in a field which doesn't  

  accept a Past date the user “DateofBirth -PDV"- 

 will get a Past Date Past Date or Current 
PDV 

PastDateValue Validation. Date is not allowed 
 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
191 



 

9.3 Relational Validations 
9.3.1. Applicable for Screening 

 
 
 

  Validation Validation Applicable For 
Validation Code Validation Name Message Message (IND/LE/RP) 

     
  PAN/FormSixty - PAN/FormSixty -  
  VS1 - Either value VS1 - Either value  
  to be provided in to be provided in  
VS1 

PAN and FormSixty PAN or FormSixty. PAN or FormSixty. IND,LE & RP 
     
  FirstName & Prefix FirstName & Prefix-  
  - VS5 - If Prefix has VS5 - If Prefix has  
  been provided then been provided then  
  FirstName is FirstName is  
VS5 

FirstName & Prefix mandatory. mandatory. IND & RP 
     
  FirstName & Prefix FirstName & Prefix  
  - VS6 - If FirstName - VS6 - If FirstName  
  is provided then is provided then  
  Prefix is Prefix is  
VS6 

Prefix & FirstName mandatory. mandatory. IND & RP 
     
  FormSixty/Pan - FormSixty/Pan -  
  VS18 - FormSixty VS18 - FormSixty  
  should be Yes if should be Yes if  
  PAN is not PAN is not  
VS18 

FormSixty/Pan provided. provided. IND, LE & RP 
     
  Pan/FormSixty - Pan/FormSixty -  
  VS19 - FormSixty VS19 - FormSixty  
  should be No if PAN should be No if PAN  
VS19 

Pan/FormSixty is provided. is provided. IND, LE & RP 
     
  Kindly pass value in "Mandatory fields  
  one of these fields: for Screening-  
 Mandatory fields Name, Mobile IND"-VS45-Kindly  
VS45 

for Screening- IND Number, Email ID, pass value in one of IND 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
192 



 

  PAN, Passport, DIN these fields: Name,  

or Driving License Mobile Number, 
Number Email ID, PAN, 

Passport, DIN or 
Driving License 
Number 

   "Mandatory fields  
   for Screening-  
  Kindly pass value in IND"-VS46-Kindly  
  one of these fields: pass value in one of  
  Name, PAN, CIN, these fields: Name,  

 Mandatory fields Mobile Number or PAN, CIN, Mobile  
VS46 for Screening- LE Email-Id Number or Email-Id LE 

 
 
 

9.3.2. General Validations 
 

     
    Applicab 
Valid    le For 
ation    (IND/LE/ 
Code Validation Name Validation Message Validation Message RP) 
     
  IncomeEffectiveDate - VS3 - IncomeEffectiveDate - VS3 -  
  IncomeEffectiveDate is IncomeEffectiveDate is  
  mandatory if ExactIncome is mandatory if ExactIncome is  
VS3 IncomeEffectiveDate provided. provided. IND & LE 
     
  FatherPrefix & FatherPrefix &  
  FatherFirstName - VS7 - If FatherFirstName - VS7 - If  
  Father prefix has been Father prefix has been  
  provided then provided then  
 FatherPrefix & FatherFirstName is FatherFirstName is  
VS7 FatherFirstName mandatory. mandatory. IND & RP 
     
  FatherFirstName & FatherFirstName &  
  FatherPrefix - VS8- If FatherPrefix - VS8 - If  
  FatherFirstName is provided FatherFirstName is provided  
 FatherFirstName & then Father prefix is then Father prefix is  
VS8 FatherPrefix mandatory mandatory IND & RP 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
193 



 

     
  SpousePrefix & SpousePrefix &  
  SpouseFirstName - VS9 - If SpouseFirstName - VS9 - If  
  SpousePrefix has been SpousePrefix has been  
  provided, then provided, then  
 SpousePrefix & SpouseFirstName is SpouseFirstName is  
VS9 SpouseFirstName mandatory. mandatory. IND & RP 
     
  SpouseFirstName & SpouseFirstName &  
  SpousePrefix - VS10 - If SpousePrefix - VS10 - If  
  SpouseFirstName is provided SpouseFirstName is provided  
 SpouseFirstName & then SpousePrefix is then SpousePrefix is  
VS10 SpousePrefix mandatory. mandatory. IND & RP 
     
  MaidenPrefix & MaidenPrefix &  
  MaidenFirstName - VS11 - If MaidenFirstName - VS11 - If  
  MaidenPrefix has been MaidenPrefix has been  
  provided, then provided, then  
 MaidenPrefix & MaidenFirstName is MaidenFirstName is  
VS11 MaidenFirstName mandatory. mandatory. IND & RP 
     
  MotherPrefix & MotherPrefix &  
  MotherFirstName - VS12 - If MotherFirstName - VS12 - If  
  MotherPrefix has been MotherPrefix has been  
  provided, then provided, then  
 MotherPrefix & MotherFirstName is MotherFirstName is  
VS12 MotherFirstName mandatory. mandatory. IND & RP 
     
  KYCAttestationType & KYCAttestationType &  
  IsEKYCOTPBased - VS13 - IsEKYCOTPBased - VS13 -  
  KYCAttestationType is invalid KYCAttestationType is invalid  
 KYCAttestationType & as value passed in as value passed in  
VS13 IsEKYCOTPBased EKYCOTPbased is Yes. EKYCOTPbased is Yes. IND 
     
  PersonalMobileNumber & PersonalMobileNumber &  
  ISD - VS14 - ISD - VS14 -  
  PersonalMobileNumber PersonalMobileNumber  
  should not be Less than or should not be Less than or  
 PersonalMobileNumber & greater than 10 digits if greater than 10 digits if IND, LE 
VS14 PersonalMobileNumberISD PersonalMobileISD = 91. PersonalMobileISD = 91. & RP 
     
 WorkMobileNumber & WorkMobileNumber & ISD - WorkMobileNumber & ISD - IND, LE 
VS15 WorkMobileNumberISD VS15 - Length of the VS15 - Length of the & RP 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
194 



 

  WorkMobileNumber cannot WorkMobileNumber cannot  

be more than 20 digits for be more than 20 digits for 
country other than India. country other than India. 

     
   CorrespondenceAddressLine  
   1 - VS16 - If  
  CorrespondenceAddressLine CorrespondenceAddressLine  
  1 and Pin is empty 1 and Pin is empty then  
  then ContactPersonEmailId1 ContactPersonEmailId1 or  
  or ContactPersonEmailId2 or ContactPersonEmailId2 or  
  ContactPersonMobileNumbe ContactPersonMobileNumbe  
 CorrespondenceAddressLine r1 or r1 or  
 1, ContactPersonMobileNumbe ContactPersonMobileNumbe  
 CorrespondenceAddressPin, r2 r2 any of these are  
VS16 Email, MobileNumber any of these are Mandatory. Mandatory. LE 
     
  PermanentAddressLine1 - PermanentAddressLine1 -  
  VS20 - If permanent address VS20 - If permanent address  
  is passed, AddressLine1 and is passed, AddressLine1 and IND, LE 
VS20 PermanentAddressLine1 PIN is mandatory. PIN is mandatory. & RP 
     
  CorrespondenceAddressLine CorrespondenceAddressLine  
  1 - VS21 - If Correspondence 1 - VS21 - If Correspondence  
  address is passed, address is passed,  
 CorrespondenceAddressLine AddressLine1 and PIN is AddressLine1 and PIN is IND, LE 
VS21 1 mandatory. mandatory. & RP 
     
  PermanentAddressDocument PermanentAddressDocument  
  - VS22 - ProofOfIDSubmitted - VS22 - ProofOfIDSubmitted  
 PermanentAddressDocumen provided not matching with provided not matching with IND, LE 
VS22 t PermanentAddressDocument PermanentAddressDocument & RP 
     
     
     
     
  EKYCOTPBased &IDProof - EKYCOTPBased &IDProof -  
  VS23 - If EKYC OTP Based is VS23 - If EKYC OTP Based is  
 EKYCOTPBased & ID Proof Yes then ProofOfID submited Yes then ProofOfID submited IND, LE 
VS23 Aadhar Card should be AadharCard. should be AadharCard. & RP 
     
  ExactIncome & IncomeRange ExactIncome & IncomeRange  
  - VS24 - ExactIncome should - VS24 - ExactIncome should IND, LE 
VS24 ExactIncome & IncomeRange be mandatory when be mandatory when & RP 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
195 



 

  IncomeRange IncomeRange  

is provided is provided 
     
  OccupationType & OccupationType &  
  OccupationTypeOther - VS25 OccupationTypeOther - VS25  
  -If OccupationType is Others -If OccupationType is Others  
 OccuptationType & then OccuptationTypeOther then OccuptationTypeOther IND, LE 
VS25 OccupationTypeOther is required is required & RP 
     
  NatureOfBusiness & NatureOfBusiness &  
  NatureOfBusinessOthers - NatureOfBusinessOthers -  
  VS30 - VS30 -  
  NatureOfBusinessOthers is NatureOfBusinessOthers is  
 NatureOfBusiness & mandatory if mandatory if IND, LE 
VS30 NatureOfBusinessOthers NatureOfBusiness is Others NatureOfBusiness is Others & RP 
     
  CorrespondenceAddressLine CorrespondenceAddressLine  
 CorrespondenceAddressLine 1 is passed then 1 is passed then  
 1 & PermanentAddressLine1 is PermanentAddressLine1 is IND, LE 
VS31 PermanentAddressLine1 mandatory mandatory & RP 
     
  AdverseReputation&Adverse AdverseReputation&Adverse  
  ReputationClassification- ReputationClassification-  
  VS32 - If AdverseReputation VS32 - If AdverseReputation  
  value is "No" then do not value is "No" then do not  
  accept value in accept value in  
  AdverseReputationClassificati AdverseReputationClassificati  
 AdverseReputation&Adverse on and on and  
VS32 ReputationClassification AdverseReputationDetails AdverseReputationDetails IND 
     
  PEP&PEPClassification-VS33- PEP&PEPClassification-VS33-  
  If PEP value is "NotAPEP" If PEP value is "NotAPEP"  
  then do not accept values in then do not accept values in  
VS33 PEP &PEPClassification PEPClassification PEPClassification IND 
     
  DIN should be available in the DIN - VS2 - DIN should be  
  Identification details section available in the Identification  
  with ID Type as Director Id details section with ID Type  
  Number for related party as Director Id Number for  
  with relationship as related party with Related 
VS2 DIN Directors. relationship as Directors. Person 

 
  

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
196 



 

 

     
   IncomeRange  
   IncomeExactValue  
   IncomeCurrency  
   IncomeEffectiveDate  
   IncomeDescription  
 IncomeRange  IncomeDocumentValue-VS36  
 IncomeExactValue Value under -Value under  
 IncomeCurrency IncomeExactValue IncomeExactValue  
 IncomeEffectiveDate IncomeCurrency & IncomeCurrency &  
 IncomeDescription IncomeEffectiveDate is IncomeEffectiveDate is IND,LE& 
VS36 IncomeDocument Mandatory Mandatory RP 
     
   ExactNetworth  
   NetworthCurrency  
   NetworthEffectiveDate  
   NetworthDescription  
 ExactNetworth  NetworthDocument-VS37-  
 NetworthCurrency Value under ExactNetworth, Value under ExactNetworth,  
 NetworthEffectiveDate NetworthCurrency and NetworthCurrency and  
 NetworthDescription NetworthEffectiveDate is NetworthEffectiveDate is IND,LE& 
V37 NetworthDocument Mandatory Mandatory RP 
     
   TaxResidencyCountry  
   TaxIdentificationNumber  
   TaxResidencyStartDate  
   TaxResidencyLastDate -VS38-  
  Value under Value under  
 TaxResidencyCountry TaxIdentificationNumber, TaxIdentificationNumber,  
 TaxIdentificationNumber TaxResidencyCountry & TaxResidencyCountry &  

 TaxResidencyStartDate TaxResidencyStartDate is TaxResidencyStartDate is IND , LE 
V38 TaxResidencyLastDate Mandatory Mandatory &RP 
     
   RegAMLSpecialCategory  
   RegAMLSpecialCategoryStart  
  Value under Date-VS39-Value under  
 RegAMLSpecialCategory RegAMLSpecialCategory & RegAMLSpecialCategory &  
 RegAMLSpecialCategoryStart RegAMLSpecialCategoryStart RegAMLSpecialCategoryStart IND , LE 
V39 Date Date is Mandatory Date is Mandatory &RP 
     
 RegAML Value under RegAML & RegAML  
 RegAMLLastReviewDate(Effe RegAMLLastReviewDate is RegAMLLastReviewDate(Effe IND , LE 
V40 ctiveDate) Mandatory ctiveDate)-VS40- Value under &RP 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
197 



 

   RegAML &  

RegAMLLastReviewDate is 
Mandatory 

     
   Segment StartDate-VS41-  
 Segment Value under Segment & Value under Segment & IND , LE 
V41 StartDate StartDate is Mandatory StartDate is Mandatory &RP 
     
   Status EffectiveDate-VS42-  
 Status Value under Status & Value under Status & IND , LE 
V42 EffectiveDate EffectiveDate is Mandatory EffectiveDate is Mandatory &RP 
     
   PersonalMobileISD &  
   PersonalMobileNumber-  
  Value under VS43- Value under  
  PersonalMobileISD and PersonalMobileISD and  

 PersonalMobileISD & PersonalMobileNumber is PersonalMobileNumber is IND , LE 
V43 PersonalMobileNumber mandatory mandatory &RP 
     
   WorkMobileISD&  
   WorkMobileNumber-VS44-  
   Value under  
   CorrespondanceMobileISD  
  Value under WorkMobileISD and  

 WorkMobileISD& and WorkMobileNumberis CorrespondanceMobileNumb IND , LE 
V44 WorkMobileNumber mandatory er is mandatory &RP 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
198 



 

 

Email :<EMAIL> 
 

Tel : +91-22–6551 4191 / 92 
199