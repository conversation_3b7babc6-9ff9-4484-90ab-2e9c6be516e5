syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";


option go_package = "github.com/epifi/gamma/api/vendors/lenden";
option java_package = "com.github.epifi.gamma.api.vendors.lenden";

message InitMandateRequestPayload {
  string user_id = 1 [json_name = "user_id"];
  string loan_id = 2 [json_name = "loan_id"];
  string product_id = 3 [json_name = "product_id"];
  string type = 4 [json_name = "type"];
  ConsentDataRequest consent_data = 5 [json_name = "consent_data"];
}

message InitMandateRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  InitMandateRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message InitMandateResponseWrapper {
  string message = 1 [json_name = "message"];
  ResponseData response = 2 [json_name = "response"];
  message ResponseData {
    string trace_id = 1 [json_name = "trace_id"];
    string message_code = 2 [json_name = "message_code"];
    string message = 3 [json_name = "message"];
    InitMandateResponse response_data = 4 [json_name = "response_data"];
  }
  message InitMandateResponse {
    Links links = 1 [json_name = "links"];
    string tracking_id = 2 [json_name = "tracking_id"];
    int64 mandate_amount = 3 [json_name = "mandate_amount"];
    // UMRN (Unique Mandate Reference Number) is a unique identifier generated by NPCI
    // when an e-NACH mandate is successfully registered. This field will be populated
    // when the mandate is already completed.
    string umrn = 4 [json_name = "umrn"];
    int64 amount = 5 [json_name = "amount"];
  }

  message Links {
    string url = 1 [json_name = "url"];
    string mandate_validity = 2 [json_name = "validity"];
    string url_validity = 3 [json_name = "valid_till"];
    string mandate_id = 4 [json_name = "mandate_id"];
  }
}
