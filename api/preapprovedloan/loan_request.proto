syntax = "proto3";

package preapprovedloan;

import "api/frontend/deeplink/deeplink.proto";
import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/enums/program.proto";
import "api/preapprovedloan/pledge_details.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/common/gender.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

//go:generate gen_sql -types=LoanRequest,LoanRequestDetails,LoanInfo,Deductions,OtpInfo
message LoanRequest {
  string id = 1;
  string actor_id = 2;
  string offer_id = 3;
  string orch_id = 4;
  string loan_account_id = 5;
  string vendor_request_id = 6;
  Vendor vendor = 7;
  LoanRequestDetails details = 8;
  LoanRequestType type = 9;
  LoanRequestStatus status = 10;
  LoanRequestSubStatus sub_status = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
  google.protobuf.Timestamp completed_at = 15;
  // Deeplink to redirect to the next screen
  frontend.deeplink.Deeplink next_action = 16;
  LoanProgram loan_program = 17;
  frontend.deeplink.Deeplink redirect_link = 18;
  string client_req_id = 19;
}

message LoanRequestDetails {
  OtpInfo otp_info = 1;
  string masked_account_number = 2;
  LoanInfo loan_info = 3;
  api.typesv2.common.PhoneNumber phone_number = 4;
  string customer_device_id = 5;
  string email_id = 6;
  // this is used only for view purpose(to populate data on screen)
  // NOTE: this should not be used to derive any business logic, for that use case we should always fetch loan offer from db
  google.protobuf.Timestamp loan_offer_expiry = 7;
  // request type specific details
  oneof details {
    PortfolioFetchDetails portfolio_fetch_details = 8;
    LoanApplicationDetails loan_application_details = 9;
    NFTDetails nft_details = 10;
  }
  enums.LoanProgramVersion program_version = 11;
  // location token we get from client side when we apply for loan.
  string location_token = 12;
  // old_offer_id is used to store the old offer id in case of offer change during the application journey
  // as of now, offer can be changed only once for a loan request, that is why we are storing only one old offer id
  string old_offer_id = 13;
  // if true, the workflow that is processing this loan request will be waiting for SyncProxy signal
  bool is_in_sync_mode = 14;
  // true if the lender has done an enquiry with the credit bureau
  bool is_hard_pull_done = 15;

  // application_details is used to store the whole application common details
  ApplicationDetails application_details = 16;
  // web_url_query is used to extract the query params from the web url that will be used to create lead in webEligibility
  string  web_url_query = 17;
  message OtpInfo {
    string otp = 1;
    int32 max_attempts = 2;
    int32 attempts_count = 3;
    // In case of failure, last entered otp becomes null
    // In case of success, update last entered otp
    string last_entered_otp = 4;
    OtpType otp_type = 5;
    // these fields are used in case multiple OTPs are needed for verification
    // to show the user how many more steps user needs to do
    int32 total_otps = 6;
    int32 otp_serial_number = 7;
    string token = 8;
  }

  message LoanInfo {
    google.type.Money amount = 1;
    int32 tenure_in_months = 2;
    // disbursal_amount is calculated amount before actual disbursal
    google.type.Money disbursal_amount = 3;
    double interest_rate = 4;
    google.type.Money emi_amount = 5;
    Deductions deductions = 6;
    google.type.Money total_payable = 7;
    double apr_rate = 8;
    PledgeDetails pledge_details = 9;
    message Deductions {
      google.type.Money total_deductions = 1;
      google.type.Money gst = 2;
      google.type.Money processing_fee = 3;
      google.type.Money advance_interest = 4;
    }
    // expected_disbursment_date is passed to the partner LMS system for creation and disbursal of loan in partner LMS system.
    google.type.Date expected_disbursment_date = 10;
    google.type.Date emi_start_date = 11;
    string disbursement_utr = 12;
    // actual_disbursal_amount is the actual amount credited to user's bank account
    // this is needed currently to validate the actual disbursal amount against the disbursal amount in the partner LMS system
    google.type.Money actual_disbursal_amount = 13;
    // true if the loan application is started with a discounted offer
    // discount details are present in the offer
    bool is_discounted_offer = 14;
  }
}

// ApplicationDetails is used to store the common details of the application like kyc, mandate details, etc.
message ApplicationDetails {
  KycDetails kyc_details = 1;
}

message KycDetails {
  // This bool will define if the KYC is already completed at vendor end, and needs to be skipped
  bool is_kyc_already_done_previously = 1;
  KycType kyc_type = 2;
  PersonalDetails personal_data = 3;
  string raw_response = 4;
  string kyc_document_number = 5;
  string user_image_path = 6;
  string vendor_kyc_request_id = 7;
  // this location token is supposed to be the token which is being fetched just after fetching kyc data
  string location_token = 8;

  message PersonalDetails {
    api.typesv2.common.Name name = 1;
    api.typesv2.common.Date dob = 2;
    api.typesv2.common.Gender gender = 3;
    api.typesv2.common.PostalAddress correspondence_address = 4;
    api.typesv2.common.PostalAddress permanent_address = 5;
    api.typesv2.common.PostalAddress kyc_address = 6;
    api.typesv2.common.PhoneNumber mobile = 7;
    string pan = 8;
    api.typesv2.common.Name fathers_name = 9;
    api.typesv2.common.Name mothers_name = 10;
  }
}



message PortfolioFetchDetails {
  oneof details {
    FiftyfinLamfPortfolioFetchDetails fiftyfin_lamf_details = 1;
  }
}

message FiftyfinLamfPortfolioFetchDetails {
  // skip account detail update during portfolio fetch.
  bool skip_account_detail_update = 1;
  bool fetch_mfc_portfolio = 2;
  bool fetch_fiftyfin_portfolio = 3;
  // This flag should be set to true only if fetch_mfc_portfolio is set to true.
  bool fetch_mfc_cas_summary_portfolio = 4;
  // This flag is set to true if the user email should be inferred from mfc portfolio. If set to false then the value is taken as input from user.
  // Also this will be used only if skip_account_details_update is set to false
  bool user_email_input_inferred = 5;
}

message LoanApplicationDetails {
  oneof details {
    FiftyfinLamfLoanApplicationDetails fiftyfin_lamf_details = 1;
    LdcLoanApplicationDetails ldc_loan_application_details = 2;
  }
}

message LdcLoanApplicationDetails {
  PreBreDataLoanPreferences pre_bre_data_loan_preferences = 1;
  AaAnalysisBankDetails aa_data = 2;
  RoiModificationDetails roi_modification_details = 3;

  message RoiModificationDetails {
    google.protobuf.Timestamp roi_modification_deadline = 2;
  }

  message PreBreDataLoanPreferences {
    int64 loan_amount = 1;
    int64 interest = 2;
  }

  message AaAnalysisBankDetails {
    string account_holder_name = 1;
    string account_number = 2;
    string ifsc = 3;
    string type = 4;
    string bank_name = 5;
    bool is_aa_data_needed = 6;
  }
}

message FiftyfinLamfLoanApplicationDetails {
  repeated FiftyfinLamfCreateLoanAttempt create_loan_attempts = 1;
}

message FiftyfinLamfCreateLoanAttempt {
  string loan_id = 1;
  google.protobuf.Timestamp created_at = 2;
}

message NFTDetails {
  oneof details {
    FiftyfinNftDetails fiftyfin = 1;
  }
}

message FiftyfinNftDetails {
  // This will be populated once the request is created.
  string external_id = 1;
  NftType type = 2;
  oneof user_auth {
    string email = 3;
    api.typesv2.common.PhoneNumber mobile = 4;
  }
  oneof details {
    NFTUpdateEmailDetails update_email = 5;
    NftUpdateMobileDetails update_mobile = 6;
  }
  // sync signal will be sent to this workflow execution if not empty
  string caller_workflow_id = 7;
}

message NFTUpdateEmailDetails {
  repeated string foliosList = 1 [deprecated = true];
  string target_email = 2 [deprecated = true];
  repeated FolioEmailUpdateDetails folios_update_list = 3;
}

message NftUpdateMobileDetails {
  repeated string foliosList = 1 [deprecated = true];
  api.typesv2.common.PhoneNumber target_mobile = 2 [deprecated = true];
  repeated FolioMobileUpdateDetails folios_update_list = 3;
}

enum NftType {
  NFT_TYPE_UNSPECIFIED = 0;
  NFT_TYPE_UPDATE_EMAIL = 1;
  NFT_TYPE_UPDATE_MOBILE = 2;
}

message FolioEmailUpdateDetails {
  string amc_code = 1;
  string folio_number = 2;
  MutualFundNftNewDetailsRelationship relationship = 3;
  string target_email = 4;
}

message FolioMobileUpdateDetails {
  string amc_code = 1;
  string folio_number = 2;
  MutualFundNftNewDetailsRelationship relationship = 3;
  api.typesv2.common.PhoneNumber target_mobile = 4;
}

enum MutualFundNftNewDetailsRelationship {
  MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED = 0;
  MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_SELF = 1;
}
