syntax = "proto3";

package vendorgateway.aml;

import "api/aml/data.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/document_proof.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/income_slab.proto";
import "api/typesv2/common/marital_status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/nationality.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/politically_exposed_status.proto";
import "api/vendorgateway/request_header.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/aml";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aml";

// Aml service is used to communicate with AML vendors for screening our customers
service Aml {
  // RPC to check the user's details for any money laundering activities
  // by querying against the vendor's database of money launderers.
  rpc ScreenCustomer (ScreenCustomerRequest) returns (ScreenCustomerResponse);

  // RPC for the new AS501 Multi-Purpose Customer Information API
  // Supports multiple purposes: Initial Screening (01), Initial Screening with TW Workflow (03),
  // and Continuous Screening (04)
  rpc ScreenCustomerAS501 (ScreenCustomerAS501Request) returns (ScreenCustomerAS501Response);
}

message ScreenCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // unique for each actor
  string record_identifier = 2;
  // request id to be passed to vendor
  string vendor_request_id = 3;
  // deprecated since AML screening rules are configured based on AmlProduct and this is of no use
  .aml.AmlEntity entity = 4 [deprecated = true];
  .aml.AmlProduct product = 5;
  CustomerDetails customer_details = 6 [(validate.rules).message.required = true];

  // Owner of the screening request.
  // Determines the correct API URL and configuration needed for the screening request with the vendor.
  // The API URL and configuration may differ by owner to comply with regulations,
  // ensuring only the owner’s designated case reviewer can access and review potential matches on money launderer lists.
  api.typesv2.common.Owner owner = 7;
}

message ScreenCustomerResponse {
  rpc.Status status = 1;
  // rejection message if the request is rejected
  string rejection_message = 2;
  // rejection code if the request is rejected
  .aml.RejectionCode rejection_code = 3;
  // record identifier passed in the request
  string record_identifier = 4;
  MatchStatus match_status = 5;
  // case id if matched and case is generated
  string case_id = 6;
  // case link if matched and case is generated
  string case_link = 7;
  // count of matches found
  uint64 alert_count = 8;
  // details of all the matches
  repeated .aml.MatchDetails match_details = 9;
}

message CustomerDetails {
  // name of the customer - Mandatory
  api.typesv2.common.Name name = 1 [(validate.rules).message.required = true];
  // father name - NOT Mandatory
  api.typesv2.common.Name father_name = 2;
  // mother name - NOT Mandatory
  api.typesv2.common.Name mother_name = 3;
  // gender - NOT Mandatory
  api.typesv2.common.Gender gender = 4;
  // marital status - NOT Mandatory
  api.typesv2.common.MaritalStatus marital_status = 5;
  // income slab - NOT Mandatory
  api.typesv2.common.IncomeSlab income_slab = 6;
  // pan number - NOT Mandatory
  string pan_number = 7;
  // nationality - Mandatory
  api.typesv2.common.Nationality nationality = 8 [(validate.rules).enum = {not_in: [0]}];
  // passport id number - NOT Mandatory
  string passport_number = 9;
  // passport expiry date - Mandatory if passport provided
  google.type.Date passport_expiry_date = 10;
  // driving license id number - NOT Mandatory
  string driving_license_number = 11;
  // passport expiry date - Mandatory if driving license provided
  google.type.Date driving_license_expiry_date = 12;
  // voter id number - NOT Mandatory
  string voter_id = 13;
  // document type of proof of address provided - NOT Mandatory
  api.typesv2.common.DocumentProofType poa_type = 14;
  // phone number - NOT Mandatory
  api.typesv2.common.PhoneNumber phone_number = 15;
  // email - NOT Mandatory
  string email = 16;
  // date of birth - NOT Mandatory
  google.type.Date date_of_birth = 17;
  // permanent address - NOT Mandatory
  api.typesv2.common.PostalAddress permanent_address = 18;
  // correspondence address - NOT Mandatory
  api.typesv2.common.PostalAddress correspondence_address = 19;
  // politically exposed status - NOT Mandatory
  api.typesv2.common.PoliticallyExposedStatus politically_exposed_status = 20;
  // employment type - NOT Mandatory
  api.typesv2.common.EmploymentType employment_type = 21;
}

// represents whether a match is found or not after screening is done
enum MatchStatus {
  MATCH_STATUS_UNSPECIFIED = 0;
  // Match found in screening
  MATCH_STATUS_MATCHED = 1;
  // Match not found in screening
  MATCH_STATUS_NOT_MATCHED = 2;
  // Error from vendor in screening attempt
  MATCH_STATUS_ERROR = 3;
}

// AS501 API Purpose codes
enum AS501Purpose {
  AS501_PURPOSE_UNSPECIFIED = 0;
  // Initial Screening with API Response and No Storage
  AS501_PURPOSE_INITIAL_SCREENING_NO_STORAGE = 1;
  // Initial Screening with API Response and TW Workflow
  AS501_PURPOSE_INITIAL_SCREENING_WITH_WORKFLOW = 3;
  // Continuous Screening with TW Workflow
  AS501_PURPOSE_CONTINUOUS_SCREENING = 4;
}
