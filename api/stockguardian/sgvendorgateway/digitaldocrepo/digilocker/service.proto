syntax = "proto3";

package stockguardian.sgvendorgateway.digitaldocrepo.digilocker;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/common/access_token.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/gender.proto";
import "google/type/date.proto";
import "validate/validate.proto";
import "api/stockguardian/sgdigilocker/common/document.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/digitaldocrepo/digilocker";


// Digilocker is a gRPC service for interacting with the Digilocker vendor APIs.
// API doc: https://cf-media.api-setu.in/resources/Requester-APISpecification-V1_12.pdf
service Digilocker {

  // GetAccessToken fetches an access token from DigiLocker, which can be used to call DigiLocker APIs.
  rpc GetAccessToken (GetAccessTokenRequest) returns (GetAccessTokenResponse);
  // RefreshAccessToken refreshes an expired or soon-to-expire access token used for Digilocker API.
  rpc RefreshAccessToken (RefreshAccessTokenRequest) returns (RefreshAccessTokenResponse);
  // GetEAadhaarDataInXML retrieves an e-Aadhaar document in XML format.
  rpc GetEAadhaarInXML (GetEAadhaarInXMLRequest) returns (GetEAadhaarInXMLResponse);

}

// Request message for obtaining an access token
message GetAccessTokenRequest {
  vendorgateway.RequestHeader header = 1;
  // Authorization code received from OAuth flow.
  string code = 2 [(validate.rules).string.min_len = 1];
  // Client ID provided by digilocker vendor
  string client_id = 3 [(validate.rules).string.min_len = 1];
  // Redirect URI used in OAuth flow to get authorization code
  string redirect_uri = 4 [(validate.rules).string.min_len = 1];
  // Code verifier used for vendor call for authentication to get access token.
  string code_verifier = 5 [(validate.rules).string.min_len = 1];
}

// Response message containing access token details
message GetAccessTokenResponse {
  rpc.Status status = 1;
  TokenDetails token_details = 2;
}

//Request message for refreshing an access token.
message RefreshAccessTokenRequest {
  vendorgateway.RequestHeader header = 1;
  // Refresh token to obtain a new access token
  string refresh_token = 2;
  // Client ID provided by digilocker vendor
  string client_id = 3 [(validate.rules).string.min_len = 1];
}

// Response message containing refreshed access token details.
message RefreshAccessTokenResponse {
  rpc.Status status = 1;
  TokenDetails token_details = 2;
}

// Message containing detailed access token information
message TokenDetails {
  // The access token that can be used to call the DigiLocker APIs
  string access_token = 1;
  //  Timestamp indicating when the access token expires..
  google.protobuf.Timestamp expires_at = 2;
  // New refresh token
  string refresh_token = 3;
  // The timestamp in UNIX format indicating when the user's consent expires.
  google.protobuf.Timestamp consent_valid_till = 4;
  // Type of token (e.g., 'bearer')
  api.typesv2.common.AccessTokenType token_type = 5;
  // The access permissions (space separated) associated with the token. (e.g., "files.issueddocs partners.PANCR partners.DRVLC")
  string scope = 6;
  // DigiLocker Id of the user account.
  string digilocker_id = 7;
  // Name of the user
  api.typesv2.common.Name name = 8;
  // Date of birth of the user.
  google.type.Date dob = 9;
  // Gender of the user.
  api.typesv2.common.Gender gender = 10;
  // Flag indicating whether e-Aadhaar is present for the user.
  api.typesv2.common.BooleanEnum is_e_aadhaar_present = 11;
  // Flag indicating whether the user’s account existed earlier or the user
  // signed up on DigiLocker through our OAuth flow.
  // NOTE: returned only in GetAccessToken api response and not with RefreshAccessToken response
  api.typesv2.common.BooleanEnum is_new_account = 12;
  // DigiLocker account reference key
  string reference_key = 13;
}

//Request message for obtaining e-Adhaar in XML format.
message GetEAadhaarInXMLRequest {
  vendorgateway.RequestHeader header = 1;
  // The access token that can be used to call the DigiLocker APIs
  string access_token = 2;
}

//Response message containing e-Adhaar in XML format.
message GetEAadhaarInXMLResponse {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // digilocker data validation failed
    STATUS_DATA_VALIDATION_FAILED = 101;
    // aadhar not linked to digilocker
    STATUS_AADHAR_NOT_LINKED = 102;
    // Asking data for higher permission than access token
    STATUS_INSUFFICIENT_SCOPE = 103;
  }
  rpc.Status status = 1;
  // base64 encoded e-Aadhaar data in XML format
  string xml_data = 2;
  api.stockguardian.sgdigilocker.common.AadhaarXMLParsedContent parsed_document = 3;
}
