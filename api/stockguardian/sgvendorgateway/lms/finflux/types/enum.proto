syntax = "proto3";

package stockguardian.sgvendorgateway.lms.finflux.types;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/lms/finflux/types";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgvendorgateway.lms.finflux.types";

enum LoanStatus {
  LOAN_STATUS_UNSPECIFIED = 0;
  LOAN_STATUS_SUBMITTED_AND_AWAITING_APPROVAL = 1;
  LOAN_STATUS_APPROVED = 2;
  LOAN_STATUS_ACTIVE = 3;
  LOAN_STATUS_TRANSFER_IN_PROGRESS = 4;
  LOAN_STATUS_TRANSFER_ON_HOLD = 5;
  LOAN_STATUS_WITHDRAWN_BY_CLIENT = 6;
  LOAN_STATUS_REJECTED = 7;
  LOAN_STATUS_CLOSED = 8;
  LOAN_STATUS_WRITTEN_OFF = 9;
  LOAN_STATUS_RESCHEDULED = 10;
  LOAN_STATUS_OVERPAID = 11;
}

// flat - interest is calculated on the initial principal amount for the entire loan tenure
// declining balance - interest is calculated on the outstanding principal amount
// https://groww.in/calculators/flat-vs-reducing-rate-calculator
enum InterestType {
  INTEREST_TYPE_UNSPECIFIED = 0;
  INTEREST_TYPE_FLAT = 1;
  INTEREST_TYPE_DECLINING_BALANCE = 2;
}

// example use case: repayment frequency type, interest rate frequency type
enum FrequencyType {
  FREQUENCY_TYPE_UNSPECIFIED = 0;
  FREQUENCY_TYPE_DAY = 1;
  FREQUENCY_TYPE_WEEK = 2;
  FREQUENCY_TYPE_MONTH = 3;
  FREQUENCY_TYPE_YEAR = 4;
}

enum ChargeType {
  CHARGE_TYPE_UNSPECIFIED = 0;
  CHARGE_TYPE_NACH_BOUNCE = 1;
}

enum ReportType {
  REPORT_TYPE_UNSPECIFIED = 0;
  REPORT_TYPE_NPA = 1;
  REPORT_TYPE_ACTIVE = 2;
  REPORT_TYPE_DISBURSEMENT = 3;
  REPORT_TYPE_REPAYMENT = 4;
  REPORT_TYPE_RPS = 5;
}
