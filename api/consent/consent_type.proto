//go:generate gen_sql -types=ConsentProvenance
syntax = "proto3";

package consent;

option go_package = "github.com/epifi/gamma/api/consent";
option java_package = "com.github.epifi.gamma.api.consent";


// Represents the type of consent that an actor can provide
// To be in sync with api/frontend/consent/consent_type.proto
enum ConsentType {
  ConsentType_UNSPECIFIED = 0;
  TnC = 1;
  cKYC = 2;
  eKYC = 3;
  Waitlist = 4;
  UPI = 5;
  FI_TNC = 6;
  FED_TNC = 7;
  FI_PRIVACY_POLICY = 8;
  FI_WEALTH_TNC = 9;
  CREDIT_REPORT_TNC = 10;
  HIGH_RISK_DEVICE = 11;
  // consent to share some of the user's data with p2p vendor
  FI_P2P_INVESTMENT = 12;
  // consent to share some of the user's data with preapprovedloan vendor
  FI_PRE_APPROVED_LOAN = 13;
  // consent to migrate the old vpa to new tpap vpa
  VPA_MIGRATION = 14;
  // consent that user has gone through the secure usage guidelines
  SECURE_USAGE_GUIDELINES = 15;
  CREDIT_REPORT_UPDATED_TNC = 16;
  // user have given consent for discrepancy in income and occupation
  INCOME_OCCUPATION_DISCREPANCY = 17;
  // consent for importing user's MF holdings with the vendor
  FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC = 18;
  // consent to share some of the user's data with credit card vendor
  FI_CREDIT_CARD = 19;
  // consent to the credit card emi tnc
  FI_CREDIT_CARD_EMI_TNC = 20;
  // consent for credit card key fact statements
  FI_CREDIT_CARD_KFS = 21;
  // consent for fi credit card most important tnc
  FI_CREDIT_CARD_MOST_IMP_TNC = 22;
  // consent for fi credit card TnC
  FI_CREDIT_CARD_TNC = 23;
  // consent to use aadhaar number E.g. Use aadhaar number to
  // set / reset upi pin
  AADHAAR_NUMBER_BASED_PIN_SET = 24;
  // consent to share PAN and DOB of the user with IDFC bank for Pre-approved loans journey
  PL_IDFC_PAN_DOB = 25;
  FED_STORAGE_POLICY = 26;
  INITIATE_BKYC = 27;
  // consent to confirm bkyc record, downloaded by agent from UIDAI
  CONFIRM_BKYC_RECORD = 28;
  // This consent includes tncs of fi wealth and mf central. It also includes the sharing of data from tech to wealth.
  FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC_V2 = 29;
  // This consent will tell users that their data for mf holdings will be available to them for limited time.
  // Mainly this consent will be for fi-lite users.
  MF_HOLDINGS_DATA_AVAILABILITY_FOR_LIMITED_TIME = 30;
  // Consent to open a fixed deposit for the purpose of onboarding on a secured credit card program
  SECURED_CREDIT_CARD_OPEN_FIXED_DEPOSIT = 31;
  // consent of user which enables fi to show credit report to user for a limited time
  CREDIT_REPORT_LIMITED_TIME_TNC = 32;
  // consent of user to delete fi account details when savings account is already closed
  FI_CLOSED_SAVINGS_ACCOUNT_DELETION = 33;
  // consent of the user to store the PAN and DOB in personal loans flow with Liquiloans
  PL_LL_PAN_DOB = 34;
  // user's consent to allow Epifi Tech fetching their EPF info from EPFO for the purpose of calculating your net worth
  EPF_TNC = 35;
  // This consent will tell users that their personal data will be used by Epifi and its partners for providing services
  EPIFI_INSIGHTS_TNC = 36;
  // This consent will tell users that their credit report data will be pulled from the vendor (eg. Experian)
  CREDIT_REPORT_DATA_PULL = 37;
  // user consent to allow idfc to auto-deduct the emi amount every month from user's account
  PL_IDFC_MANDATE = 38;
  // This consent will tell users that their credit report data will be pulled by Liquiloans vendor
  PL_LL_CREDIT_REPORT_DATA_PULL = 39;
  CONFIRM_PROFILE_UPDATE_AT_BANK = 40;
  REJECT_PROFILE_UPDATE_AT_BANK = 41;
  // consent to use device biometric unlock method
  DEVICE_UNLOCK_BIOMETRIC = 42;
  // consent of user which enables fi to show connected accounts data to user for a limited time
  CONNECTED_ACCOUNTS_LIMITED_TIME_TNC = 43;
  // consent allows epifi wealth to import and use financial data via account aggregator Finvu
  CONNECTED_ACCOUNTS_FI_WEALTH_AND_FINVU_TNC = 44;
  // consent allows epifi wealth to import and use financial data via account aggregator OneMoney
  CONNECTED_ACCOUNTS_FI_WEALTH_AND_ONEMONEY_TNC = 45;
  // BKYC_CUSTOMER_DUE_DILIGENCE consent is taken when the agent checks the details of the customer whose KYC is carried out with the bank.
  BKYC_CUSTOMER_DUE_DILIGENCE = 46;
  // consent is taken when user submits the savings account closure request
  SA_CLOSURE_REQUEST = 47;
  // this consent allows fi and its partners to process and use user input manual asset data for providing services to user
  MANUAL_ASSET_FORM_TNC = 48;
  // consent taken in screener itr intimation stage
  ITR_INTIMATION = 49;
  // consent taken from user for epifi wealth data sharing to epifi tech
  // use-case: we are taking consent from user to share connected accounts data to epifi tech for loan eligibility
  EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP = 50;
  // This consent will inform users that their data (credit bureau report, kyc info etc) can be pulled by Moneyview lender.
  DATA_PULL_BY_MONEYVIEW_LENDER = 51;
  CIBIL_REPORT_TNC = 52;

  UNSECURED_CREDIT_CARD_MOST_IMP_TNC = 53;
  UNSECURED_CREDIT_CARD_KFS = 54;
  UNSECURED_CREDIT_CARD_FEES = 55;

  SECURED_CREDIT_CARD_MOST_IMP_TNC = 56;
  SECURED_CREDIT_CARD_KFS = 57;

  MASS_UNSECURED_CREDIT_CARD_MOST_IMP_TNC = 58;
  MASS_UNSECURED_CREDIT_CARD_KFS = 59;

  CREDIT_REPORT_CHECK = 60;
  OPEN_FIXED_DEPOSIT = 61;
  // consent taken from user for epifi wealth data sharing to epifi tech
  // use-case: we are taking consent from user to share connected accounts data to epifi tech for screener
  EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_SCREENER_GROUP = 62;

  // This consent will inform users that their employment data can be pulled by ABFL lender.
  LOANS_ABFL_EMPLOYMENT_DATA = 63;
  // This consent will inform users that their itr data can be used by lenders.
  ITR_INTIMATION_PROCESSING_AND_SHARING_WITH_LENDERS = 64;
  // For user to agree to the risk profile decided by Epifi Wealth as an investment advisory
  EPIFI_WEALTH_INVESTMENT_RISK_PROFILE = 65;
  // Consent to enable UPI / TPAP for the accounts connected via AA
  LINK_CONNECTED_ACCOUNTS_VIA_UPI = 66;

  // NRI Account onboarding consents
  // https://fi.money/tnc/nr
  CONSENT_FI_NRE_NRO_ACCOUNTS = 70;
  // I confirm that I’m not resident in India
  CONSENT_NON_INDIAN_RESIDENCY = 67;
  // I’m not (nor am related to) a politically exposed person.
  CONSENT_NOT_POLITICALLY_EXPOSED = 68;
  // https://fi.money/tnc/fatca
  // "I declare that I am neither a citizen of USA nor a tax resident of any country other than India even though
  // my residence/mailing address/telephone number is of a country other than India. I have understood the information
  // requirements of the FATCA/CRS Form (read along with the FATCA/CRS Instructions) and hereby confirm that all
  // information provided by me is true, correct, and complete."
  CONSENT_FATCA_CRS = 76;
  // "I acknowledge that the services provided here are currently part of a regulatory sandbox test &
  // consent to participate in the testing phase."
  CONSENT_NON_RESIDENT_ACCOUNT_TESTING = 69;
  // "I also want to open a NRO account Learn More"
  // If user has given the consent then we can trigger the nro account creation after nre account creation
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=1643-12243&t=kJsS7G6jzYrRzpVg-0
  CONSENT_NRO_ACCOUNT_CREATION = 75;
  // "I confirm this is my current communication address"
  // This consent is taken when user confirms the communication address
  CONSENT_CONFIRM_COMMUNICATION_ADDRESS = 71;
  // Take user's consent to use their AA data for lending.
  CONSENT_USE_AA_DATA_FOR_LENDING = 72;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=50454-25879&t=JHvB4WZY6LQsc5vI-4
  // Consent taken to indicate that the user has agreed to the revised offer terms
  LENDING_CONFIRM_REVISED_OFFER_TERMS = 73;
  // "I consent Federal Bank to fetch my financial data using the licensed Account Aggregator One Money for the purpose of loan underwriting."
  CONSENT_FEDERAL_AA_DATA_SHARE = 74;
  // I'm an Indian citizen; India is my country of tax residence
  CONSENT_INDIAN_RESIDENCY = 77;
  // Consent taken to indicate Stock Guardian can download the CKYC data for the user
  CONSENT_STOCK_GUARDIAN_TSP_CKYC_DOWNLOAD = 78;
  // Consent taken to indicate Stock Guardian can do a hard pull of the user's credit report
  CONSENT_STOCK_GUARDIAN_TSP_TYPE_CREDIT_REPORT_HARD_PULL = 79;
  // Consent taken to indicate Stock Guardian can use the user's data for promotions
  CONSENT_STOCK_GUARDIAN_TSP_DATA_USAGE_FOR_PROMOTIONS = 80;
  // Consent to record user has aggred to the terms and conditions of the SMS fetcher POC
  CONSENT_SMS_FETCHER_POC_WEALTH_TNC = 81 [deprecated = true];
  // consent to sg ckyc record fetch
  CONSENT_LOANS_SG_CKYC_RECORD_FETCH = 82;
  // consent to sg credit bureau fetch and info
  CONSENT_LOANS_SG_CREDIT_BUREAU_INFO = 83;
  // consent to sg to data use by associated partners
  CONSENT_LOANS_SG_DATA_USE_BY_ASSOCIATED_PARTNERS = 84;
  // consent to open a savings account with a minimum balance
  CONSENT_OPEN_MIN_BALANCE_SAVINGS_ACCOUNT = 85;
  // consent to allow Epifi Wealth to process user's SMS data
  CONSENT_SMS_DATA_PROCESSING_CONSENT = 86;
  // Consent to record user has agreed to the terms and conditions of the SMS scanner
  // If a user provides their consent, we will scan their sms to provide insights on their finances
  // Note: This is separate from CONSENT_SMS_FETCHER_POC_WEALTH_TNC consent which was used just for POC purposes
  CONSENT_SMS_FETCHER_WEALTH_TNC = 87;
  // Pre Bre Lenden consent to let lenden(innofin) check bureau and validate pan.
  CONSENT_LENDEN_CREDIT_REPORT_CHECK_AND_PAN_VALIDATION = 88;
  // Pre Bre Lenden privacy policy.
  CONSENT_PRE_BRE_LENDEN_PRIVACY_POLICY_AND_TNC = 89;
  // Consent to let Lenden(innofin) share users data with other vendors like loan data,personal details and credit data
  CONSENT_LENDEN_DATA_SHARING = 90;
  // declaration of annual income by user.
  CONSENT_DECLARATION_OF_ANNUAL_INCOME = 91;
  // Consent that user has agreed to current e-sign document
  // Consent Text: "I confirm that I have read and understood the Key Fact statement and Loan Agreement."
  CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_UNDERSTOOD = 92;
  // Consent to let user share their location
  // Consent text: "I allow my location to be shared for loan agreement purpose"
  CONSENT_LOANS_LDC_KFS_ALLOW_LOCATION = 93;
  // Consent that user has understood the e-sign document of the loan
  // Consent Text: "I confirm the information is presented in the language I understand."
  CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_LANGUAGE_UNDERSTOOD = 94;
  // consent to share user data associated with wealth to other epifi groups
  // This is a blanket consent and can be used by anyone i.e. EPIFI_TECH etc
  // This is a v2 config for in-app only use case of EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP
  // The current use case is to use it for share aa and sms data
  // Owned by Wealth entity
  CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2 = 95;

  // This consent allows Epifi Group to access data of a user present with Epifi Wealth
  // at a point in time via a user specified action.
  // This differs from the CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2 consent,
  // which allows Epifi Group to access user's Epifi Wealth data without requiring any
  // explicit action from them.
  CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP = 96;

  // This consent will inform users that their data can be shared with ABFL lender.
  CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER = 97;

  // This consent will inform users that their data can be shared with Federal lender.
  CONSENT_TYPE_DATA_SHARING_WITH_FEDERAL = 98;

  // Consent taken to allow Federal to pull user's credit report
  CONSENT_TYPE_CREDIT_REPORT_PULL_BY_FEDERAL_LENDER = 99;

  // Consent to allow Innofin Solutions Pvt Ltd (Lenden Club) to debit the EMI amount from the user's bank account as per the provided mandate
  CONSENT_TYPE_LDC_PAYMENT_MANDATE = 100;

  // Consent to allow Innofin Solutions Pvt Ltd (Lenden Club) to fetch user's Aadhaar data from DigiLocker
  CONSENT_TYPE_LDC_AADHAAR_DATA_PULL = 101;

  // Consent allowing Lenden Club to fetch user's CKYC data and confirming that the user is
  // not a politically exposed person and is not a citizen of any country other than India
  CONSENT_TYPE_LDC_POLITICAL_EXPOSURE_CITIZENSHIP = 102;

  // Consent allowing Lenden Club to fetch user's CKYC data
  CONSENT_TYPE_LDC_CKYC_DATA_PULL = 103;

  // User consents to filing form 60 as a declaration
  CONSENT_TYPE_FORM_60_DECLARATION = 104;

  CONSENT_TYPE_LDC_MODIFY_ROI = 105;

  // User declars that he/she is not differently abled person
  CONSENT_TYPE_DISABILITY = 106;

  // User declars if the account will not be used to receive any scholarship fund
  CONSENT_TYPE_SCHOLARSHIP = 107;

  // User declars if the account will not be used for DBT
  CONSENT_TYPE_DIRECT_BENEFIT_TRANSFER = 108;

  // User consent to use Fi MCP
  FI_MCP_AUTH = 109;

  // user consent as per new SEBI guidelines for wealth (mutual funds)
  // figma: https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=O4Q25jFCOhOqUxLT-4
  FI_WEALTH_MITC_TNC = 110;
}

// ConsentProvenance represents the source of consent provided by the user
enum ConsentProvenance {
  CONSENT_PROVENANCE_UNSPECIFIED = 0;
  // this is an explicit consent provided by the user in the app
  CONSENT_PROVENANCE_IN_APP = 1;
  // this is an implicit consent taken by just notifying the user via email
  // e.g: we've shared "Quick Update of Terms & Conditions" email to users for accessing their
  // connected accounts data (consent-type: EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP)
  CONSENT_PROVENANCE_EMAIL_IMPLICIT = 2;
}
