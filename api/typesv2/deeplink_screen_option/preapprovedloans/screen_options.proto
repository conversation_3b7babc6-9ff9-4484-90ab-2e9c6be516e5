syntax = "proto3";

package api.typesv2.deeplink_screen_option.preapprovedloans;

import "api/frontend/analytics/analytics_screen_name.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/errors/error_view.proto";
import "api/frontend/preapprovedloan/pal_enums/enums.proto";
import "api/frontend/recurringpayment/recurring_payment.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/components.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/enums.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/form.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/option_section_view_component.proto";
import "api/typesv2/ui/top_section_components.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.preapprovedloans";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// V2 version of loan offer details screen for pre approved loan, this can be used to run A/B experiments
message PreApprovedLoanOfferDetailsV2ScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  map<string, ComponentVersion> screen_version = 2;
  string offer_id = 3;
  string toolbar_title = 4;
  LoanConstraintsComponent loan_constraints_component = 5;
  LoanInfoComponent loan_info_component = 6;
  string partnership_url = 7 [deprecated = true];
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 8;
  typesv2.common.VisualElement partnership_logo = 9;

  message LoanConstraintsComponent {
    ComponentVersion version = 1;
    common.Text title = 2;
    Constraints constraints = 3;
    common.Text loan_amount_selector_label = 4;
    common.Text tenure_selector_label = 5;
    frontend.deeplink.InfoItemV2 benefits = 6;
    typesv2.Money default_amount = 7;
    int32 default_tenure = 8;
    frontend.deeplink.Button submit_cta = 9;
    Constraints loan_offer_constraints = 10;
    typesv2.Money emi_amount = 11;
    typesv2.Money max_emi_amount = 12;
    bool is_constraint_refresh_needed = 13;
    frontend.deeplink.InfoItemV2 bottom_message = 14;

    message Constraints {
      Money min_loan_amount = 1;
      Money max_loan_amount = 2;
      int32 min_tenure_in_months = 3;
      int32 max_tenure_in_months = 4;
    }
  }

  message LoanInfoComponent {
    ComponentVersion version = 1;
    common.Text title = 2;
    repeated ComponentDetailItem loan_info_details = 3;
    common.Text offer_validity_text = 4;
    frontend.deeplink.InfoItemV2 deductions = 5;
  }
}

// V2 version of application details screen for pre approved loan, this can be used to run A/B experiments
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3122
message PreApprovedLoanApplicationDetailsV2ScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  map<string, ComponentVersion> screen_version = 2;
  string offer_id = 3;
  string toolbar_title = 4;
  string partnership_url = 5 [deprecated = true];
  ApplicationLoanInfoComponent application_loan_info_component = 6;
  EmiInfoComponent emi_info_component = 7;
  TncInfoComponent tnc_info_component = 8;
  BenefitsComponent benefits_component = 9;
  frontend.deeplink.Button cta = 10;
  typesv2.Money loan_amount = 11;
  int32 loan_tenure = 12;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 13;
  InformationComponent information_component = 14;
  frontend.deeplink.InfoItemV2 guideline_text = 15;
  typesv2.common.VisualElement partnership_logo = 16;

  message InformationComponent {
    ComponentVersion version = 1;
    frontend.deeplink.InfoItemV2 information_text = 2;
  }

  message ApplicationLoanInfoComponent {
    ComponentVersion version = 1;
    repeated frontend.deeplink.InfoItemV2 loan_fees_and_charges = 2;
    frontend.deeplink.InfoItemV2 fee_and_charges_text = 3;
    common.Image separator_icon = 4;
    frontend.deeplink.InfoItemV2 amount_to_be_disbursed = 5;
  }
  message EmiInfoComponent {
    ComponentVersion version = 1;
    repeated ComponentDetailItem emi_interest_and_charges = 2;
    frontend.deeplink.InfoItemV2 emi_amount_details = 3;
    repeated frontend.deeplink.InfoItemV2 emi_schedule_details = 4;
    repeated frontend.deeplink.InfoItemV2 emi_info_list = 5;
  }
  message TncInfoComponent {
    ComponentVersion version = 1;
    common.Text title = 2;
    repeated frontend.deeplink.TermInfo term_infos = 3;
  }
  message BenefitsComponent {
    ComponentVersion version = 1;
    repeated frontend.deeplink.InfoItemV2 benefit_items = 2;
  }
}

message ComponentVersion {
  int32 major_version = 1;
  int32 minor_version = 2;
}

// ComponentDetailItem can be used to display the details inside a component or a sub-component.
// can be used to show the single detail item
// or can be used to show the merged details item like this https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=17025-33026&t=iwfIuybinMQWJkRC-4
message ComponentDetailItem {
  repeated frontend.deeplink.InfoItemV2 custom_info_items = 1;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36470&t=L5WOwrBPFf7FxhUr-4
message EarlySalaryOfferSelectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;
  common.Text title = 4;
  string title_icon = 5;
  frontend.deeplink.InfoItemV2 bottom_selector_info = 6;
  repeated SuggestedAmount suggested_amounts = 7;
  frontend.deeplink.Cta custom_amount = 8;
  frontend.deeplink.Button next = 9;
  string partnership_icon = 10;
  frontend.deeplink.Cta help = 11;

  message SuggestedAmount {
    typesv2.Money amount = 1;
    common.Text desc = 2;
    bool default_state = 3;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36660&t=L5WOwrBPFf7FxhUr-4
message EarlySalaryApplicationDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // meta data
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // meta data
  string offer_id = 3;
  // deprecated, not used anymore, removed from design
  string title_icon = 4 [deprecated = true];
  common.Text title = 5 [deprecated = true];

  // This will give the terms and condition text
  // deprecated: replaced with consent_texts
  repeated frontend.deeplink.TermInfo term_infos = 6 [deprecated = true];
  Summary summary = 7;
  Fees fees = 8;
  frontend.deeplink.Button next = 9;
  string partnership_icon = 10;
  // meta data for ApplyForLoan rpc request
  typesv2.Money amount = 11;
  // meta data for ApplyForLoan rpc request
  int32 tenure = 12;
  // represents consent and TnC strings with clickable action in embedded links (using ^^link^^string^^ formatting)
  repeated typesv2.common.Text consent_texts = 13;

  message Summary {
    // indicates title for corresponding tab in the tab selector
    common.Text Title = 1;
    repeated DescriptionInfoItem items = 2;
    DescriptionInfoItem total = 3;
  }

  message Fees {
    common.Text Title = 1;
    repeated DescriptionInfoItem items = 2;
  }

  message DescriptionInfoItem {
    frontend.deeplink.InfoItemV2 item = 1;
    common.Text additional_msg = 2;
    frontend.deeplink.Cta clickable_action = 3;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18494%3A35155&t=L5WOwrBPFf7FxhUr-4
message EarlySalaryLoanDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // id to identify loan
  string loan_id = 3;
  string partnership_url = 4;
  frontend.deeplink.InfoItemV2 toolbar_info = 5;
  TopDescription top_description = 6;
  BottomDescription bottom_description = 7;
  repeated frontend.deeplink.Tile bottom_info_tiles = 8;
  frontend.deeplink.InfoItemV2 past_loan_details = 9;
  repeated frontend.deeplink.Cta overflow_items = 10;
  frontend.deeplink.Tile prepay_tile = 11;

  message TopDescription {
    frontend.deeplink.InfoItemV2 amount = 1;
    common.Text additional_message = 2;
    string bg_color = 3;
  }

  message BottomDescription {
    repeated frontend.deeplink.InfoItemTileV2 item_tile = 1;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35605&t=L5WOwrBPFf7FxhUr-4
message EarlySalaryCustomOfferSelectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;
  common.Text title = 4;
  typesv2.Money default_amount = 5;
  common.Text fee = 6;
  common.Text bottom_message = 7;
  frontend.deeplink.Button next = 8;
  Constraints constraints = 9;
  message Constraints {
    typesv2.Money min_amount = 1;
    typesv2.Money max_amount = 2;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36018&t=L5WOwrBPFf7FxhUr-4
message EarlySalaryDashboardScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.deeplink.InfoItemV2 toolbar_info = 3;
  string title_icon = 4;
  ApplicationDetails application_details = 5;
  // Button to start new application
  frontend.deeplink.Button new_application_button = 6;
  // For FAQs etc
  repeated frontend.deeplink.Tile bottom_info_tiles = 7;
  string partnership_url = 8;
  frontend.deeplink.InfoItemV2 past_loan_details = 9;

  // Will be used to represent applications in-progress/in-active ones
  message ApplicationDetails {
    // Used to represent tile Get, Fee, Repay on
    frontend.deeplink.InfoItemTileV2 details = 1;
    // For cases where the loan application is in progress, this message will be shown
    InProgressMessage in_progress_message = 2;
    // To represent overflow items
    repeated frontend.deeplink.Cta overflow_items = 3;
    // it holds the loan request id
    string loan_request_id = 4;
    // added cta for reload button on loan application tile so as to enable user to refresh the status for their application
    frontend.deeplink.InfoItemWithCtaV2 reload_cta = 5;
    message InProgressMessage {
      // icon to be shown beside the message
      string icon_url = 1;
      string message = 2;
      string bg_colour = 3;
      // will be nil for all cases, expect when loan application is ready for verification
      frontend.deeplink.Cta next_step = 4;
    }
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36388&t=L5WOwrBPFf7FxhUr-4
message EarlySalaryNonQualifiedUserScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.deeplink.InfoItemV2 title = 3;
  repeated Tile tiles = 4;
  message Tile {
    // icon, title, done (desc)
    frontend.deeplink.InfoItemV2 info = 1;
    // 1 received on 03 March 2023 (text)
    common.Text additional_message = 2;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=18313-36072&t=Dk6sJLrEnq8OTTq9-0
message EarlySalaryIntermediateStatusScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.deeplink.InfoItemV2 status_data = 3;
  frontend.deeplink.Button button = 4;
}

message EarlySalaryAutoRepaymentScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.deeplink.InfoItemV2 toolbar_info = 3;
  string title_icon = 4;
  string bg_color = 5;
  frontend.deeplink.InfoItemBox desc_box = 6;
  // This will give the terms and condition text
  repeated frontend.deeplink.TermInfo term_infos = 7;
  frontend.deeplink.Button next = 8;
  typesv2.common.Text bottom_message = 9;
  RecurringPaymentInformation recurring_payment_information = 10;
}

// Information needed to set up recurring payments.
message RecurringPaymentInformation {
  string recurring_payment_id = 1;
  // action like revoke, modify etc
  frontend.recurringpayment.Action action = 2;
}

message PlEnterDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text desc = 4;
  string bg_colour = 5;
  repeated DetailsSection details_section_list = 6;
  repeated frontend.deeplink.TermInfo term_infos = 7;
  frontend.deeplink.Button continue_button = 8;
  string partnership_url = 9;
  string loan_request_id = 10;
  common.VisualElement partnership_logo = 11;

  message DetailsSection {
    LayoutType layout_type = 1;
    DetailsType details_type = 2;
    PreFilledValue pre_filled_value = 3;
    oneof Details {
      Card card_details = 4;
      OccupationList occupation_list = 5;
      PlainText plain_text_details = 6;
    }
    enum LayoutType {
      LAYOUT_TYPE_PLAIN_TEXT = 0;
      LAYOUT_TYPE_CARD = 1;
      LAYOUT_TYPE_BULLET_POINTS = 2;
    }
    message Card {
      typesv2.common.Text header_text = 1;
      string colour = 2;
    }
    message OccupationList {
      repeated Occupation occupation_list = 1;
    }
    message PlainText {
      typesv2.common.Text label = 1;
    }
  }
}

message Occupation {
  typesv2.EmploymentType employment = 1;
  string occupation_text = 2;
}

// this can be used to send some pre-filled(half-filled) data to client.
message PreFilledValue {
  string value = 1;
  int32 remaining_characters = 2;
}

message PlReviewDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  // details to be reviewed by the user
  repeated frontend.deeplink.InfoItemBox details = 4;
  string partnership_url = 5;
  frontend.deeplink.Button button = 6;
  string loan_request_id = 7;
  repeated frontend.deeplink.TermInfo term_infos = 8;
  typesv2.common.VisualElement partnership_logo = 9;
  bool is_lat_long_needed = 10;
}

message PlDowntimeScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  string image_url = 4;
  frontend.deeplink.InfoItemV2 content = 5;
  frontend.deeplink.Button button = 6;
  typesv2.common.Text bottom_text = 7;
}

message ClientCallbackScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string client_callback_req = 3;
}

message PersonalLoansPanDobAdditionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;

  typesv2.common.Text title = 4;
  typesv2.common.Text desc = 5;
  string bg_colour = 6;

  typesv2.common.Text pan_title = 7;
  typesv2.common.Text pan_placeholder = 8;
  typesv2.common.Text dob_title = 9;
  typesv2.common.Text dob_placeholder = 10;

  repeated frontend.deeplink.TermInfo term_infos = 11;

  frontend.deeplink.Button continue_button = 12;
  string pre_filled_pan = 13;
  string pre_filled_dob = 14;
}

message PlCreditReportFetchConsentScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  typesv2.common.VisualElement icon = 4;
  typesv2.common.Text title = 5;
  typesv2.common.Text desc = 6;
  string loan_step_execution_id = 7;
  CreditReportVendor credit_report_vendor = 8;
  typesv2.common.ui.widget.BackgroundColour bg_colour = 9;
}

// Screen: PERSONAL_LOAN_NAME_AND_GENDER_ADDITION_SCREEN
// Figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31318&mode=design&t=OPDVQmKEaPBKLjcw-4
message PersonalLoanNameAndGenderAdditionScreenOptions {
  // meta data
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // meta data
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // meta data
  string loan_request_id = 3;
  // heading
  typesv2.common.Text title = 4;
  // heading description
  typesv2.common.Text desc = 5;
  // background color
  string bg_colour = 6;
  // includes struct representing name field
  Name name = 7;
  // includes struct representing gender field
  Gender gender = 8;
  // next action button
  frontend.deeplink.Button continue_button = 9;

  message Name {
    // Title text (Full Name)
    typesv2.common.Text title = 1;
    // the default/ pre-filled value that we need to show at start
    typesv2.common.Text default = 2;
    // edit name icon
    typesv2.common.VisualElement edit_icon = 3;
    // bottom description text
    typesv2.common.Text description = 4;
    // placeholder to be shown when default is empty string (Can hide title, edit_icon, desc)
    // if default not empty, show default text (with title, edit_icon and desc)
    typesv2.common.Text placeholder = 5;
  }

  message Gender {
    // Title text (Gender)
    typesv2.common.Text title = 1;
    // represents a list of options to show the user
    repeated Option options = 2;
    // default/ pre-selected option that we need to show at start
    string selected_option_color = 3;

    message Option {
      // option text
      typesv2.common.Text value = 1;
      // info to make option as selected by default
      // only one should be selected by default
      bool is_default_selected = 2;
      // meta data that needs to be sent for RPC call
      typesv2.Gender gender_enum = 3;
    }
  }
}

message PlBankingDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;

  typesv2.common.Text title = 4;
  typesv2.common.Text desc = 5;

  repeated Field fields = 6;

  frontend.deeplink.Button confirm_details_button = 7;

  string lse_id = 8;

  ui.IconTextComponent kyc_text = 9;
  frontend.preapprovedloan.pal_enums.SearchIfscType search_ifsc_use_case = 10;

  message Field {
    InputType input_type = 1;
    typesv2.common.Text hint = 2;
    FieldType field_type = 3;
    // Text to be shown as suffix when only a partial value is expected for a field (like a partial account number)
    // E.g. if the field is account number ********, the suffix can be set to 678 to indicate
    // that the user should only enter the first 5 digits
    typesv2.common.Text value_suffix = 4;

    enum FieldType {
      FIELD_TYPE_IFSC = 0;
      FIELD_TYPE_BANK_NAME = 1;
      FIELD_TYPE_ACCOUNT_NUMBER = 2;
      FIELD_TYPE_ACCOUNT_HOLDER_NAME = 3;
      // Field to allow users to enter a bank account number prefix, i.e. the part not present in the suffix
      FIELD_TYPE_ACCOUNT_NUMBER_PREFIX = 4;
    }
    enum InputType {
      INPUT_TYPE_PLAIN_TEXT = 0;
      INPUT_TYPE_DROPDOWN = 1;
    }
  }
}

// Entry point screen to show user to check loan eligibility. This will be a part of the landing info response screens
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31638&mode=design&t=vtNSCKIj8kuYWZd7-4
message LoanEligibilityLandingScreenOptions {
  // meta data
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // top toolbar items info
  ToolbarInfo toolbar_info = 3;
  // centre container with loan offer information and cta to start eligibility check journey
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=27388-9105&mode=design&t=vtNSCKIj8kuYWZd7-4
  CentreContainer centre_container = 4;
  // clickable tiles to redirect user to FAQ etc sections
  repeated ClickableBottomTile clickable_bottom_tiles = 5;
  // bottom banners with button e.g. check credit score banner
  repeated BottomBanner bottom_banners = 6;

  message ClickableBottomTile {
    string bg_color = 1;
    typesv2.common.Text title = 2;
    typesv2.common.VisualElement icon = 3;
    // on click redirection information CTA
    frontend.deeplink.Cta navigation_cta = 4;
  }

  message BottomBanner {
    string bg_color = 1;
    typesv2.common.Text title = 2;
    typesv2.common.VisualElement icon = 3;
    // button to redirection user to respective banner screen
    frontend.deeplink.Button navigation_button = 4;
  }

  message ToolbarInfo {
    // toolbar title
    typesv2.common.Text title = 1;
    // i button to redirect user to help section
    frontend.deeplink.InfoItemWithCtaV3 help_button = 2;
  }

  message CentreContainer {
    // first line of title
    typesv2.common.Text title = 1;
    // next line of title (different size)
    typesv2.common.Text sub_title = 2;
    typesv2.common.VisualElement icon = 3;
    // list of description texts with images
    repeated frontend.deeplink.InfoItemV3 descriptions = 4;
    // check eligibility button to start loan eligibility journey
    frontend.deeplink.Button next_action_button = 5;
    typesv2.common.VisualElement partnership_icon = 6;
    string bg_colour = 7;
  }
}

// Loan Application/Eligibility Review details screen with all the application data filled by the user till now
// figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32000&mode=design&t=vh1XIQSkhr8pc92K-4
// client to call GetDeeplink rpc with related screen to get this deeplink with screen and screen options filled
// on next_action_button, needs to call SubmitReviewLoanDetails RPC with loan header and loan_request_id
message LoanApplicationReviewDetailsScreenOptions {
  // meta data to be passed back to BE
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // bg  color of the outer screen layer
  string bg_color = 3;
  // texts at the top of the screen
  typesv2.common.Text title = 4;
  typesv2.common.Text sub_title = 5;
  // list of similar structured blocks
  repeated DetailBlock detail_blocks = 6;
  TncBlock tnc_block = 7;
  frontend.deeplink.Button next_action_button = 8;

  // meta data to be sent for rpc call
  string loan_request_id = 9;
  // used to show the top image at the top of the screen
  typesv2.common.VisualElement top_image = 10;
  // component to be shown on the toolbar right corner
  typesv2.ui.IconTextComponent toolbar_right_cta = 11;

  message DetailBlock {
    typesv2.common.Text title = 1;
    string block_bg_color = 2;
    // using cta_icon to show the icon and deeplink for navigation.
    // if icon is "" (null string), do not show the icon button.
    frontend.deeplink.Cta edit_cta = 3;
    // this is vertically laid out stack of itemsRows
    repeated ItemRow item_rows = 4;

    message ItemRow {
      // this is horizontally laid out stack of items
      repeated Item items = 1;
    }
    message Item {
      typesv2.common.Text key = 1;
      typesv2.common.Text value = 2;
    }
  }

  message TncBlock {
    typesv2.common.Text title = 1;
    string block_bg_color = 2;
    // represents consent and TnC strings with clickable action in embedded links (using ^^link^^string^^ formatting)
    repeated common.ui.widget.CheckboxItem consent_checkboxes = 3;
  }
}

// Loan Application/Eligibility loading screen after submission of all the application data filled by the user
// figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32100&mode=dev
message LoanApplicationReviewLoadingScreenOptions {
  // meta data to be passed back to BE
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;

  // contains visual element to show at the top
  typesv2.common.VisualElement hero_visual_element = 4;

  // bg  color of the outer screen layer
  string bg_color = 5;

  // text at the bottom of the screen. "This may take up to a minute. Hang on tight!"
  typesv2.common.Text bottom_text = 6;

  // current attempt number - incremented with each attempt on BE. Backend can control after which attempt number they want to add any handling
  int32 retry_attempt_number = 7;
  // duration in seconds before calling polling rpc again
  int32 next_poll_interval = 8;

  // used to set different handling for lottie for client to show, initial, poll, success/terminal
  LottieHandler lottie_handler = 9;

  message LottieHandler {
    // screen for lottie handling for success case check
    frontend.deeplink.Screen success_screen = 1;
    // frame end number for initial lottie, 0 to frame 1 end
    int32 frame_1_end = 2;
    // frame end number for poll lottie, frame 1 end to frame 2 end
    int32 frame_2_end = 3;
    // frame end number for success lottie, frame 2 end to frame 3 end
    int32 frame_3_end = 4;
  }
}

// PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN, Loan offer not available screen
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29325-52592&mode=dev
message LoanEligibilityNoOfferAvailableScreenOptions {
  // meta data to be passed back to BE
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // used to set centre icon, title and subtitle vertically aligned
  common.ui.widget.VisualElementTitleSubtitleElement visual_element_title_subtitle_element = 3;
  // back to home button
  frontend.deeplink.Button home_button = 4;
  // on click hides the text as button and opens the blocks with possible reasons for no available offer
  frontend.deeplink.Button learn_why_button = 5;
  // blocks with possible reasons for no available offer, that needs to appear after clicked on learn_why button
  NoLoanOfferReasons reasons_for_no_offer = 6;
  string bg_color = 7;
}

message NoLoanOfferReasons {
  typesv2.common.Text title = 1;
  repeated ReasonBlock reasons = 2;
  int32 top_margin = 6;

  message ReasonBlock {
    string bg_color = 1;
    typesv2.common.Text title = 2;
    typesv2.common.VisualElement icon = 3;
    typesv2.common.Text description = 4;
    frontend.deeplink.Cta block_action_cta = 5;
  }
}

// PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN, Loan offer available screen
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29325-52592&mode=dev
message LoanEligibilityOfferAvailableScreenOptions {
  // meta data to be passed back to BE
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // Congratulations
  typesv2.common.Text ticket_title = 3;
  // You have got a loan offer.
  typesv2.common.Text ticket_description = 4;
  // white box inside the image
  TicketBox ticket_box = 5;
  // powered by as text and liquiloans as icon
  typesv2.common.TextWithIcon partnership_text_with_icon = 6;
  // background color outside of the ticket box
  string bg_color = 7;
  // contains deeplink for navigation to the next screen
  frontend.deeplink.Cta next_navigation_cta = 8;
  // interval after which we need to navigate
  int32 next_navigation_interval_seconds = 9;
  // background image for overlay
  typesv2.common.VisualElement background_image = 10;

  message TicketBox {
    // white box bg color
    string bg_color = 1;
    // Maximum loan amount text
    typesv2.common.Text amount_title = 2;
    // amount as money
    typesv2.Money money = 3;
    // bg color to be set as ellipse color
    typesv2.common.Text interest_rate = 4;
  }
}

message LoanNonEligibilityLandingScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // used to set centre icon, title and subtitle vertically aligned
  common.ui.widget.VisualElementTitleSubtitleElement visual_element_title_subtitle_element = 2;
  // on click shows loans know more screen
  frontend.deeplink.Cta faq_cta = 3;
  // for showing possible reasons for no available offer
  NoLoanOfferReasons reasons_for_no_offer = 4;
  string bg_color = 5;
}

// https://www.figma.com/file/x1hL90FILdP836CGYpOQwZ/Fi-lite-Onboarding?type=design&node-id=2267-87461&mode=design&t=319NTHmrko9s6Foy-4
message AcqToLendLandingScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // Top Section
  frontend.deeplink.InfoItemWithCtaV3 help_button = 3;
  typesv2.common.Text title = 4;
  // added for further case
  typesv2.common.TextWithIcon partnership_text_with_icon = 5;

  // Middle Section
  SectionTypeGrid information_grid = 6;
  SectionTypeBanner information_banner = 7;

  // Bottom Section
  // CTA to be showed in screen
  // upon click, CheckLoanEligibility rpc will be called
  frontend.deeplink.Button button = 8;
  // icon + text that will be showed below the primary CTA, client can use left visual element, text and deeplink
  ui.IconTextComponent bottom_text = 9;
  string bg_color = 10;
}

message SectionTypeGrid {
  // title for grid
  typesv2.common.Text section_title = 1;
  // list of components for grid
  repeated ui.VerticalIconTextComponent information_blocks = 2;
  // bg colour for grid
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  // number of rows of information blocks in grid
  int32 num_of_rows = 4;
  // number of columns of blocks in grid
  int32 num_of_cols = 5;
  // Used to show the amount
  ui.IconTextComponent section_desc = 6;
  // Used to show the partnership title
  ui.IconTextComponent partnership_title = 7;
  // Used to show the partnership logos
  repeated typesv2.common.VisualElement logos = 8;
}
message SectionTypeBanner {
  ui.IconTextComponent section_title = 1;
  typesv2.common.VisualElement benefit_icon = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  frontend.deeplink.Cta cta = 4;
}

message AcqToLendLandingScreenOptionsV2 {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  repeated AcqToLendUiComponent ui_components = 3;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43148&mode=design&t=XIhv1d39sRT8sY3Z-4
message SectionHeader {
  // used to set centre icon, title and subtitle vertically aligned
  common.ui.widget.VisualElementTitleSubtitleElement visual_element_title_subtitle_element = 1;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43147&mode=design&t=4UzdfUfGxpKzUweZ-4
message SectionFooter {
  // Bottom Section
  // CTA to be showed in screen
  // upon click, CheckLoanEligibility rpc will be called
  frontend.deeplink.Button button = 1;
  // icon + text that will be showed below the primary CTA, client can use left visual element, text and deeplink
  ui.IconTextComponent bottom_itc = 2;
  // Used to show the footer label
  ui.IconTextComponent footer_label = 3;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43150&mode=design&t=4UzdfUfGxpKzUweZ-4
message SectionTypeLoanDetails {
  // for showing section title along-with loan amount, duration and interest rate
  // section title comes from infoblock.title, each row comes from infoblock.infoItems(index).title and infoblock.infoItems(index).desc
  frontend.deeplink.InfoBlockV2 loan_details = 1;
  string bg_color = 2;
  // component to show offer expiry. this is an optional field and will not get populated for all loan programs
  ui.IconTextComponent offer_expiry = 3;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43137&mode=design&t=LLj49bCshJpxz7OQ-4
message SectionTypeOfferTicket {
  // Congratulations text
  typesv2.common.Text ticket_title = 1;
  // You have got a loan offer.
  typesv2.common.Text ticket_description = 2;
  // white box inside the image
  TicketBox ticket_box = 3;
  // background color outside of the ticket box
  string bg_color = 4;
  // background image for overlay
  typesv2.common.VisualElement background_image = 5;
  // for offer expires in text
  typesv2.common.TextWithIcon offer_expiry_text = 6;

  message TicketBox {
    // white box bg color
    string bg_color = 1;
    //offer amount as money
    typesv2.Money offer_amount = 2;
    typesv2.common.Text interest_rate = 3;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43172&mode=design&t=buhfP9HZdNIiAAvA-4
message SectionApplicationMessage {
  ui.IconTextComponent message = 1;
}

message AcqToLendUiComponent {
  oneof component {
    SectionHeader section_header = 1;
    SectionTypeGrid section_type_grid = 2;
    SectionTypeBanner section_type_banner = 3;
    SectionTypeProgress section_type_progress = 4;
    SectionTypeLoanDetails section_type_loan_details = 5;
    SectionTypeOfferTicket section_type_offer_ticket = 6;
    SectionApplicationMessage section_application_message = 7;
    SectionFooter section_footer = 8;
    SectionApplicationBar section_application_bar = 9;
  }
}

message LamfLandingScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  repeated LoansScreenUiComponents components = 3;
  frontend.deeplink.Button button = 4;
  string screen_bg_color = 5 [deprecated = true];
  typesv2.common.Text navbar_title = 6;
  // text to be shown anchored to the screen just above the start button.
  // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=989%3A9147&mode=dev
  typesv2.common.Text regulatory_disclosure = 7;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color_v2 = 8;
  // Tag to show at the top of the bottom cta
  ui.IconTextComponent bottom_cta_tag = 9;
}

message SectionApplicationBar {
  //https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30970-28395&mode=dev
  oneof application_bar {
    OverFlowMenu over_flow_menu = 1;
    frontend.deeplink.Cta help_cta = 2;
  }
  message OverFlowMenu {
    repeated ui.IconTextComponent overflow_items = 1;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43461&mode=dev
message EditLoanBottomSheetOptions {
  common.ui.widget.VisualElementTitleSubtitleElement visual_element_title_subtitle_element = 1;
  frontend.deeplink.Cta back_cta = 2;
  // ongoing loan application need to be cancelled first before showing offer details screen, so client will call CancelApplication rpc on clicking button
  frontend.deeplink.Cta proceed_cta = 3;
  deeplink_screen_option.ScreenOptionHeader header = 4;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 5;
  // required to call CancelApplication() rpc
  string loan_request_id = 6;

}

// LoansInfoScreenOptions used as a generic intro/error/popup screen
// which might have center image, title, desc, bullet points, term infos, Cta for deeplink navigation
// e.g.-
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=31119-26825&mode=design&t=6T29915B1N6Q7zST-4
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29358-62846&mode=design&t=6T29915B1N6Q7zST-4
message LoansInfoScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // meta data
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;

  string bg_color = 4;
  common.ui.widget.VisualElementTitleSubtitleElement center_element = 5;
  // to represent bullet points
  repeated typesv2.common.Text bullet_points = 6;
  // to represent terms checkboxes
  repeated common.ui.widget.CheckboxItem consent_checkboxes = 7;
  frontend.deeplink.Button continue_button = 8;
  // AccountDetailsView to show failed account details in mandate flow
  // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10381-55108&mode=design&t=qb0liToG5S7EFswc-0
  AccountDetailsView account_details_view = 9;
  // secondary_cta: specifies if there's any action cat to be displayed which is not a continue_button
  // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10381-55108&mode=design&t=qb0liToG5S7EFswc-0
  ui.IconTextComponent secondary_cta = 10;
  string lse_id = 11;
  repeated Component components = 12;
  message Component {
    oneof component {
      // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=573-17300&mode=design&t=pbJ1Maqf2Tyj0Gm8-4
      HorizontalListComponent horizontal_list = 1;
      // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=573-17368&mode=design&t=pbJ1Maqf2Tyj0Gm8-4
      KeyValueRowsComponent key_value_info = 2;
      // figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=34470-57400&mode=design&t=l8LWG5hOs5dkEp4a-4
      BulletListComponent bullet_point_list = 3;
      //figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=12420-38875&mode=design&t=N0bxttI9pY7AXTTV-4
      BannerWithLeftIconAndBottomRightCta banner_info = 4;
    }
  }
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54134&mode=dev
message LamfJourneyInfoScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 8;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 2;

  typesv2.common.Text title = 3;
  typesv2.common.VisualElement icon_image = 4;

  repeated frontend.deeplink.InfoItemV3 journey_steps = 5;

  repeated frontend.deeplink.TermInfo term_infos = 6;

  frontend.deeplink.Button button = 7;
  // error message to be shown in case user if user is not allowed to check loan eligibility.
  // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=822-30036&mode=design&t=wm2v7y4ivnLimHcd-0
  typesv2.common.ui.widget.ImageTitleSubtitleElement error_message = 9;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54609&mode=design&t=JYrIaMGmibgom2Zx-4
message LamfShowOfferScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 2;

  repeated LoansScreenUiComponents components = 3;

  frontend.deeplink.Button primary_button = 7;

  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 8;
  // error message to be shown in case user can't proceed further with the offer
  // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=2241-24703&mode=dev
  typesv2.common.ui.widget.ImageTitleSubtitleElement error_message = 9;
  // component to be shown on the toolbar to refresh loan offer.
  typesv2.ui.IconTextComponent refresh_offer_component = 10;

  frontend.analytics.AnalyticsScreenName offer_screen_type = 11;
}

message LamfPfDetailsUpdateScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string process_id = 3;
  typesv2.ui.VerticalKeyValuePair page_title = 4;
  PhoneNumberField phone_number = 5;
  EmailField email = 6;
  frontend.deeplink.Button cta = 7;
  string loan_step_execution_id = 8;
  string loan_request_id = 9;
  // list of checkboxes to be shown above the CTA. CTA should be disables if the checkboxes are not ticked.
  repeated frontend.deeplink.TermInfo term_infos = 10;
  // e.g. "This does not affect your credit score"
  // deprecated in favor of "disclosure_text_v2" field
  typesv2.common.Text disclosure_text = 11 [deprecated = true];
  // specifies type of validation that are to be run by Client.
  ValidationType validation_type = 12;
  TextField pan_field = 13;
  typesv2.ui.IconTextComponent disclosure_text_v2 = 14;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 15;

  message PhoneNumberField {
    typesv2.common.Text label = 1;
    typesv2.common.PhoneNumber value = 2;
    typesv2.common.Text helper_text = 3;
    typesv2.common.Text placeholder_text = 4;
  }

  message EmailField {
    typesv2.common.Text label = 1;
    typesv2.common.Text value = 2;
    typesv2.common.Text helper_text = 3;
    typesv2.common.Text placeholder_text = 4;
  }

  message TextField {
    typesv2.common.Text label = 1;
    typesv2.common.Text value = 2;
    bool editable = 3;
  }

  enum ValidationType {
    VALIDATION_TYPE_UNSPECIFIED = 0;
    // both phone and email fields are required
    VALIDATION_TYPE_ALL_FIELDS_REQUIRED = 1;
    // at least one of phone/email is required.
    VALIDATION_TYPE_AT_LEAST_ONE_FIELD_REQUIRED = 2;
  }
}

message MultipleOtpVerificationScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string client_req_id = 3;
  typesv2.common.VisualElement image = 4;
  typesv2.ui.VerticalKeyValuePair text = 5;
}

message LoanDetailsSelectionScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;
  typesv2.common.Text page_title = 4;
  repeated LoanOfferSelectionComponent components = 5;
  typesv2.common.ui.widget.ImageTitleSubtitleElement error_message = 6;
  frontend.deeplink.Button cta = 7;
  typesv2.ui.IconTextComponent partner_text = 8;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 9;
}

message LoanOfferSelectionComponent {
  oneof component {
    PrincipleSelectionComponent principle_selection = 1;
    LoanTenureSelectionComponent tenure_selection = 2;
    MutualFundSelectionComponent mutual_funds_selection = 3;
    CollapsedLoanAmountSelectionComponent collapsed_loan_amount_selection_component = 4;
    LoanTenureOrEmiSelectionComponent loan_tenure_or_emi_selection_component = 5;
  }
}

// Screen: REVISED_LOAN_OFFER_APPLICATION_DETAIL_SCREEN
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48955&t=ineZGXSL5c6MuaPK-0
message RevisedLoanOfferApplicationDetailsScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;
  string loan_request_id = 4;
  typesv2.common.Text page_title = 5;
  repeated LoanApplicationDetailsScreenComponent components = 6;
  frontend.deeplink.Button cta = 7;
  typesv2.Money loan_amount = 8;
  int32 tenure_in_months = 9;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 10;
  // icon to be shown on right side of nav-bar
  ui.IconTextComponent nav_bar_cta = 11;
  LoansRewardsComponent reward_details = 12;
  // flag to show the expanded view of fee breakdown by default instead of showing a CTA to get the details
  bool show_expanded_view_by_default = 13;
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=356-47856&mode=design&t=K20VDDIFPLesoqxD-4
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-41997&mode=design&t=iM6BrGbvBq6JJ3Qz-4
message LoanApplicationDetailsScreen {
  typesv2.common.Text page_title = 1;
  repeated LoanApplicationDetailsScreenComponent components = 2;
  frontend.deeplink.Button cta = 3;
  typesv2.Money loan_amount = 5;
  int32 tenure_in_months = 6;
  MutualFundsPledgeDetails mutual_funds = 7;
  deeplink_screen_option.ScreenOptionHeader header = 8;
  string offer_id = 9;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 10;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 11;
  // icon to be shown on right side of nav-bar
  ui.IconTextComponent nav_bar_cta = 12;
  LoansRewardsComponent reward_details = 13;
  // flag to show the expanded view of fee breakdown by default instead of showing a CTA to get the details
  bool show_expanded_view_by_default = 14;
}

message MutualFundsPledgeDetails {
  repeated MutualFund funds = 1;
  message MutualFund {
    string isin = 1;
    // number of funds user is willing to pledge
    double quantity = 2;
  }
}

message LoanApplicationDetailsScreenComponent {
  oneof component {
    AmountWithExpandableBreakupComponent amount_with_breakup = 1;
    KeyValueRowsComponent details = 2;
    MfSchemeListComponent mf_pledge_details = 3;
    VisualElementComponent visual_element = 4;
    TermsAndConditionsComponent tnc_component = 5;
  }
}

message LamfApplicationAdditionalDetailsInputScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_application_id = 3;
  typesv2.ui.VerticalKeyValuePair title = 4;
  TextInputOption father_name = 5;
  TextInputOption mother_name = 6;
  DropdownSelectionInputOption marital_status = 7;
  DropdownSelectionInputOption employment_type = 8;
  DropdownSelectionInputOption residence_type = 9;
  frontend.deeplink.Button cta = 10;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 11;

  message TextInputOption {
    typesv2.common.Text label = 1;
    typesv2.common.Text value = 2;
    typesv2.common.Text helper_text = 3;
  }

  message DropdownSelectionInputOption {
    typesv2.common.Text label = 1;
    DropdownSelectionOptionComponent dropdown = 2;
    string selected_value = 3;
  }
}

message MfPortfolioEligibilityDetailsScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text page_title = 3;
  typesv2.common.Text page_heading = 6;
  // deprecated in favor of portfolio_value_text field.
  typesv2.Money portfolio_value = 7 [deprecated = true];
  SchemeList eligible_schemes = 4;
  SchemeList ineligible_schemes = 5;
  // pledged_schemes are shown in lamf dashboard screen
  SchemeList pledged_schemes = 8;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 9;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 10;
  typesv2.ui.IconTextComponent portfolio_value_text = 11;

  message SchemeList {
    typesv2.ui.IconTextComponent heading = 1;
    typesv2.common.Text name_col_header = 2;
    typesv2.common.Text value_col_header = 3;
    repeated SchemeDetails scheme_details = 4;
  }

  message SchemeDetails {
    typesv2.common.Image icon = 1;
    typesv2.common.Text name = 2;
    // deprecated in favor of amount_text field
    typesv2.Money amount = 3 [deprecated = true];
    frontend.deeplink.Deeplink deeplink = 4;
    typesv2.common.Text amount_text = 5;
  }
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=447%3A9545&mode=dev
message MfSchemeFolioBreakupScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Image icon = 3;
  typesv2.common.Text name = 4;
  // This is a section to show overall view of user's folio, positioned above folio details.
  FolioOverview folio_overview = 7;
  repeated FolioDetails folio_details = 5;
  frontend.deeplink.Button cta = 6;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 8;

  message FolioOverview {
    ui.IconTextComponent total_value_title = 1;
    ui.IconTextComponent total_value = 2;
    ui.IconTextComponent units_pledged_title = 3;
    ui.IconTextComponent units_pledged = 4;
  }

  message FolioDetails {
    typesv2.common.Text folio_desc = 1;
    // deprecated in favor of amount_text field
    typesv2.Money amount = 2 [deprecated = true];
    typesv2.common.Text units = 3;
    // details to be shown below folio description.
    repeated typesv2.common.Text details = 4;
    // tags to be shown alongside folio description
    repeated typesv2.ui.IconTextComponent tags = 5;
    typesv2.common.Text amount_text = 6;
  }
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=315-38685&mode=design&t=gLZPcHbizlMUM5cc-0
message LoansSubjectInfoWithDetailsListScreen {
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement title = 1;
  DetailsItemList details_list = 2;
  frontend.deeplink.Button cta = 3;
  deeplink_screen_option.ScreenOptionHeader header = 4;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 5;

  message DetailsItemList {
    repeated typesv2.common.ui.widget.VisualElementTitleSubtitleElement items = 1;
  }
}

message LamfVendorKycScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.InfoItemV2 header_info_item = 2;
  string loan_request_id = 3;
  string entry_url = 4;
  string exit_url = 5;
  frontend.deeplink.Deeplink next_screen = 6;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 7;
}

// ProgressUpdateScreen can be used to show a progress
// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=315-37950&mode=design&t=mInBSuWNBqaS3jIe-0
message ProgressUpdateScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  int32 percent_completed = 3;
  // ex. 90%
  typesv2.common.Text percent_text = 4;
  // ex. COMPLETED
  typesv2.common.Text completion_text = 5;
  typesv2.ui.IconTextComponent title = 6;
  typesv2.ui.IconTextComponent sub_title = 7;
  frontend.deeplink.Button cta = 8;
  repeated frontend.deeplink.TermInfo term_infos = 9;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 10;
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-11161&mode=design&t=mInBSuWNBqaS3jIe-0
message LoanApplicationCompletionSuccessScreen {
  typesv2.common.VisualElement icon = 1;
  typesv2.ui.IconTextComponent title = 2;
  typesv2.ui.IconTextComponent sub_title = 3;
  SectionTypeProgress journey_details = 4;
  frontend.deeplink.InfoItem tile = 5;
  frontend.deeplink.Button cta = 6;
  // image/lottie to be shown on the whole screen ex. confetti.
  typesv2.common.VisualElement full_screen_visual = 7;
  deeplink_screen_option.ScreenOptionHeader header = 8;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 9;
}

// figma link: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=573%3A17300&mode=dev
message LoansMandateIntroScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement top_component = 3;
  repeated Component components = 4;
  frontend.deeplink.Button cta = 5;
  // denotes learn how this works cta in below figma
  // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=555-16769&mode=design&t=L99QNe9uO1uOLBm9-0
  typesv2.ui.IconTextComponent know_more_component = 6;
  // information required to trigger recurring payment rpc
  RecurringPaymentInformation recurring_payment_information = 7;
  message Component {
    oneof component {
      // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=573-17300&mode=design&t=pbJ1Maqf2Tyj0Gm8-4
      HorizontalListComponent horizontal_list = 1;
      // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=573-17368&mode=design&t=pbJ1Maqf2Tyj0Gm8-4
      KeyValueRowsComponent key_value_info = 2;
      // figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=34470-57400&mode=design&t=l8LWG5hOs5dkEp4a-4
      BulletListComponent bullet_point_list = 3;
    }
  }
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=328-41534&mode=design&t=WDfFPySvpBiGCTT7-0
message LoansWebviewWithStatusPollScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string entry_url = 4;
  // retry backoff in milliseconds. Amount of time client has to wait before calling the status RPC again.
  uint32 retry_backoff = 5;
  typesv2.common.Text page_title = 6;
  // client will be checking the url open in the webview. Client should close the webview once user reaches the exit url in the webview.
  string exit_url = 7 [deprecated = true];
  // client will be checking the url open in the webview. Client should close the webview once user reaches any of the listed exit urls in the webview.
  repeated string exit_urls = 13;
  // duration (in milliseconds) client needs to wait before closing the webview on reaching exit url
  int32 exit_delay_in_milliseconds = 8;
  // once the webview is closed, client needs to show a polling waiting screen to the user.
  // this field contains details required for loading that view.
  PollingView polling_view = 9;
  // ensure camera permissions are present before opening the webview
  // this is needed for cases where we do kyc in webview.
  bool camera_permission_required = 10;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 11;
  // helps in switching between web view and external tab
  bool should_open_in_external_tab = 12;
  // This field is used to show the instruction alongwith the web view at the bottom
  // E.g - Important points to remember
  // 1. Make sure your entered details match the details on your PAN card.
  InstructionView instructions = 14;
  // This flag is used for Android to show the custom tab with some instructions
  bool is_custom_tab_v2_enabled = 15;

  message PollingView {
    typesv2.common.VisualElement icon = 1;
    typesv2.common.Text title = 2;
  }

  // figma- https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-FFF?node-id=74264-78724&t=tmYlEfECHI0vkdZr-4
  message InstructionView {
    InstructionHeader header = 1;
    repeated InstructionPoint points = 2;
    string bg_color = 3;
    map<string, string> event_properties = 4;

    message InstructionHeader {
      typesv2.common.VisualElement icon = 1;
      typesv2.common.Text title = 2;
      // this is specific to Android
      int32 title_text_size = 3;
    }

    message InstructionPoint {
      typesv2.common.VisualElement icon = 1;
      typesv2.common.Text title = 2;
      // this is specific to Android
      int32 title_text_size = 3;
    }
  }
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=7vaja8dLnmbHqoOM-0
message LoansDashboardScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text page_title = 2;
  typesv2.common.Text page_subtitle = 3;
  LoanDashboardTopSection top_section = 4;
  LoanDashboardBottomSection bottom_section = 5;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 6;
}

message LoanDashboardTopSection {
  repeated SectionDivision divisions = 1;

  // A section division can contain multiple components.
  message SectionDivision {
    typesv2.ui.IconTextComponent title = 1;
    typesv2.ui.IconTextComponent redirect_text = 2;
    repeated Component components = 3;
  }

  message Component {
    oneof component {
      CardSemiCircularProgress card_semi_circular_progress = 1;
    }
  }
}

message LoanDashboardBottomSection {
  repeated SectionDivision divisions = 1;

  // A section division can contain multiple components.
  message SectionDivision {
    typesv2.ui.IconTextComponent title = 1;
    typesv2.ui.IconTextComponent redirect_text = 2;
    repeated Component components = 3;
  }

  message Component {
    oneof component {
      CardWithStageProgress card_with_stage_progress = 1;
      CardWithCircleProgress card_with_circle_progress = 2;
      CardWithCalenderProgress card_with_calender_progress = 3;
      CardWithMultiColumnsAndRows card_with_multi_columns_and_rows = 4;
      BannerWithLeftIconAndBottomRightCta banner_with_left_icon_and_bottom_right_cta = 5;
      SingleColumnLineItems single_column_line_items = 6;
      CardWithLineProgress card_with_line_progress = 7;
      BannerWithRightIconAndBottomLeftCta banner_with_right_icon_and_bottom_left_cta = 8;
      BannerWithLeftIconAndRightCta banner_with_left_icon_and_right_cta = 9;
    }
  }
}

// Screen with additional description: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=356-50072&mode=design&t=WDfFPySvpBiGCTT7-0
// Screen with CTA: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=284-14097&mode=design&t=WDfFPySvpBiGCTT7-0
message LoansFailureScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // deprecated in favor of `components` fields.
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement page_header = 3 [deprecated = true];
  // deprecated in favor of `components` fields.
  Description description = 4 [deprecated = true];
  // button to be shown below the failure text
  // this can be used to provide user an alternative option to proceed further
  frontend.deeplink.Button cta = 5;
  // cta to be shown at the bottom of the page.
  // Should be used to provide user an option to go back.
  frontend.deeplink.Button back_cta = 6;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 7;
  repeated LoansFailureScreenComponent components = 8;
  message Description {
    typesv2.common.Text title = 1;
    repeated frontend.deeplink.InfoItem detail_items = 2;
  }
}


message LoansFailureScreenComponent {
  oneof component {
    VisualElementTitleSubtitleComponent title_with_image = 1;
    NoLoanOfferReasons reasons_list = 2;
    VerticalKeyValuePairComponent key_value_pair = 3;
    Description description = 4;
    VisualElementComponent visual_element = 5;
    ButtonComponent button = 6;
  }

  message Description {
    typesv2.common.Text title = 1;
    repeated frontend.deeplink.InfoItem detail_items = 2;
    int32 top_margin = 3;
  }
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16463&mode=design&t=hUQ1W9x7CV6BJTYF-0
message LoansOverviewScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // meta data
  string loan_id = 7;
  // page top banner title and description
  typesv2.common.Text page_title = 2;
  typesv2.common.Text page_subtitle = 3;
  // top card (black)
  LoansOverviewTopSection top_section = 4;
  // bottom card (white)
  LoansOverviewBottomSection bottom_section = 5;
  // meta data
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 6;
}

message LoansOverviewTopSection {
  repeated SectionDivision divisions = 1;
  // to show vendor icon and name
  // in reference with https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41121-22856&mode=design&t=PXOPRVc6BEJbzHDi-4
  typesv2.ui.IconTextComponent partner_component = 2;

  // A section division can contain multiple components.
  message SectionDivision {
    typesv2.ui.IconTextComponent title = 1;
    repeated Component components = 2;
  }

  message Component {
    oneof component {
      CardWithLineProgressAndCta card_with_line_progress_and_cta = 1;
    }
  }
}

message LoansOverviewBottomSection {
  repeated SectionDivision divisions = 1;

  // A division can contain multiple components.
  message SectionDivision {
    typesv2.ui.IconTextComponent title = 1;
    typesv2.ui.IconTextComponent redirect_text = 2;
    repeated Component components = 3;
  }

  message Component {
    oneof component {
      CardWithCalenderProgress card_with_calender_progress = 1;
      CardWithMultiColumnsAndRows card_with_multi_columns_and_rows = 2;
      SingleColumnLineItems single_column_line_items = 3;
      BannerWithLeftIconAndBottomRightCta banner_with_left_icon_and_bottom_right_cta = 4;
    }
  }
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=1304-13428&mode=design&t=hUQ1W9x7CV6BJTYF-0
message LoansPaymentDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text page_title = 2;
  typesv2.ui.IconTextComponent footer = 4;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 5;
  string loan_id = 6;
  TabView tab_view = 7;
  typesv2.common.VisualElement share_icon = 8;
  string share_bottom_screen_title = 9;
  string share_message_content = 10;
  string bg_color = 11;

  message TabView {
    repeated NavigationTab tabs = 1;
    string selected_tab_bg_color = 2;
    string selected_tab_text_color = 3;
    string unselected_tab_bg_color = 4;
    string unselected_tab_text_color = 5;
  }

  message NavigationTab {
    typesv2.ui.IconTextComponent title = 1;
    repeated Division divisions = 2;
  }

  // A section division can contain multiple components.
  message Division {
    typesv2.ui.IconTextComponent title = 1;
    repeated Component components = 2;
    typesv2.ui.IconTextComponent footer = 3;
  }

  message Component {
    oneof component {
      DoubleColumnLineItemsDetails double_column_line_items = 1;
      SingleColumnLineItems single_column_line_items = 2;
    }
  }
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15163&mode=design&t=hUQ1W9x7CV6BJTYF-0
message LoansDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text page_title = 2;
  repeated Component components = 3;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 4;
  message Component {
    oneof component {
      KeyValueRowsComponent key_value_rows_component = 1;
    }
  }
}

message InitiateLoansSiSetupInfo {
  string loan_account_id = 1;
  deeplink_screen_option.ScreenOptionHeader header = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 4;
}

message LoansCheckEligibilityScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  typesv2.common.Text progress_title = 4;
}

// can use VerifyDetails Rpc with this
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=35395-22733&mode=design&t=Db39zXl0zYjKb3lG-4
message LoansFormDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // meta data
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  typesv2.common.Text title = 4;
  typesv2.common.Text desc = 5;
  string bg_colour = 6;
  repeated typesv2.FormField form_fields = 7;
  typesv2.common.VisualElement partnership_logo = 8;
  repeated frontend.deeplink.TermInfo term_infos = 9;
  frontend.deeplink.Button continue_button = 10;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=34310%3A53694&mode=design&t=qMHivEgLGpTxg8aY-1
message LoanSelfieScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement visual_element = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  typesv2.common.Text footer = 5;
  // Cta for the user to start selfie
  frontend.deeplink.Cta cta = 6;
  // loan header
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 7;
  // it holds the loan request id
  string loan_request_id = 8;
}

// screen: LOANS_INCOME_VERIFICATION_INTRO_SCREEN
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40718&mode=design&t=fEGbs85eYqgpYdX1-0
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9996-49595&mode=design&t=fEGbs85eYqgpYdX1-0
message LoansIncomeVerificationIntroScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement header_component = 3;
  ListItemComponent list_item_component = 4;
  // waiting info to be shown at the bottom of the page above the bottom CTA
  // eg. you will be notified when we have verified your income
  typesv2.ui.IconTextComponent bottom_waiting_info = 6;
  // CTA to be shown at the bottom of the page
  // On clicking, SubmitIncomeVerificationOption RPC should be called
  frontend.deeplink.Cta bottom_cta = 7;
  string loan_request_id = 8;
  bool fetch_screen_options = 9;
  // event properties to be published by client along with screen loaded event
  map<string, string> event_properties = 10;

  message ListItemComponent {
    string bg_color = 1;
    repeated ListItem list_items = 2;
  }

  // represents a single selectable/non-selectable list item
  message ListItem {
    typesv2.common.VisualElement left_icon = 1;
    // to show reasoning for unavailability. will be populated only if item is not available to select
    // eg. coming soon, waiting for status etc
    typesv2.ui.IconTextComponent right_icon_text_component = 2;
    // eg. connect your account
    typesv2.common.Text title = 3;
    // eg. powered by epifi wealth
    typesv2.ui.IconTextComponent subtitle = 4;
    // if true, radio button should be shown and user should be able to select and right_icon_text_component can be ignored
    // if false, radio button should NOT be shown and user should NOT be able to select and right_icon_text_component to be displayed on the right
    bool is_radio_selection_available = 5;
    // if true, radio button should be in selected state
    bool is_selected = 6;
    // id to uniquely identify the selected item
    string id = 7;
    // checkboxes to be shown at the bottom of the page above the bottom CTA when this item is selected
    repeated typesv2.common.ui.widget.CheckboxItem checkbox_items = 8;
  }
}

// screen: LOANS_INCOME_VERIFICATION_RESULT_SCREEN
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40545&mode=design&t=fEGbs85eYqgpYdX1-0
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10109-41931&mode=design&t=fEGbs85eYqgpYdX1-0
message LoansIncomeVerificationResultScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement header_component = 3;
  BankAccountsBlock bank_accounts_block = 4;
  AlternateOptionsBlock alternate_options_block = 5;
  // checkboxes to be shown at the bottom of the page above the bottom CTA
  repeated typesv2.common.ui.widget.CheckboxItem checkbox_items = 6;
  // CTA to be shown at the bottom of the page
  frontend.deeplink.Cta bottom_cta = 7;
  string loan_request_id = 8;
  // event properties to be published by client along with screen loaded event
  map<string, string> event_properties = 9;

  message AlternateOptions {
    typesv2.common.VisualElement left_icon = 1;
    typesv2.common.VisualElement right_icon = 2;
    typesv2.common.Text text = 3;
    frontend.deeplink.Deeplink deeplink = 4;
  }

  message AlternateOptionsBlock {
    typesv2.common.Text title = 1;
    string bg_color = 2;
    repeated AlternateOptions alternate_options = 3;
  }

  message BankAccountsBlock {
    repeated BankAccountItem bank_account_items = 1;
    // to be used only when there are more than 1 BankAccountItem
    // eg. 4 Bank Accounts
    typesv2.common.Text multi_bank_text = 2;
    string bg_color = 3;
    typesv2.common.VisualElement right_visual_element = 4;
  }

  message BankAccountItem {
    typesv2.common.VisualElement logo = 1;
    // ignore the below 2 fields if there are more than 1 accounts
    // eg. HDFC bank
    typesv2.common.Text title = 2;
    // eg. 2 Accounts, ••1234
    typesv2.common.Text sub_title = 3;
  }
}

// screen: LOANS_MANDATE_INITIATE_SCREEN_V2
// figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9767-33940&mode=design&t=GRyn8WVk9G8XIEld-0
message LoansMandateInitiateV2ScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string lse_id = 4;
  // render image, title and subtitle on the screen
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement top_component = 5;
  // generic content which has to be rendered after top component
  // initial use-case is to render mandate steps, but to keep it extensible keeping this as a list
  repeated MiddleComponent middle_components = 6;
  // Account details component to be render default or selected account by the user for disbursal and emi
  // if this field is not populated then this view should be rendered
  AccountDetailsComponent account_details_component = 7;
  // cta button at the bottom
  frontend.deeplink.Button cta = 8;
  frontend.analytics.AnalyticsScreenName screen_name = 9;
  // screen toolbar title
  typesv2.common.Text toolbar_title = 10;
  // Used to show the partner logo
  ui.IconTextComponent partner_logo = 11;

  message MiddleComponent {
    oneof component {
      // this component will contain steps mentioned in mandate screen
      // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50089&mode=design&t=GRyn8WVk9G8XIEld-0
      KeyValueRowsComponent key_value_rows_component = 1;
    }
  }

  message AccountDetailsComponent {
    typesv2.common.Text title_text = 1;
    // this component will default disbursal and emi account on mandate screen
    // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50089&mode=design&t=GRyn8WVk9G8XIEld-0
    AccountDetailsView selected_or_default_account_details = 2;
  }
}

// screen: LOANS_ALTERNATE_ACCOUNTS_SCREEN
// figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50117&mode=design&t=2x255heXsl6qSXfq-0
message LoansAlternateAccountsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.analytics.AnalyticsScreenName screen_name = 3;
  string loan_request_id = 4;
  string lse_id = 5;
  // title text which has to be rendered at top of the view
  ui.IconTextComponent title_text_itc = 6;
  // list of accounts which are to be shown on the list
  repeated AccountDetailsView account_data_list = 7;
  // itc specifying whether to show new account cta or not
  ui.IconTextComponent add_new_account_cta = 8;
}

// screen: LOANS_MANDATE_SETUP_SCREEN
// figma: it can be digio sdk or webview
message LoansMandateSetupScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.analytics.AnalyticsScreenName screen_name = 3;
  string loan_request_id = 4;
  string lse_id = 5;
  // mandate view info will specify what view has to be loaded with what parameters
  MandateViewInfo mandate_view_info = 6;
  // if this field populated then show overlay on mandate screen
  MandateOverlayDetails mandate_overlay_details = 7;
  // if this field is populated then show dropoff view
  MandateDropOffDetails mandate_dropoff_details = 8;
}

// common mandate info message which can be used across different deeplinks if need arises in the future
message MandateViewInfo {
  // specifies what view has to be loaded
  MandateViewType mandate_view_type = 1;
  // view specific info
  oneof view_info {
    SdkInfo sdk_info = 2;
    WebPageInfo web_page_info = 3;
  }

  message SdkInfo {
    // specifies which sdk has to be triggered
    MandateSdkVendor mandate_sdk_vendor = 1;
    oneof info {
      // digio vendor info
      DigioInfo digio_info = 2;
    }
  }

  message DigioInfo {
    // mandate id received from liquiloans API
    string mandate_id = 1;
    typesv2.common.PhoneNumber contact_number = 2;
  }

  message WebPageInfo {
    string entry_url = 1;
    string exit_url = 2;
    // encoded html which has to be surfaced on client
    string base64_encoded_html = 3;
  }
}

message MandateOverlayDetails {
  // title component to show the
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50734&mode=dev
  frontend.deeplink.IconTextWidget mandate_overlay_title = 1;
  // delay duration which client has to use after which cta has to be loaded
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50388&mode=dev
  google.protobuf.Duration view_card_details_cta_delay = 2;
  // debit card which client has to use to trigger pin verification and fetch card cvv details
  // if this field is empty then do not trigger above specified flow
  string debit_card_id = 3;
}

message MandateDropOffDetails {
  // Deeplink to nudge the user to continue the mandate process when user tries to go back
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50534&mode=dev
  // can repurpose BOTTOM_SHEET_INFO_VIEW for this since the UI structure is same
  frontend.deeplink.Deeplink mandate_dropoff_deeplink = 1;
}

// screen: LOANS_PWA_LANDING_SCREEN
message LoansPWALandingScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // loan header
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // denotes the unique identifier for the loan request/account.
  oneof loan_identifier {
    string loan_request_id = 3;
    string loan_account_id = 4;
  }
}

// screen : LOANS_BOTTOM_SHEET_SCREEN
message LoansBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // denotes the image/animation to be displayed on the bottom sheet.
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-40109&mode=design&t=HaJNCIwtunJUHdcu-0
  typesv2.common.VisualElement visual_element = 2;
  // denotes the title for the bottom sheet like "Need help? Get in touch with Moneyview support"
  typesv2.common.Text title = 3;
  // denotes the list of infos to be displayed on the bottom sheet.
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-40109&mode=design&t=HaJNCIwtunJUHdcu-0
  repeated Info infos = 4;
  // denotes the subtitle text for bottom sheet to be show right below title. Middle aligned
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41248-56923&mode=design&t=VBD155dYlKlB7E7z-4
  typesv2.common.Text sub_title = 5;
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-40109&mode=design&t=eteHVC9TMcosedZq-0
  message Info {
    // denotes the value which should be displayed in the info section e.g "(+91) 9812341234"
    typesv2.common.Text display_value = 1;
    // denotes the value which should get copied on the clipboard on copy action on the info element e.g "919812341234"
    // **Note** : this value shouldn't be displayed on the info section.
    string copy_value = 2;
    // denotes whether the info is copyable or not.
    bool is_copyable = 3;
  }

  NavBar nav_bar = 6;
  message NavBar {
    // denotes image/animation to be displayed on top right
    typesv2.common.VisualElement visual_element = 1;
  }

  frontend.deeplink.Cta cta = 7;
}

// screen: APPLY_FOR_LOAN_SCREEN
message ApplyForLoanScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // BE will send offer_id in screen options since it will be required in apply for loan rpc request
  string offer_id = 3;
}

// screen: LOANS_TIMED_LOADER_SCREEN
message LoansTimedLoaderScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.ui.widget.BackgroundColour bg_color = 2;
  // e.g: "Redirecting to IDFC First Bank for your video KYC"
  typesv2.common.Text title = 3;
  // if true, loader will be hidden
  bool hide_loader = 4;
  // duration (in milliseconds) client needs to wait before navigating to next screen
  int32 duration_in_milliseconds = 5;
  // deeplink to be opened after the duration
  frontend.deeplink.Deeplink deeplink = 6;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 7;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 8;
  typesv2.common.VisualElement loader_visual_element = 9;
}

// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9532-43792&mode=dev
message LoansLandingScreenV2 {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  ui.IconTextComponent nav_bar_cta = 3 [deprecated = true];

  repeated LoansScreenUiComponents components = 4;

  typesv2.common.VisualElement background_image = 5;
  string offerId = 6;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=42772-54548&mode=design&t=VBD155dYlKlB7E7z-4
  HamburgerComponent nav_bar_hamburger_item = 7;
}

// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9532-43735&mode=dev
message LoansAmountSelectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // helper icon to be shown on nav bar
  ui.IconTextComponent nav_bar_cta = 3;

  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 4;

  // card view of loan amount selection
  typesv2.common.Text card_title = 5;
  typesv2.Money default_amount = 6;
  repeated ui.IconTextComponent features = 7;
  typesv2.common.ui.widget.BackgroundColour card_bg_color = 8;

  frontend.deeplink.Button cta = 9;

  typesv2.Money min_loan_amount = 10;
  typesv2.Money max_loan_amount = 11;
  string offerId = 12;
}

// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9747-163118&mode=dev
message LoanDetailSelectionScreenV2 {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // helper icon to be shown on nav bar
  ui.IconTextComponent nav_bar_cta = 3;

  string offer_id = 4;
  typesv2.common.Text page_title = 5;

  repeated Component components = 6;

  message Component {
    oneof component {
      CollapsedLoanAmountSelectionComponent collapsed_loan_amount_selection_component = 1;
      LoanTenureOrEmiSelectionComponent tenure_or_emi_selection_component = 2;
      IconTextWithMarginComponent icon_text_with_margin_component = 3;
      ClickableTileComponent clickable_tile_component = 4;
    }
  }
}

message LamfLoanDetailsVerificationScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string loan_step_id = 4;
  repeated LamfLoanDetailsVerificationScreenComponent components = 5;
  // text anchored at the bottom, above the cta.
  typesv2.common.Text bottom_anchored_text = 6;
  frontend.deeplink.Button cta = 7;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 8;
}

message LamfLoanDetailsVerificationScreenComponent {
  oneof component {
    VerticalKeyValuePairComponent key_value_pair = 1;
    IconTextWithMarginComponent icon_text = 2;
    MfSchemeListComponent mf_scheme_list = 3;
  }
}

message LamfFundVerificationBottomSheet {
  deeplink_screen_option.ScreenOptionHeader header = 5;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement title = 1;
  repeated typesv2.ui.VerticalKeyValuePair questions = 2;
  FundDetailsUpdateInfo details_update_info = 3;
  frontend.deeplink.Button cta = 4;

  message FundDetailsUpdateInfo {
    typesv2.common.Text title = 1;
    typesv2.ui.IconTextComponent url = 2;
    typesv2.common.Text phone_text = 3;
    typesv2.common.Text email_text = 4;
  }
}

// screen: LOANS_DEBIT_CARD_DETAILS_SCREEN
// figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=39156-13848&mode=design&t=W03zfTMXwYDWoqvq-0#672660734
message LoansDebitCardDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title_text = 3;
  typesv2.common.Text subtitle_text = 4;
  // cta which needs to be shown below card image
  frontend.deeplink.IconTextWidget view_card_details_cta = 5;
  // note section informing user if they want to note dc details
  RecommendationNoteSection recommendation_note_section = 6;
  // this map contains info on what bottom sheet has to be shown for which error code
  // if client faces any issue while fetching dc details using dc RPC then appropriate error views has to be surfaced
  // client can use error code as key while retrieving error view from below map
  map<uint32, frontend.errors.ErrorView> err_code_to_err_view_map = 7;
  // active debit card id of that user
  string debit_card_id = 8;
  frontend.deeplink.Button continue_cta = 9;

  message RecommendationNoteSection {
    typesv2.common.VisualElement note_visual_element = 1;
    typesv2.common.Text text = 2;
  }
}

// defining LoansMandateDigioSdkScreenOptions message to init digio SDK on Android client side
// this has to be only used when user is using fi account to setup mandate and want to fetch dc details before setting up mandate
// this screen option has to be used or passed in continue_cta in LoansDebitCardDetailsScreenOptions
// For non-fi account or iOS client please use LoansMandateSetupScreenOptions as a thumb rule
// this is done only for Android client to decrease screen navigation complexity as highlighted by Ganesh
message LoansMandateDigioSdkScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 4;
  string lse_id = 5;
  // init params to start Digio SDK on client
  DigioSdkInitParams init_params = 6;
  // additional params required by client to show some additional display component on SDK
  DisplayParams display_params = 7;
  message DisplayParams {
    typesv2.common.Text toolbar_title = 1;
  }
}

message DigioSdkInitParams {
  // mandate id received from liquiloans API
  string mandate_id = 1;
  typesv2.common.PhoneNumber contact_number = 2;
}

message LoansStatusPollScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  PollingView polling_view = 4;
  int32 retry_attempt_number = 5;
  int32 retry_delay = 6;
  int32 retry_duration = 7;
  typesv2.common.ui.widget.BackgroundColour bg_colour = 8;
  // cta to be rendered at the bottom of the screen
  // if empty, hide
  frontend.deeplink.Button bottom_cta = 9;
  // flag to be used if next action needs to be fetched in sync using the sync proxy workflow.
  bool get_next_action_in_sync = 10;
  // deprecated as this can be fetched using loan_request_id
  string workflow_id = 11 [deprecated = true];
  // unique identifier for a polling screen.
  // client can use it to check if view needs to be updated or not.
  string id = 12;
  // OPTIONAL - can be used to override the default screen name sent by client in analytics events.
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 13;
}

message PollingView {
  oneof view {
    TextWithVisualView polling_text_with_visual_view = 1;
    TimerView timer_view = 2;
    CarousalView carousal_view = 3;
  }

  message TimerView {
    // time in seconds
    int32 max_time = 1;
    typesv2.ui.VerticalKeyValuePair polling_text = 2;
    // list of banners to be shown
    ItcBannerList banner_list = 3;
    // items to be shown on screen once `max_time` has passed
    TimeoutView timeout_view = 4;
    // a random string used as a unique identifier for a particular timer view.
    // This is needed for client to decide whether to refresh the timer or not when it receives timer view during polling.
    string id = 5;
    bool hide_loader = 6;

    message TimeoutView {
      typesv2.common.ui.widget.VisualElementTitleSubtitleElement text_with_visual = 1;
      frontend.deeplink.Button bottom_cta = 2;
      bool hide_loader = 3;
    }
  }

  message TextWithVisualView {
    typesv2.common.ui.widget.VisualElementTitleSubtitleElement text_with_visual = 1;
    ItcBannerList banner_list = 2;
    bool hide_loader = 3;
  }
}

message CarousalView {
  // List of carousel items to display
  repeated CarousalObject carousal_objects = 1;
  // Whether the carousel should loop back to the beginning after reaching the end
  bool is_cyclic = 2;
  // Whether to hide the loading indicator
  bool hide_loader = 3;
  // to decide wether to send deeplink or rpc
  api.typesv2.deeplink_screen_option.preapprovedloans.LoansCtaAction action = 4;

}
message CarousalObject {
  // The UI element to display (could be a Lottie animation, image, etc.)
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement visual_element = 3;

  // How long this item should be displayed in milliseconds
  // (overrides auto_advance_duration_ms from CarousalView)
  int32 display_duration_ms = 5;
}

message ItcBannerList {
  repeated ItcBanner banners = 1;
  // duration after which next banner should be shown. if 0, auto-swipe should be disabled.
  int32 auto_swipe_duration_in_ms = 2;
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6199-19081&mode=dev
message ItcBanner {
  typesv2.ui.IconTextComponent title = 1;
  typesv2.ui.IconTextComponent body = 2;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5722-42359&mode=design&t=9fvkFSsYXGLMsk2O-0
message LamfSkipLinkScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.VisualElement icon = 3;
  typesv2.common.Text title = 4;
  typesv2.common.Text subtitle = 5;
  typesv2.ui.IconTextComponent info_label = 6;
  DualVerticalKeyValuePairComponent amount_info_component = 7;
  frontend.deeplink.Button primary_cta = 8;
  frontend.deeplink.Cta bottom_text_cta = 9;
}

// Screen options to link phone email in LAMF
// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5674-30676&mode=design&t=9fvkFSsYXGLMsk2O-0
// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5679-31208&mode=design&t=9fvkFSsYXGLMsk2O-0
message LamfLinkScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  repeated Component before_components = 5;
  repeated Component after_components = 6;
  typesv2.ui.IconTextComponent partner_logo = 7;
  frontend.deeplink.Button cta_link = 8;
  frontend.deeplink.Cta skip_cta = 9;
  ErrorBanner before_error_banner = 10;
  ErrorBanner after_error_banner = 11;
  bool is_cta_link_enabled = 12;
  string loan_req_id = 13;
  string loan_step_id = 14;
  typesv2.ui.IconTextComponent help_text = 15;
  google.protobuf.Timestamp component_change_time = 16;
  string analytics_screen_name = 17;

  message Component {
    oneof component {
      LinkedMFComponent linked_mf_component = 1;
      UnLinkedMFComponent unlinked_mf_component = 2;
    }
  }
}

message LinkedMFComponent {
  typesv2.common.VisualElement icon = 1;
  typesv2.ui.IconTextComponent title = 2;
  typesv2.common.Text subtitle = 3;
  typesv2.common.ui.widget.BackgroundColour bg_color = 7;
  int32 top_margin = 8;

}

message UnLinkedMFComponent {
  typesv2.common.VisualElement icon = 1;
  typesv2.ui.IconTextComponent title = 2;
  typesv2.ui.IconTextComponent link_cta = 3;
  MfPortfolioEligibilityDetailsScreen.SchemeList unlinked_funds = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  int32 top_margin = 6;
  bool is_expanded = 7;
}

message LoansRecordUserActionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // screen on which the action was called. Client needs to pass this in RecordUserAction rpc request.
  frontend.deeplink.Screen screen = 3;
  // encoded action details. client needs to pass this in RecordUserAction rpc request.
  string action_details = 4;
  // client needs to send whichever id is present in  screenOptions to RecordUserAction() rpc
  string loan_request_id = 5;
  string loan_step_id = 6;
  string loan_id = 7;
}

message LoansVkycRedirectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
}

// LoansCalculationBottomSheetScreenOptions can be used where ever some calculations(showing breakdown, or summation of some charges etc..)
// needs to be shown on the bottom sheet.
message LoansCalculationBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  common.ui.widget.VisualElementTitleSubtitleElement title = 3;
  repeated ui.VerticalKeyValuePair faqs = 4;
  KeyValueRowsComponent calculations_component = 5;
  frontend.deeplink.Button cta = 6;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41416-154329&mode=design&t=zpA35OflGRRe5Hr0-4
message LoansDocumentsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_account_id = 3;
  typesv2.common.Text toolbar_title = 4;
  // list of documents
  repeated DocumentItem documents = 5;
  string bg_color = 6;

  message DocumentItem {
    typesv2.common.VisualElement left_icon = 1;
    VerticalKeyValuePairComponent vertical_key_value_pair = 2;
    // to pass in RPC request
    string document_id = 3;
    string bg_color = 4;
    // if document is available or not for the user
    bool is_disabled = 5;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52155&mode=design&t=fTYpRhjsruhHypBT-4
message LoansPrepayV2ScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text toolbar_title = 3;
  typesv2.ui.IconTextComponent toolbar_right_cta = 4;
  typesv2.common.Text pre_pay_title = 5;
  typesv2.Money default_pre_pay_amount = 6;
  typesv2.ui.IconTextComponent suggestion_badge = 7;
  ChooseAmountBottomContainer choose_amount_bottom_container = 8;
  string loan_account_id = 9;
  // the account against which payment is being made, used in lows like LAMF where we have separate accounts for
  // principal outstanding and interest amounts
  string loan_account_type_identifier = 10;

  message ChooseAmountBottomContainer {
    typesv2.ui.IconTextComponent title = 1;
    typesv2.Money slider_min_amount = 2;
    typesv2.Money slider_max_amount = 3;
    frontend.deeplink.Button primary_button = 4;
    // this can be used to represent emi amount, over_due amount
    // if due_amount is not equal to slider_max_amount, max_amount can be considered as fore-closure
    typesv2.Money due_amount = 5;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52183&mode=design&t=fTYpRhjsruhHypBT-4
message LoansRepaymentMethodsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_account_id = 3;
  frontend.preapprovedloan.pal_enums.RepaymentDetailsType repayment_details_type = 4;
  typesv2.common.Text toolbar_title = 5;
  typesv2.ui.IconTextComponent toolbar_right_cta = 6;
  typesv2.common.Text pre_pay_title = 7;
  typesv2.Money default_pre_pay_amount = 8;
  typesv2.ui.IconTextComponent edit_icon = 9;
  KeyValueRowsComponent amount_breakup_component = 10;
  ChoosePaymentMethodBottomContainer choose_payment_method_bottom_container = 11;
  // ITC to show addition banner style message below the amount breakup component
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=42985-13916&mode=design&t=VBD155dYlKlB7E7z-4
  typesv2.ui.IconTextComponent additional_message_component = 12;
  // the account against which payment is being made, used in flows like LAMF where we have separate accounts for
  // principal outstanding and interest payment
  string loan_account_type_identifier = 13;
  // it helps to check that that the pin set flow is required or not
  bool skip_pin_set_check = 14;

  message ChoosePaymentMethodBottomContainer {
    typesv2.ui.IconTextComponent title = 1;
    AccountDetailsView account_details_view = 2;
    repeated typesv2.common.Text consent_texts = 3;
    frontend.deeplink.Button slider = 4;
    typesv2.ui.IconTextComponent sub_title = 5;
    string bg_color = 6;
  }
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52243&mode=design&t=vBzUvqfnnSiSHxXC-4
message LoansPayViaCXScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  VisualElementTitleSubtitleComponent top_component = 3;
  ClosureDetailsComponent closure_details_component = 4;
  frontend.deeplink.Button primary_cta = 5;
  frontend.deeplink.Button secondary_cta = 6;
  string loan_account_id = 7;

  message ClosureDetailsComponent {
    typesv2.ui.IconTextComponent title = 1;
    repeated typesv2.ui.IconTextComponent steps = 2;
    string bg_color = 3;
  }
}

message LoansGenericInformationBottomSheet {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement title = 3;
  repeated typesv2.ui.VerticalKeyValuePair faqs = 4;
  frontend.deeplink.Button cta = 5;
}

message LoansMobileNumberIntroScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  VisualElementTitleSubtitleComponent top_component = 4;
  typesv2.ui.IconTextComponent toolbar_right_cta = 5;
  frontend.deeplink.Button primary_cta = 6;
  frontend.deeplink.Button secondary_cta = 7;
}

// figma link: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=14169-14092&mode=design&t=aiZJNa1FTxB04wvS-0
message LoansGenericIntroScreen {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement top_component = 3;
  repeated Component components = 4;
  frontend.deeplink.Button cta = 5;
  repeated typesv2.common.ui.widget.CheckboxItem consents = 7;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 8;

  message Component {
    oneof component {
      // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=14169%3A14092&mode=design&t=aiZJNa1FTxB04wvS-1
      KeyValueRowsComponent vertical_list = 1;
      VisualElementComponent partnership_logo = 2;
    }
  }
}

// Screen: REVISED_LOAN_OFFER_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48596&t=ineZGXSL5c6MuaPK-0
message RevisedLoanOfferCustomPlanSelectionBottomSheet {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;
  string loan_request_id = 4;

  string bg_color = 5;
  ui.IconTextComponent help_icon = 6;
  typesv2.common.VisualElement top_image = 7;
  typesv2.common.Text title = 8;

  AmountSelectionComponent amount_selection_component = 9;
  TenureSelectionComponent tenure_selection_component = 10;

  frontend.deeplink.Cta cancel_button = 11;
  frontend.deeplink.Cta save_button = 12;

  message AmountSelectionComponent {
    typesv2.common.Text title = 1;
    typesv2.Money default_amount = 2;
    typesv2.Money min_amount = 3;
    typesv2.Money max_amount = 4;
    typesv2.common.Text slider_min_label = 5;
    typesv2.common.Text slider_max_label = 6;
    // count of total steps
    double step_size = 7 [deprecated = true];
    // this is specific for android to support the step size in the xml based slider
    // where instead of taking step counts it takes difference of value betweent two steps
    int32 step_interval = 8;
    // step_count meaning how many steps are there in the slider each with same interval value,
    // slider step count should always be actual_count-1 because the end(max) value is inclusive
    // example for 5000 to 50000 slider min max values - min=5000, max=50000, step_interval=500, step_count=89
    int32 step_count = 9;
  }

  message TenureSelectionComponent {
    typesv2.common.Text title = 1;
    int32 default_tenure_in_months = 2;
    int32 min_tenure_in_months = 3;
    int32 max_tenure_in_months = 4;
    typesv2.common.Text slider_min_label = 5;
    typesv2.common.Text slider_max_label = 6;
    int32 step_size = 7;
    typesv2.common.Text emi_tag_label = 8;
    // e.g - Reduce the loan amount to unlock more tenure options
    ui.IconTextComponent subtitle = 9;
  }
}

// figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-64651&m=dev
message LoansCustomPlanSelectionBottomSheet {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  string bg_color = 3;
  ui.IconTextComponent help_icon = 4;
  typesv2.common.VisualElement top_image = 5;
  typesv2.common.Text title = 6;

  AmountSelectionComponent amount_selection_component = 7;
  TenureSelectionComponent tenure_selection_component = 8;

  frontend.deeplink.Cta cancel_button = 9;
  frontend.deeplink.Cta save_button = 10;
  string offer_id = 11;
  message AmountSelectionComponent {
    typesv2.common.Text title = 1;
    typesv2.Money default_amount = 2;
    typesv2.Money min_amount = 3;
    typesv2.Money max_amount = 4;
    typesv2.common.Text slider_min_label = 5;
    typesv2.common.Text slider_max_label = 6;
    double step_size = 7 [deprecated = true];
    // this is specific for android to support the step size in the xml based slider
    // where instead of taking step counts it takes difference of value betweent two steps
    int32 step_interval = 8;
    // step_count meaning how many steps are there in the slider each with same interval value,
    // slider step count should always be actual_count-1 because the end(max) value is inclusive
    // example for 5000 to 50000 slider min max values - min=5000, max=50000, step_interval=500, step_count=89
    int32 step_count = 9;

  }

  message TenureSelectionComponent {
    typesv2.common.Text title = 1;
    int32 default_tenure_in_months = 2;
    int32 min_tenure_in_months = 3;
    int32 max_tenure_in_months = 4;
    typesv2.common.Text slider_min_label = 5;
    typesv2.common.Text slider_max_label = 6;
    int32 step_size = 7;
    typesv2.common.Text emi_tag_label = 8;
    // e.g - Reduce the loan amount to unlock more tenure options
    ui.IconTextComponent subtitle = 9;
  }
}

// Screen: REVISED_LOAN_OFFER_DETAILS_SCREEN
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48955&t=ineZGXSL5c6MuaPK-0
message RevisedLoanOfferDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;
  string loan_request_id = 4;

  AmountSelectionSection amount_selection_section = 5;
  LoanPlanSelectionSection loan_plan_selection_section = 6;
  frontend.deeplink.Cta submit_cta = 7;
  VisualElementComponent partner_visual_element_component = 8;
  string bg_color = 9;
  ui.IconTextComponent toolbar_right_cta = 10;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=46090-2465&t=QHGY3NC65qoWvFHS-4
  message AmountSelectionSection {
    typesv2.common.Text card_title = 1;
    typesv2.common.VisualElement card_icon = 2;

    typesv2.common.Text collapsed_view_title = 3;
    typesv2.Money collapsed_view_amount = 4;

    typesv2.common.ui.widget.BackgroundColour bg_color = 5;
    bool is_collapsed_view = 6;
    typesv2.Money min_loan_amount = 7;
    typesv2.Money max_loan_amount = 8;

    typesv2.Money default_loan_amount = 9;
    typesv2.common.Text amount_slider_min_label = 10;
    typesv2.common.Text amount_slider_max_label = 11;
    int32 step_size = 12 [deprecated = true];
    // this is specific for android to support the step size in the xml based slider
    // where instead of taking step counts it takes difference of value betweent two steps
    int32 step_interval = 13;
    // step_count meaning how many steps are there in the slider each with same interval value,
    // slider step count should always be actual_count-1 because the end(max) value is inclusive
    // example for 5000 to 50000 slider min max values - min=5000, max=50000, step_interval=500, step_count=89
    int32 step_count = 14;
  }
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-84566&t=QHGY3NC65qoWvFHS-4
  message LoanPlanSelectionSection {
    typesv2.common.Text title = 1;
    repeated ui.IconTextComponent plan_info_tags = 2;
    repeated LoanPlanOption loan_plan_options = 3;
    typesv2.common.VisualElement loading_image = 4;
    string bg_color = 5;
  }
}

message LoansOfferDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string offer_id = 3;

  AmountSelectionSection amount_selection_section = 4;
  LoanPlanSelectionSection loan_plan_selection_section = 5;
  frontend.deeplink.Cta submit_cta = 6;
  VisualElementComponent partner_visual_element_component = 7;
  string bg_color = 8;
  ui.IconTextComponent toolbar_right_cta = 9;
  // this screen will be used in eligibility flow also
  string loan_request_id = 10;
  // flow like eligibility,loan_application etc.
  frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow flow = 11;
  // loan offer eligibility criteria owner against which given data is being collected.
  frontend.preapprovedloan.pal_enums.Vendor loec_owner = 12;
  // usecase of lenden where we do not want user to enter any random amount
  bool is_amount_non_editable = 13;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54513-23574&t=HcHy6CF5e8YkQKzM-4
  message AmountSelectionSection {
    typesv2.common.Text card_title = 1;
    typesv2.Money card_amount = 2 [deprecated = true];
    typesv2.common.VisualElement card_icon = 3;

    typesv2.common.Text collapsed_view_title = 4;
    typesv2.Money collapsed_view_amount = 5;

    typesv2.common.ui.widget.BackgroundColour bg_color = 6;
    bool is_collapsed_view = 7;
    typesv2.Money min_loan_amount = 8;
    typesv2.Money max_loan_amount = 9;

    typesv2.Money default_loan_amount = 10;
    typesv2.common.Text amount_slider_min_label = 11;
    typesv2.common.Text amount_slider_max_label = 12;
    int32 step_size = 13 [deprecated = true];

    // To show the interest rate component .
    ui.IconTextComponent interest_rate = 14;
    // this is specific for android to support the step size in the xml based slider
    // where instead of taking step counts it takes difference of value between two steps
    int32 step_interval = 15;
    // step_count meaning how many steps are there in the slider each with same interval value,
    // slider step count should always be actual_count-1 because the end(max) value is inclusive
    // example for 5000 to 50000 slider min max values - min=5000, max=50000, step_interval=500, step_count=89
    int32 step_count = 16;
  }
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-84566&t=QHGY3NC65qoWvFHS-4
  message LoanPlanSelectionSection {
    typesv2.common.Text title = 1;
    repeated ui.IconTextComponent plan_info_tags = 2;
    repeated LoanPlanOption loan_plan_options = 3;
    typesv2.common.VisualElement loading_image = 4;
    string bg_color = 5;
  }
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=46393-26295&t=q6zuheZrcZJvN1GB-4
message LoansAlternateOfferScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  ApplicationStatusTransitionData application_status_transition_data = 3;
  AlternateOfferData alternate_offer_data = 4;

  message ApplicationStatusTransitionData {
    // Image/Lottie can be shown
    typesv2.common.VisualElement visual_element = 1;
    typesv2.common.Text title = 2;
    typesv2.common.Text subtitle = 3;
    string bg_color = 4;
  }

  message AlternateOfferData {
    typesv2.ui.IconTextComponent toolbar_right_cta = 1;
    typesv2.common.Text title = 2;
    typesv2.common.Text subtitle = 3;
    OfferDetailsComponent offer_details_component = 4;
    BannerWithLeftIconAndBottomRightCta banner_info = 5;
    frontend.deeplink.Cta continue = 6;
    string bg_color_top = 7;
    string bg_color_bottom = 8;

    message OfferDetailsComponent {
      typesv2.common.VisualElement icon = 1;
      typesv2.common.Text title = 2;
      typesv2.ui.IconTextComponent description = 3;
      string divider_color = 4;
      string border_color = 5;
      repeated OfferDetails offer_details = 6;
      string bg_color = 7;

      message OfferDetails {
        typesv2.common.VisualElement left_icon = 1;
        typesv2.ui.VerticalIconTextComponent offer_detail = 2 [deprecated = true];
        typesv2.ui.VerticalKeyValuePair offer_data = 3;
      }
    }
  }
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49837&t=AfIAgEeR8jJ3Srx2-4
message LoanAddressVerificationIntroScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  ui.IconTextComponent address_text = 5;
  ui.IconTextComponent current_location_label = 6;
  string loan_request_id = 7;
  frontend.deeplink.Cta continue_cta = 8;
  string bg_color = 9;
  LoansBottomSheetScreenOptions location_permission_bottom_sheet = 10;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-50114&t=9ZqzZkIVaGc6qZwD-4
message LoanAddNewAddressDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string lse_id = 4;

  ActionBar action_bar = 5;
  message ActionBar {
    typesv2.common.Text title = 2;
    typesv2.common.Text subtitle = 3;
  }

  typesv2.PostalAddress address = 6;
  // this will be used to show any extra info to user below landmark field of address
  // for eg : Filling this will help us improve chances of getting a loan
  typesv2.common.Text info_box = 7;

  ui.IconTextComponent bottom_warning_banner = 8;
  frontend.deeplink.Cta continue_cta = 9;
  // flag to be sent in the subsequent rpc call
  bool add_address_details_in_sync = 10;

  // Use this deeplink to show the LOAN_ADDRESS_AUTOCOMPLETE_BOTTOMSHEET. BE needs to send the screen name only.
  // Figma:- https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49912&t=L7KoARBVyyxuiEd7-4
  frontend.deeplink.Deeplink deeplink = 11;
}

message LoansMultipleOfferSelectionScreenOptions {
  // meta data
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.Money selected_amount = 2;

  typesv2.common.Text toolbar_title = 3;
  ui.IconTextComponent toolbar_help = 4;
  string bg_color = 5;
  repeated LoanOfferDetailsCard offer_details_cards = 6;
  // call getOfferDetails RPC using the loan_offer_id and loan_header for the selected offer
  // deprecated: use LoanOfferDetailsCard -> LoansCta instead to use at the bottom of the screen
  frontend.deeplink.Cta choose_offer_cta = 7 [deprecated = true];
  string selected_banner_border_color = 8;
  // if non is selected, can send -1
  int32 index_of_default_selected = 9;
  //https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-23955&t=bmfcmwui88Ja8DQr-4
  ViewMoreOfferCardComponent unlock_more_offers = 10;
  ui.IconTextComponent faq_label = 11;
  ui.IconTextComponent cta_banner = 12;
  ui.IconTextComponent footer_label = 13;
  // this is used to show the data e.g. Offer may get updated based on your details
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=65413-79527&t=O88mO1OzJOt5mgoL-4
  ui.IconTextComponent footer_secondary_label = 14;
  // if none of the above cards are selected, use this to show the default CTA and CTA state
  frontend.deeplink.Cta default_cta = 15;
  // if true, call GetDeeplink RPC to get the entire screen options
  bool get_data_from_rpc_call = 16;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37325&t=KW7tl7yDgNqbY4lQ-0
message LoansApplicationConfirmationBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string bg_color = 2;
  typesv2.common.VisualElement visual_element = 3;
  typesv2.common.Text title = 4;
  typesv2.common.Text subtitle = 5;
  LoansCta primary_cta = 6;
  LoansCta secondary_cta = 7;
}

message LoansMultipleOfferDetailsScreenOptions {
  // meta data
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.Money selected_amount = 2;

  ScreenTransitionData screen_transition_data = 3;
  ui.IconTextComponent toolbar_help = 4;
  string bg_color = 5;
  Banner loan_amount_banner = 6;
  LoanOfferDetailsCard offer_details_card = 7;
  Banner more_offers_banner = 8;
  // if no back_cta, save cta to be of full width
  frontend.deeplink.Cta save_cta = 9;
  // if back_cta is null, don't show. If not null, use this same as back button
  frontend.deeplink.Cta back_cta = 10;

  message Banner {
    ui.IconTextComponent title = 1;
    ui.IconTextComponent description = 2;
    string bg_color = 3;
    // if empty, do not show shadow
    common.ui.widget.Shadow shadow = 4;
  }
  message ScreenTransitionData {
    // lottie to be shown
    typesv2.common.VisualElement visual_element = 1;
    string bg_color = 2;
  }
}

message LoansAAConsentCollectionScreenOptions {
  // meta data
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  Banner consent_banner = 3;

  message Banner {
    // icon shown at the top of the bottom sheet. It is center aligned.
    typesv2.common.VisualElement icon = 1;
    // title of the bottom sheet, shown just below the icon.
    typesv2.common.Text title = 2;
    // multi-line description text. can contain links to some url (privacy policy, tnc, etc.)
    typesv2.common.Text description = 3;
    // background color of the bottom sheet.
    typesv2.common.ui.widget.BackgroundColour bg_color = 4;
    typesv2.common.Text cta_text = 5;
    // text to be sent to backend when user accepts the consent
    string record_action_details = 6;
    // text to be sent to backend when user is shown the consent
    string record_banner_shown_action_details = 7;
  }
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63471-109989&t=PvE3VAZhZnjtOvUi-4
message LoansConsentScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // flow like eligibility,loan_application etc.
  frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow flow = 3;
  string loan_request_id = 4;
  // Icon to be displayed at the top of the screen
  typesv2.common.VisualElement top_icon = 5;
  // Title for the screen
  typesv2.common.Text title = 6;
  // Subtitle for the screen
  typesv2.common.Text subtitle = 7;
  // Checkbox items list for the screen
  // deprecated, use consents_box instead
  repeated common.ui.widget.CheckboxItem consent_items = 8;
  // CTA for the screen
  // deprecated, use swipe_cta instead
  frontend.deeplink.Cta cta = 9 [deprecated = true];
  // Background color for the screen
  string bg_color = 10;
  // loan offer eligibility criteria owner against which given data is being collected.
  // deprecated, use loan_header data_owner instead
  frontend.preapprovedloan.pal_enums.Vendor loec_owner = 11 [deprecated = true];

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63454-34090&t=WXHzMq429kA2DLtx-4
  // box containing list of consents and title
  ConsentsBox consents_box = 12;

  // CTA with swipe action
  frontend.deeplink.Cta swipe_cta = 13;

  message ConsentsBox {
    // title above and outside consent box
    ui.IconTextComponent title = 1;
    // list of consents in text format
    repeated ui.IconTextComponent consent_texts = 2;
    // bg color for the consent box
    typesv2.common.ui.widget.BackgroundColour bg_color = 3;
    // list of consent ids used for consents
    repeated string consent_ids = 4;
  }
}

// screen option for OptionSelectionScreen for Loans
message LoansOptionSelectionScreenOption {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string bg_color = 3;
  typesv2.common.Text title = 4;
  typesv2.ui.OptionSelectionView option_view = 5;
  frontend.deeplink.Cta primary_cta = 6;
  ui.IconTextComponent toolbar_right_cta = 7;
  typesv2.common.VisualElement partner_logo = 8;
  bool no_selection_allowed = 9;
  frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow flow = 10;
  string loan_request_id = 11;
}

// Loans offer screen - New screen for generic offer screen all types loans.
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24361&t=MeqgoxJuVnwFly4X-4
message LoansOfferScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 3;
  repeated LoansScreenUiComponents components = 4;
  // deprecated: use primary_loans_cta
  frontend.deeplink.Cta primary_cta = 5 [deprecated = true];
  // error message to be shown in case user can't proceed further with the offer
  // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=2241-24703&mode=dev
  typesv2.common.ui.widget.ImageTitleSubtitleElement error_message = 6;
  // component to be shown on the toolbar right corner
  typesv2.ui.IconTextComponent toolbar_right_cta = 7;
  // banner to be shown above the primary button "Trusted by 1,23,456 Users"
  typesv2.ui.IconTextComponent cta_banner = 8;
  // label shown below the primary button "Offer may get updated based on your details"
  typesv2.common.Text footer_label = 9;
  // this screen can be used in eligibility flow also
  string loan_request_id = 10;
  // flow like eligibility,loan_application etc.
  frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow flow = 11;
  // Used to show the lottie or the background image
  typesv2.common.VisualElement background_visual_element = 12;
  // Secondary cta to be shown below the primary cta e.g Explore all Fi products
  typesv2.ui.IconTextComponent footer_cta_label = 13;
  LoansCta primary_loans_cta = 14;
}

message LoansSingleVendorMultiOfferScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // Right cta to show at the header
  ui.IconTextComponent toolbar_right_cta = 3;
  // Title for the screen
  typesv2.common.Text title = 4;
  repeated LoanOfferDetailsCard offer_details_cards = 5;
  string selected_card_border_color = 6;
  // if non is selected, can send -1
  int32 default_selected_card_index = 7;
  // call getOfferDetails RPC using the loan_offer_id and loan_header for the selected offer
  frontend.deeplink.Cta cta = 8;
  // Background color for the screen
  string bg_color = 9;
  // Partner Logo to show at the Bottom
  ui.IconTextComponent footer_label = 10;
}

message LamfRepaymentMethodsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.ui.widget.BackgroundColour background_color = 3;
  typesv2.common.Text toolbar_title = 4;
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement header_component = 5;
  repeated RepaymentMethodsComponent repayment_methods = 6;
  ui.IconTextComponent additional_message = 7;
  ui.IconTextComponent footer_label = 8;
  frontend.deeplink.Cta cta = 9;
  string loan_id = 10;

  message RepaymentMethodsComponent {
    string id = 1;
    ui.IconTextComponent repayment_title = 2;
    ui.IconTextComponent amount_info = 3;
    ui.IconTextComponent repayment_desc = 4;
    // Use this to show the icon only if the component is not a radio button
    typesv2.common.VisualElement right_icon = 5;
    // Use this to mark the default selected radio button
    bool is_selected = 6;
    // Show radio button if this is true otherwise show icon
    bool is_radio_button = 7;
    typesv2.common.ui.widget.BackgroundColour background_color = 8;
  }
}

message LoansKnowMoreBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  repeated InfoBlock method_blocks = 6;

  message InfoBlock {
    typesv2.common.VisualElement icon = 1;
    typesv2.ui.IconTextComponent title = 2;
    typesv2.ui.IconTextComponent info_badge = 3;
    typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  }
}

/**
  * Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9255-54117&t=t2K4QJevtLdS8EZ1-4
  * Screen options to render Auto pay authorization methods in bottom sheet
  */
message LoansAutoPayAuthorizationMethodBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.Text title = 3;
  repeated typesv2.ui.OptionSelectionItem authorization_methods = 4;
  frontend.deeplink.Cta cta = 5;
  typesv2.common.ui.widget.BackgroundColour bg_color = 6;
}

message LoanApplicationErrorStatusScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  typesv2.common.VisualElement icon = 3;
  frontend.deeplink.Cta cta = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  // Instead maintaining different fields BE will send list of items with
  // top margin for each item to maintain the space between the items
  // So that BE can add more items in future without changing the proto
  repeated InfoMessageWithTopMargin info_messages = 6;
}

message InfoMessageWithTopMargin {
  api.typesv2.ui.IconTextComponent info_message = 1;
  int32 top_margin = 2;
}


// LoansFormEntryScreenOptions defines the screen options for a loan application form
// It provides structure for rendering form fields, banner, and primary CTA
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
message LoansFormEntryScreenOptions {
  // Header information for the screen
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 3;

  // Banner to display at the top of the screen
  api.typesv2.ui.TopSection top_banner = 4;
  api.typesv2.ui.USPCarouselComponent usp_carousel = 5;
  // List of form fields to be displayed on the screen
  repeated CompositeFormField form_fields = 6;
  frontend.deeplink.Cta primary_cta = 7;
  string loan_request_id = 8;
  bool is_location_required = 9;
  ToolbarRightCta top_right_cta = 10;
  // https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=13722-12043&t=JRRtoxAlmiE5ojXh-0
  repeated typesv2.common.ui.widget.CheckboxItem consents = 11;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77939-331666&t=yvX0qj4seLBrKSrd-4
message ToolbarRightCta {
  typesv2.common.VisualElement icon = 1;
  typesv2.common.Text title = 2;
  frontend.deeplink.Deeplink deeplink = 3;
  string border_color = 4;
  string bg_color = 5;
  // client will show the title after this delay .
  int32 title_delay_seconds = 6;
}

// CompositeFormField represents a form field that can be of different types
// (input field, selectable field, or amount selection field)
message CompositeFormField {
  oneof field {
    IntegerInputFormField integer_input_form_field = 1;
    // Text input field
    StringInputFormField string_input_form_field = 2;
    // Selection field (dropdown/picker)
    SelectableFormField selectable_form_field = 3;
    // Amount selection field with slider
    AmountSelectionFormField amount_selection_form_field = 4;
    // Date input field
    DateInputFormField date_input_form_field = 5;
  }
  // Type of the form field for domain-specific handling
  string field_type = 6;
  // Border color for the field container
  string border_color = 7;
  // Indicates if this field is optional
  bool is_optional = 8;
  // Hint text to be displayed in the field
  api.typesv2.common.Text hint = 9;
  // Label for the field
  api.typesv2.common.Text label = 10;
  // Indicates if additional fetch is required for the field
  // based on the field will decide to make RPC call.
  bool additional_fetch_required = 11;
  // Style for the entered text
  api.typesv2.common.Text entered_text_style = 12;
  // Interactive help element that provides additional information or guidance
  // figma- https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-FFF?node-id=74264-78481&t=5ygLUlOICYbtAZoL-4
  // e.g - Where to find my PAN number?
  api.typesv2.ui.IconTextComponent bottom_helper_cta = 13;

  // InputFormField represents an integer input field in the form
  message IntegerInputFormField {
    int64 default_value = 1;
  }

  // InputFormField represents a string input field in the form
  message StringInputFormField {
    string default_value = 3;
  }

  // SelectableFormField represents a field where the user selects from options
  message SelectableFormField {
    // Icon shown for the action (e.g., dropdown icon)
    api.typesv2.common.VisualElement action_icon = 2;
    // Deeplink to navigate to when the field is tapped
    // For Screen name - LOANS_OPTION_SELECTION_BOTTOM_SHEET
    frontend.deeplink.Deeplink deeplink = 4;
    // default selected option
    api.typesv2.ui.OptionSelectionItem default_selected_option = 5;
  }

  // AmountSelectionFormField represents a field for selecting monetary amounts
  message AmountSelectionFormField {
    // Default selected amount
    api.typesv2.Money default_amount = 2;
    // Minimum selectable amount
    api.typesv2.Money min_amount = 3;
    // Maximum selectable amount
    api.typesv2.Money max_amount = 4;
    api.typesv2.common.VisualElement edit_icon = 5;
    // Whether the amount can be directly edited or only selected via slider
    bool is_amount_editable = 6;
    // Deeplink to navigate to when the field is tapped
    // To goto - PRE_APPROVED_INCOME_SELECTION_SCREEN
    frontend.deeplink.Deeplink deeplink = 7;
  }
  // Represents a form field for date input
  message DateInputFormField {
    api.typesv2.Date default_date = 1;
  }
}

// LoansFormEntryScreenOptions defines the screen options for LOANS_CONFIRMATION_BOTTOM_SHEET screen
message LoansConfirmationBottomSheetScreenOption {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  string bg_color = 3;
  typesv2.common.Text title = 4;
  typesv2.common.Text subtitle = 5;
  frontend.deeplink.Cta confirm_cta = 6;
  frontend.deeplink.Cta cancel_cta = 7;
  typesv2.common.VisualElement confirmation_logo = 8;
  frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow flow = 9;
  string loan_request_id = 10;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=69553-38807&t=xKsnIo792toAzLeX-4
message LoansConsentV2ScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // use LoansCta -> LoansCtaAction -> CallRpc -> Common Meta Data -> lse_flow
  frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow flow = 3 [deprecated = true];
  // use LoansCta -> LoansCtaAction -> CallRpc -> Common Meta Data -> lr_id
  string loan_request_id = 4 [deprecated = true];
  // Icon to be displayed at the top of the screen
  typesv2.common.VisualElement top_icon = 5;
  // Title for the screen
  typesv2.common.Text title = 6;
  // Subtitle for the screen
  typesv2.common.Text subtitle = 7;
  // Checkbox items list for the screen
  repeated common.ui.widget.CheckboxItem consent_items = 8;
  // CTA for the screen
  // deprecated: use loans_cta instead
  frontend.deeplink.Cta cta = 9 [deprecated = true];
  // Background color for the screen
  string bg_color = 10;
  // Background color for the consent container
  string consent_bg_color = 11;
  LoansCta loans_cta = 12;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37832&t=JHfSdzSbmlwPJwbn-0
message LoansPermissionBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;

  // Main title text displayed on the bottom sheet.
  typesv2.common.Text title = 3;

  // Descriptive text providing additional information under the title.
  typesv2.common.Text description = 4;

  // Icon or visual element displayed at the top of the bottom sheet.
  typesv2.common.VisualElement top_icon = 5;

  // List of permission-related dialogues, each showing a permission with icon and text.
  repeated PermissionDialogueList permission_dialogue_lists = 6;

  // Background color for the bottom sheet, typically in hex format (e.g., "#FFFFFF").
  string bg_color = 7;

  // Primary call-to-action button shown at the bottom (e.g., "Continue" or "Allow" or "Tap to Copy").
  frontend.deeplink.Cta cta = 8;

  // Flag indicating whether a secondary "Tap to Copy" CTA is enabled.
  bool is_copy_cta_enabled = 9;

  // The actual text that will be copied when the "Tap to Copy" CTA is triggered.
  string text_to_be_copied = 10;

  // Represents an individual permission item with an icon and label.
  message PermissionDialogueList {
    // Title or label for the permission (e.g., "Camera Access").
    typesv2.common.Text permission_title = 1;

    // Icon representing the permission.
    typesv2.common.VisualElement permission_icon = 2;
  }
}

// Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=75523-46825&t=nxmxSGLBM1kmeBal-4
message LoansKeyValueRowsBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 2;
  // used to set centre icon, title and subtitle vertically aligned
  common.ui.widget.VisualElementTitleSubtitleElement visual_element_title_subtitle_element = 3;
  string bg_color = 4;
  frontend.deeplink.Cta cta = 5;
  repeated KeyValueRow key_value_info = 6;
  // if this is false cta would take the whole width otherwise it should wrap around the content
  bool should_wrap_cta = 7;
}
