syntax = "proto3";

package wealthonboarding;

import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/image_type.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/document_proof.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/income_slab.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/politically_exposed_status.proto";
import "api/typesv2/residential_status.proto";
import "api/wealthonboarding/data.proto";
import "api/wealthonboarding/enums.proto";
import "api/wealthonboarding/preinvestment.proto";
import "api/wealthonboarding/troubleshoot.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding";
option java_package = "com.github.epifi.gamma.api.wealthonboarding";

message GetOnboardingStatusRequest {
  // actor id of the customer for which onboarding status is being fetched
  string actor_id = 1;
  OnboardingType onboarding_type = 2;
}

message GetOnboardingStatusResponse {
  // rpc response status
  rpc.Status status = 1;
  string onboarding_id = 2;
  OnboardingStatus onboarding_status = 3;
  OnboardingStep current_step = 4;
  // OnboardingEligibility can be used to identify whether a user is able to wealth onboard or not
  OnboardingEligibility eligibility = 5;
}

message GetNextOnboardingStatusRequest {
  // actor id of the customer for which onboarding status is being fetched
  string actor_id = 1;
  // depending on this returned deep links will change
  WealthFlow wealth_flow = 2;
  // onboarding type
  wealthonboarding.OnboardingType onboarding_type = 3;
  // entrypoint of flow
  OnboardingStepEntrypoint entry_point = 4;
}

message GetNextOnboardingStatusResponse {
  // rpc response status
  rpc.Status status = 1;
  // deeplink for the for the user to perform the step if user intervention is needed in the step
  frontend.deeplink.Deeplink next_step = 2;
  // current onboarding status
  OnboardingStatus onboarding_status = 3;
}

message CollectDataFromCustomerRequest {
  string actor_id = 1;
  api.typesv2.Gender gender = 2;
  api.typesv2.MaritalStatus marital_status = 3;
  api.typesv2.DocumentProof pan = 4;
  // pan will be used as POI always
  api.typesv2.DocumentProof poi = 5 [deprecated = true];
  api.typesv2.DocumentProof poa = 6;
  api.typesv2.common.Image signature = 7;
  // directly collecting as confirmation from customer as it is part of agreement
  api.typesv2.ResidentialStatus residential_status = 8 [deprecated = true];
  // directly collecting as confirmation from customer as it is part of agreement
  api.typesv2.PoliticallyExposedStatus politically_exposed_status = 9 [deprecated = true];
  // directly collecting as confirmation from customer as it is part of agreement
  api.typesv2.Nationality nationality = 10 [deprecated = true];
  api.typesv2.IncomeSlab income_slab = 11 [deprecated = true]; // use income instead
  // to be populated as false if user selects No Digilocker Account option on digilocker screen
  api.typesv2.common.BooleanEnum has_digilocker_account = 12;
  // to be used to identify the onboarding details for which the data is being collected
  wealthonboarding.OnboardingType onboarding_type = 13;
  int32 income = 14;
  NomineeDeclarationDetails nominee_declaration_details = 15;
  // should be passed as true when user click on 'skip now' button on WEALTH_ONBOARDING_IA_AGREEMENT_SCREEN
  api.typesv2.common.BooleanEnum skip_signing_advisory_agreement = 16;
  // figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?type=design&node-id=11054-3372&mode=design&t=U3KCdmlbslxDGtO8-4
  string user_submitted_pan = 17;
  google.type.Date user_submitted_dob = 18;
  // figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?type=design&node-id=11054-3372&mode=design&t=U3KCdmlbslxDGtO8-4
  string user_submitted_name = 19;
  // consent_id in consent table in case the EPIFI_WEALTH_INVESTMENT_RISK_PROFILE consent is saved for user
  string risk_profiling_consent_id = 20;
  // reference of entry in table investment_risk_profile
  string investment_risk_profile_id = 21;
}

message CollectDataFromCustomerResponse {
  // rpc response status
  rpc.Status status = 1;
}

message CollectDataFromCustomerWithStreamRequest {
  oneof CustomerData {
    // first chunk of the stream must be this
    // later chunks can contain chunks of streaming data (image, video etc.)
    BasicCustomerInfo basic_info = 1;

    // base 64 encoded PAN image data as bytes
    bytes pan_img_chunk = 2;
  }
}

message BasicCustomerInfo {
  string actor_id = 1;

  // to be used to identify the onboarding details for which the data is being collected
  wealthonboarding.OnboardingType onboarding_type = 2;

  api.typesv2.DocumentProofType proof_type = 3;

  api.typesv2.common.ImageType img_type = 4;
}

message CollectDataFromCustomerWithStreamResponse {
  rpc.Status status = 1;
}

message GetInvestmentOnbStatusRequest {
  // list of actor ids for which investment status is being fetched
  repeated string actor_ids = 1;
}

message UpdateOnbStatusRequest {
  // actor id of the user for which onboarding status is being updated
  string actor_id = 1;
  // onboarding status to update
  wealthonboarding.OnboardingStatus onboarding_status = 2;
  // onboarding type wealth/pre-investment
  wealthonboarding.OnboardingType onboarding_type = 3;
}

message GetInvestmentOnbStatusResponse {
  rpc.Status status = 1;
  // investment onboarding data is mapped to an actor, key is the actor id
  map<string, InvestmentOnbData> investment_onb_info = 2;
}

message UpdateOnbStatusResponse {
  rpc.Status status = 1;
}

message InvestmentOnbData {
  // current investment status of the user
  wealthonboarding.OnboardingStatus onboarding_status = 1;
  // current investment step of the user
  wealthonboarding.OnboardingStep onboarding_step = 2;
}

message GetInvestmentDataRequest {
  // list of actor ids for which investment details are to be fetched
  repeated string actor_ids = 1;
}


message GetInvestmentDataResponse {
  rpc.Status status = 1;
  // InvestmentDetailData would be null if an error occurs while fetching the specific detail for an actor
  map<string, wealthonboarding.PreInvestmentDetail> investment_detail_info = 2;
}

message GetOnboardingTroubleshootDetailsRequest {
  string actor_id = 1;
  // onboarding type
  wealthonboarding.OnboardingType onboarding_type = 2;
}

message GetOnboardingTroubleshootDetailsResponse {
  rpc.Status status = 1;
  // Overall onboarding troubleshoot details
  wealthonboarding.OnboardingTroubleshootDetails onboarding_troubleshoot_details = 2;
}

message DownloadDigilockerDocsRequest {
  string actor_id = 1;

  // authorization code from when user logs into DigiLocker and grants us permission
  string auth_code = 2;
}

message DownloadDigilockerDocsResponse {
  rpc.Status status = 1;
}

message GetInvestmentDataV2Request {
  // list of actor ids for which investment details and status are to be fetched
  repeated string actor_ids = 1;
}

message GetInvestmentDataV2Response {
  rpc.Status status = 1;
  map<string, wealthonboarding.PreInvestmentDetailsV2> investment_detail_info = 2;
}

message InitiateDocumentExtractionRequest {
  string actor_id = 1;
  // currently only pan card is supported
  api.typesv2.DocumentProofType document_type = 2;
}

message InitiateDocumentExtractionResponse {
  enum Status {
    // initiation successful
    OK = 0;
    // docket is not downloaded for the user
    DOCKET_NOT_DOWNLOADED = 101;
  }
  rpc.Status status = 1;
  // signed s3 url to download requested document,
  // if document already exists document url will be returned in the response
  string document_url = 2;
}

message GetDocumentRequest {
  string actor_id = 1;
  // currently only pan card is supported
  api.typesv2.DocumentProofType document_type = 2;
}

message GetDocumentResponse {
  enum Status {
    // initiation successful
    OK = 0;

    // Getting the document is either impossible or not supported
    NOT_FOUND = 5;

    // document fetch is in progress
    OPERATION_IN_PROGRESS = 101;

    // Deprecated: Returning NOT_FOUND in case actor ID is not present in wealth onboarding details
    // invalid actor id in request
    // actor is not present in WONB system
    INVALID_ARGUMENT_ACTOR_NOT_PRESENT = 102 [deprecated = true];

    // Deprecated: Returning NOT_STARTED in case workflow is not started
    // document extraction workflow for the requested document type is not found
    DOCUMENT_EXTRACTION_WORKFLOW_NOT_FOUND = 103 [deprecated = true];

    // Document fetch is possible/supported but has not been started
    NOT_STARTED = 104;
  }
  rpc.Status status = 1;
  // signed s3 url to download requested document
  string document_url = 2;
}

message GetOrCreateUserRequest {
  string actor_id = 1;
}

// GetOrCreateUserResponse returns details required by consumers of the RPC. Details should be added to the response on
// a need basis
message GetOrCreateUserResponse {
  rpc.Status status = 1;
  message UserDetails {
    string id = 1;
    api.typesv2.common.Name name = 2;
    api.typesv2.Gender gender = 3;
    api.typesv2.common.PhoneNumber phone_number = 4;
    string email = 5;
    google.type.Date dob = 6;
    api.typesv2.DocumentProof pan_details = 7;
  }
  UserDetails user_details = 2;
}

service WealthOnboarding {
  // will be used to get the wealth onboarding status of the customer
  rpc GetOnboardingStatus (GetOnboardingStatusRequest) returns (GetOnboardingStatusResponse) {}
  // will be used to get the next step for onboarding
  rpc GetNextOnboardingStep (GetNextOnboardingStatusRequest) returns (GetNextOnboardingStatusResponse) {}
  // primary RPC to  collect data from customer
  rpc CollectDataFromCustomer (CollectDataFromCustomerRequest) returns (CollectDataFromCustomerResponse) {}
  // fallback RPC for collecting large data from customer that don't fit into standard GRPC msg limit
  rpc CollectDataFromCustomerWithStream (stream CollectDataFromCustomerWithStreamRequest) returns (CollectDataFromCustomerWithStreamResponse) {}
  // GetInvestmentOnbStatus is to be used by the core investment service for fetching the current investment status of the user
  // It starts the investment onboarding of a user, if not already done. It supports bulk mode as well
  rpc GetInvestmentOnbStatus (GetInvestmentOnbStatusRequest) returns (GetInvestmentOnbStatusResponse);
  // To update the onboarding status of a user
  rpc UpdateOnbStatus (UpdateOnbStatusRequest) returns (UpdateOnbStatusResponse);
  // GetInvestmentData returns the pre investment details required for an actor
  // currently only details for fatca, order feed, elog and credit mis file is supported
  rpc GetInvestmentData (GetInvestmentDataRequest) returns (GetInvestmentDataResponse);
  // RPC to get onboarding details and oboarding step details
  rpc GetOnboardingTroubleshootDetails (GetOnboardingTroubleshootDetailsRequest) returns (GetOnboardingTroubleshootDetailsResponse);
  // RPC to get access tokens from DigiLocker using the authorization code returned by DigiLocker when
  // a user logs into DigiLocker and grants us permission.
  // These tokens can then be later used in other DigiLocker API calls to get user's documents, etc.
  // NOTE: In an earlier implementation of this RPC the DigiLocker documents were actually downloaded here itself, but
  // since downloading of documents can throw transient errors, this implementation has been moved to inside the wealth onboarding orchestrator for automatic retries.
  // Ideally this RPC should now be perhaps named as GetDigilockerAccess, but that would also mean a similar naming for the FE RPC and a client change.
  rpc DownloadDigilockerDocs (DownloadDigilockerDocsRequest) returns (DownloadDigilockerDocsResponse);
  // GetInvestmentDataV2 returns the pre investment details required for an actor
  // currently only details for fatca, order feed, elog and credit mis file is supported
  // It also provides the current status of the data present on the wealth onboarding end
  rpc GetInvestmentDataV2 (GetInvestmentDataV2Request) returns (GetInvestmentDataV2Response);
  // Wealth On-boarding has user's kyc documents like pan card, Aadhar card etc, other domain services may need to access these documents
  // InitiateDocumentExtraction begins document extraction process,
  // document will be extracted from kra docket if not already present in db
  // actual document will be part of GetDocument rpc response.
  // currently only pan card extraction is supported
  rpc InitiateDocumentExtraction (InitiateDocumentExtractionRequest) returns (InitiateDocumentExtractionResponse);
  // GetDocument returns an s3 url for the requested document
  rpc GetDocument (GetDocumentRequest) returns (GetDocumentResponse);
  // GetOrCreateUser rpc returns the user by actor id if found, else it creates the user.
  rpc GetOrCreateUser (GetOrCreateUserRequest) returns (GetOrCreateUserResponse);
}

