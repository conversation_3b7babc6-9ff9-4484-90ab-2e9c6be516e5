// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package wealthonboarding;

option go_package = "github.com/epifi/gamma/api/wealthonboarding";
option java_package = "com.github.epifi.gamma.api.wealthonboarding";

enum OnboardingStatus {
  ONBOARDING_STATUS_UNSPECIFIED = 0;
  ONBOARDING_STATUS_PENDING = 1;
  ONBOARDING_STATUS_IN_PROGRESS = 2;
  ONBOARDING_STATUS_COMPLETED = 3;
  ONBOARDING_STATUS_FAILED = 4;
  ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED = 5;
  ONBOARDING_STATUS_FUTURE_SCOPE = 6;
}

// OnboardingType determines the phase of onboarding in wealth
enum OnboardingType {
  ONBOARDING_TYPE_UNSPECIFIED = 0;
  ONBOARDING_TYPE_WEALTH = 1;
  ONBOARDING_TYPE_PRE_INVESTMENT = 2;
}

enum WealthFlow {
  WEALTH_FLOW_UNSPECIFIED = 0;
  WEALTH_FLOW_CONNECTED_ACCOUNT = 1;
  WEALTH_FLOW_INVESTMENT = 2;
}

// OnboardingStepEntrypoint is used for getting next action in onboarding based of different entry point
enum OnboardingStepEntrypoint {
  ONBOARDING_STEP_ENTRYPOINT_UNSPECIFIED = 0;
  ONBOARDING_STEP_ENTRYPOINT_INVEST_SUMMARY = 1;
}

enum OnboardingStep {
  ONBOARDING_STEP_UNSPECIFIED = 0;
  // this step will be used for user data collection from fi bank
  ONBOARDING_STEP_DATA_COLLECTION = 5;
  // for NSDL pan verification
  ONBOARDING_STEP_PAN_VERIFICATION = 10;
  // download data from KRA
  ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA = 20;
  // step downloads data from digilocker by interacting with customer via deeplink
  ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER = 23;
  // perform face match
  ONBOARDING_STEP_FACE_MATCH = 25;
  // perform liveness
  ONBOARDING_STEP_LIVENESS = 30;
  // collect missing data if any from user
  ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO = 40;
  // create and sign KRA docket
  ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET = 45;
  // consolidate manual review data from all the steps and handle changes in review status
  ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW = 47;
  // user input
  ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP = 50;
  // sign pdf agreement with wealth
  ONBOARDING_STEP_SIGN_AGREEMENT = 60;
  // data collection during pre investment step
  ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT = 65;
  // upload docket for fresh kra user during pre investment phase
  ONBOARDING_STEP_UPLOAD_DOCKET = 70;
  // download kra docs for existing kra user during pre investment phase
  ONBOARDING_STEP_DOWNLOAD_KRA_DOC = 90;
  // aadhaar based e-sign for investment advisory agreement
  ONBOARDING_STEP_ADVISORY_AGREEMENT = 91;
  // user consent step for wealth onboarding
  ONBOARDING_STEP_USER_CONSENT = 92;
  // investment risk profiling flow
  // figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fwealthonboarding?type=design&node-id=11560-18291&mode=design&t=2gLvPJ6KH85969X2-4
  ONBOARDING_STEP_INVESTMENT_RISK_PROFILING = 93;
  // user has to modify nominee with some additional details (pan, phone, email)
  ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS = 94;
  // user consent for new mitc guideline as per SEBI
  // figma: https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=O4Q25jFCOhOqUxLT-4
  ONBOARDING_STEP_USER_MITC_CONSENT = 95;
}

enum OnboardingStepStatus {
  STEP_STATUS_UNSPECIFIED = 0;
  STEP_STATUS_PENDING = 1;
  STEP_STATUS_IN_PROGRESS = 2;
  STEP_STATUS_COMPLETED = 3;
  STEP_STATUS_FAILED = 4;
  STEP_STATUS_MANUAL_INTERVENTION_NEEDED = 5;
  STEP_STATUS_FUTURE_SCOPE = 6;
  STEP_STATUS_TRANSIENT_FAILURE = 7;
  // this is intended for optional steps where user has an option to skip.
  // we have to move to next step for skipped status as well in addition to completed status
  STEP_STATUS_SKIPPED = 8;
}

enum OnboardingStepSubStatus {
  STEP_SUB_STATUS_UNSPECIFIED = 0;
  // ONBOARDING_STEP_DATA_COLLECTION
  STEP_SUB_STATUS_MANDATORY_DETAILS_MISSING_IN_FI = 1;
  // for ONBOARDING_STEP_PAN_VERIFICATION
  // intentional gap
  STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED = 11;
  STEP_SUB_STATUS_NAME_MATCH_FAILED = 12;
  STEP_SUB_STATUS_NSDL_PAN_AADHAAR_SEEDING_PENDING = 13;
  // for ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA
  // intentional gap
  STEP_SUB_STATUS_UNHANDLED_KRA_STATUS = 21;
  STEP_SUB_STATUS_FRESH_KRA_NEEDED = 22;
  STEP_SUB_STATUS_KRA_STATUS_NOT_PERMITTED = 23 [deprecated = true];
  STEP_SUB_STATUS_KRA_PARTIAL_DATA_UPLOAD_NEEDED = 24;
  STEP_SUB_STATUS_KRA_STATUS_HOLD = 25;
  STEP_SUB_STATUS_INVALID_PAN_NO_FORMAT = 26;
  // for ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER
  // intentional gap
  STEP_SUB_STATUS_DIGILOCKER_ACCOUNT_AVAILABILITY_UNKNOWN = 27;
  STEP_SUB_STATUS_NO_DIGILOCKER_ACCOUNT = 28;
  // for ONBOARDING_STEP_LIVENESS
  // intentional gap
  STEP_SUB_STATUS_LIVENESS_FAILED_WITH_RETRY = 31;
  STEP_SUB_STATUS_LIVENESS_FAILED = 32;
  // for ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO
  // intentional gap
  STEP_SUB_STATUS_UNHANDLED_KRA_APP_STATUS_DELTA = 41;
  STEP_SUB_STATUS_CKYC_RECORD_NOT_FOUND = 42;
  STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_L = 43;
  STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_S = 44;
  STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_O = 45;
  STEP_SUB_STATUS_UNSUPPORTED_AADHAAR_TYPE = 46;
  STEP_SUB_STATUS_PAN_IMAGE_MISSING_IN_CKYC_DATA = 47;
  STEP_SUB_STATUS_DATA_COLLECTION_EVALUATOR_FAILED = 48;
  STEP_SUB_STATUS_USER_INPUT_NEEDED = 49;
  STEP_SUB_STATUS_DOB_MISMATCH_ERROR_IN_CKYC_DOWNLOAD_RESPONSE = 50;
  STEP_SUB_STATUS_PAN_UPLOAD_ATTEMPTS_EXHAUSTED = 121;
  // used only when user's PAN image verification failed (e.g. image not clear) and user has to retry uploading
  STEP_SUB_STATUS_USER_INPUT_INCL_PAN_REUPLOAD_NEEDED = 122;
  // for non individual response from ckyc
  STEP_SUB_STATUS_CKYC_ENTITY_TYPE_NON_INDIVIDUAL = 123;
  // for ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET
  // intentional gap
  STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING = 51;
  STEP_SUB_STATUS_CUSTOMER_SIGNED = 52;
  STEP_SUB_STATUS_IMAGE_CONVERSION_FAILED = 53;
  STEP_SUB_STATUS_POA_ID_MISSING = 54;
  STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING_HOLD_CASES = 128;
  STEP_SUB_STATUS_CUSTOMER_SIGNED_HOLD_CASES = 129;
  STEP_SUB_STATUS_COMPLETED_HOLD_CASES = 130;
  STEP_SUB_STATUS_KRA_FORM_DETAILS_MISSING = 131;
  STEP_SUB_STATUS_PDF_MERGING_FAILED = 132;
  // for ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW
  // intentional gap
  STEP_SUB_STATUS_PERMANENTLY_REJECTED = 55;
  STEP_SUB_STATUS_REVIEW_ITEM_NOT_FOUND = 56;
  STEP_SUB_STATUS_INVALID_REVIEW_STATUS = 57;
  // for ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP
  // intentional gap
  STEP_SUB_STATUS_CONSENT_FETCH_FAILED = 61;
  // for ONBOARDING_STEP_SIGN_AGREEMENT
  // intentional gap
  STEP_SUB_STATUS_AGREEMENT_PDF_GENERATION_FAILED = 71;
  // intentional gap
  STEP_SUB_STATUS_RETRY_EXHAUSTED = 81;
  // intentional gap
  STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET = 91;
  STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE = 92;
  STEP_SUB_STATUS_UNSUPPORTED_NATIONALITY = 93;
  // for generic sub statuses
  STEP_SUB_STATUS_FAILED_DUE_TO_ORCHESTRATION_FAILURE = 101;
  // for ONBOARDING_STEP_UPLOAD_DOCKET
  // intentional gap
  STEP_SUB_STATUS_UPDATED_KRA_STATUS_NOT_SUPPORTED = 111;
  STEP_SUB_STATUS_UPLOAD_ATTEMPT_FAILED = 112;
  // for ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT
  STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET = 141;
  // this substatus can be used across steps wherever suitable
  STEP_SUB_STATUS_UNSUPPORTED_KYC_STATUS_FROM_KRA = 142;
  // when we are waiting on user to give digilocker authentication
  STEP_SUB_STATUS_DIGILOCKER_AUTH_NEEDED = 143;
  // sub status to identify if the user's onboarding is paused due to a vendor downtime
  STEP_SUB_STATUS_VENDOR_DOWNTIME = 144;
  // this sub status means that there is a true AML match for the user and has been confirmed by the ops agent
  STEP_SUB_STATUS_AML_MATCH_FOUND = 145;
  // this sub status is used when some feature release evaluation fails in the step
  STEP_SUB_STATUS_FEATURE_EVALUATION_FAILED = 146;
}

// livenessRejectReason stores the reason if the liveness video is rejected
// this is generic enum which can be used to map status from liveness service
// as well as from cx agent review
enum LivenessRejectReason {
  LIVENESS_REJECT_REASON_UNSPECIFIED = 0;
  LIVENESS_REJECT_REASON_VIDEO_BLURRY = 1;
  LIVENESS_REJECT_REASON_OTP_NOT_CLEAR = 2;
  LIVENESS_REJECT_REASON_INCOMPLETE_OTP = 3;
  LIVENESS_REJECT_REASON_NO_AUDIO = 4;
  LIVENESS_REJECT_REASON_LOW_LIGHT = 5;
  LIVENESS_REJECT_REASON_OVEREXPOSURE = 6;
  LIVENESS_REJECT_REASON_MULTIPLE_FACES = 7;
  LIVENESS_REJECT_REASON_SPOOFING_ATTEMPT = 8;
  LIVENESS_REJECT_REASON_MISLEADING_USER = 9;
  LIVENESS_REJECT_REASON_UNDER_AGE_CUSTOMER = 10;
  LIVENESS_REJECT_REASON_GENDER_MISMATCH_AUDIO = 11;
  LIVENESS_REJECT_REASON_GENDER_MISMATCH_VIDEO = 12;
  LIVENESS_REJECT_REASON_TECHNICAL_ISSUE = 13;
  LIVENESS_REJECT_REASON_WEARING_FACE_MASK = 14;
  LIVENESS_REJECT_REASON_FACE_TOO_CLOSE = 15;
  LIVENESS_REJECT_REASON_FACE_TOO_FAR = 16;
  LIVENESS_REJECT_REASON_CAMERA_PLACEMENT_ISSUE = 17;
  LIVENESS_REJECT_REASON_FACE_NOT_DETECTED = 18;
  LIVENESS_REJECT_REASON_FACE_POORLY_DETECTED = 19;
  LIVENESS_REJECT_REASON_FACE_TOO_DARK = 20;
  LIVENESS_REJECT_REASON_FACE_TOO_BRIGHT = 21;
  LIVENESS_REJECT_REASON_NO_FACE_DETECTED = 22;
  LIVENESS_REJECT_REASON_OTHER = 23;
}

// used to enable wealth onboarding features
enum WealthOnboardingFeature {
  WEALTH_ONBOARDING_FEATURE_UNSPECIFIED = 0;
  reserved 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11;
  // tnc page moves to first step in wealth onboarding (with consent text changes)
  WEALTH_ONBOARDING_FEATURE_TNC_V2 = 12;
  WEALTH_ONBOARDING_FEATURE_PAN_DOB_SUBMIT_SCREEN = 13;
  WEALTH_ONBOARDING_FEATURE_NSDL_PAN_INQUIRY_WITH_NAME = 14;
  // feature if enabled the users will go through the risk profiling survey
  // figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fwealthonboarding?type=design&node-id=11560-18290&mode=design&t=Lwzph6DiGFbpNL6Q-4
  WEALTH_ONBOARDING_FEATURE_RISK_PROFILING = 15;
  // feature to perform liveness check mandatorily for all users whose KYC
  // has to be registered with KRAs for the first time, i.e. they are not registered
  // with any of the KRAs till date.
  // liveness check is carried out to guard against spoofing and other fraudulent manipulations
  // For more details on requirements, refer point 52 on page 17 of SEBI's master circular on KYC norms
  // https://www.sebi.gov.in/legal/master-circulars/oct-2023/master-circular-on-know-your-client-kyc-norms-for-the-securities-market_77945.html
  WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS = 16;
  // feature used to check if nominee modification is enabled or not
  // now its mandatory to have nominee identity details, email and phone number
  WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS = 17;
  // feature used to check if wealth mitc content screen is enabled or not
  // figma: https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=O4Q25jFCOhOqUxLT-4
  WEALTH_ONBOARDING_FEATURE_MITC_CONTENT = 18;
}

enum LivenessCreationReason {
  LIVENESS_CREATION_REASON_UNSPECIFIED = 0;
  // first attempt for liveness in wealth onboarding
  LIVENESS_CREATION_REASON_FIRST_ATTEMPT = 1;
  // creating liveness due to failed retry (mostly rejected in manual review)
  LIVENESS_CREATION_REASON_PREV_ATTEMPT_REJECTED = 2;
  // last liveness attempt was stale (more than 3 days old)
  LIVENESS_CREATION_REASON_PREV_ATTEMPT_STALE = 3;
  // wealth liveness attempt not found in auth data
  // possible reasons: old user started liveness flow, and we created liveness attempt in wealth with onboarding/auth liveness
  // and now as its mandatory to have wealth liveness, we are creating it again
  LIVENESS_CREATION_REASON_PREV_ATTEMPT_NOT_WEALTH_INITIATED = 4;
}
