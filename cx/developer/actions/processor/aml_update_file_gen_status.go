// nolint:dupl
//
//nolint:all
package processor

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	amlPb "github.com/epifi/gamma/api/aml"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type AmlUpdateFileGenStatus struct {
	amlClient amlPb.AmlClient
}

func NewAmlUpdateFileGenStatus(amlClient amlPb.AmlClient) *AmlUpdateFileGenStatus {
	return &AmlUpdateFileGenStatus{amlClient: amlClient}
}

// understand this
func (c *AmlUpdateFileGenStatus) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	var options []string
	for k, v := range amlPb.FileGenerationStatus_name {
		if k != 0 {
			options = append(options, v)
		}
	}
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            "id",
			Label:           "ID of client-req-id to actor-id mapping record",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            "status",
			Label:           "New File Generation Status",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         options,
		},
	}
	return paramList, nil
}

func (c *AmlUpdateFileGenStatus) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var id string
	var status amlPb.FileGenerationStatus
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case "id":
			id = filter.GetStringValue()
		case "status":
			statusInt, ok := amlPb.FileGenerationStatus_value[filter.GetDropdownValue()]
			if !ok {
				return "", fmt.Errorf("invalid report type: %v", filter.GetDropdownValue())
			}
			status = amlPb.FileGenerationStatus(statusInt)
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.GetParameterName()))
			return "", errors.New("invalid filter")
		}
	}
	var marshalledRes []byte
	resp, err := c.amlClient.UpdateFileGenStatus(ctx, &amlPb.UpdateFileGenStatusRequest{
		Id:     id,
		Status: status,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error in calling UpdateFileGenStatus", zap.Error(te))
		marshalledRes = []byte(te.Error())
	} else {
		marshalledRes, err = protojson.Marshal(resp)
		if err != nil {
			logger.Error(ctx, "error marshalling UpdateFileGenStatus response", zap.Error(err))
			marshalledRes = []byte(err.Error())
		}
	}
	return string(marshalledRes), nil
}
