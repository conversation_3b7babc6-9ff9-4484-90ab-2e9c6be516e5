// nolint:dupl
//
//nolint:all
package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	amlPb "github.com/epifi/gamma/api/aml"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type AmlReportUpload struct {
	amlClient amlPb.AmlClient
}

func NewAmlReportUpload(amlClient amlPb.AmlClient) *AmlReportUpload {
	return &AmlReportUpload{amlClient: amlClient}
}

// understand this
func (c *AmlReportUpload) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	var reportOptions []string
	for k, v := range amlPb.AmlReportType_name {
		if k != 0 {
			reportOptions = append(reportOptions, v)
		}
	}
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            "report-type",
			Label:           "Type of aml report",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         reportOptions,
		},
		{
			Name:            "file",
			Label:           "Upload the report",
			Type:            dsPb.ParameterDataType_FILE,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (c *AmlReportUpload) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var reportType amlPb.AmlReportType
	var fileData []byte
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case "report-type":
			reportTypeInt, ok := amlPb.AmlReportType_value[filter.GetDropdownValue()]
			if !ok {
				return "", fmt.Errorf("invalid report type: %v", filter.GetDropdownValue())
			}
			reportType = amlPb.AmlReportType(reportTypeInt)
		case "file":
			fileData = filter.GetFile().GetContent()
			if len(fileData) == 0 {
				return "", errors.New("empty file")
			}
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}
	var marshalledRes []byte
	resp, err := c.amlClient.ExtractReportData(ctx, &amlPb.ExtractReportDataRequest{
		FileData:   fileData,
		ReportType: reportType,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error in calling ExtractReportData", zap.Error(te))
		marshalledRes = []byte(te.Error())
	} else {
		marshalledRes, err = json.Marshal(resp)
		if err != nil {
			logger.Error(ctx, "error marshalling ExtractReportData response", zap.Error(err))
			marshalledRes = []byte(err.Error())
		}
	}
	return string(marshalledRes), nil
}
