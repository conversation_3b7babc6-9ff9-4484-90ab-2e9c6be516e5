module "mf-process-reverse-feed-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "mf-process-reverse-feed-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 21
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-mutualfund", "epifi-${var.env}-mutualfund-karvy"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "simulator-complaint-status-queue" {
  service                                   = "simulator"
  bu-name                                   = "horizontal"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "simulator-complaint-status-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
}

module "mf-process-catalog-update-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "mf-process-catalog-update-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 4
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-mutualfund"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "casper-exchanger-order-orchestration-queue" {
  service                                   = "casper"
  bu-name                                   = "growth-infra"

  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "casper-exchanger-order-orchestration-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 12

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 500
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 9000

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  # todo: add alert for avg messages in delayed state of DLQ
  team                                          = "growth-experience"
}

module "casper-exchanger-order-notification-delay-queue" {
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  queue_name                                    = "casper-exchanger-order-notification-delay-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 6
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  avg_number_of_messages_delayed_dlq            = 10000
  team                                          = "growth-experience"
}

module "salaryprogram-notification-delay-queue" {
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "salaryprogram-notification-delay-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 6
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
}

module "referrals-order-update-queue" {
  encrypted                                    = true
  service                                   = "inappreferral"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "referrals-order-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Acquisitions"
  cloudwatch_alarm_priority                 = "P1"
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderTags\":[\"DEPOSIT\",\"FD\",\"SD\",\"RD\",\"MERCHANT\",\"FIT\",\"CASH\",\"WALLET\",\"MUTUAL_FUND\",\"BHARAT_QR\",\"US_STOCKS\",\"JUMP_P2P_INVESTMENT\",\"INTERNATIONAL\"]}"
}

module "referrals-savings-account-state-update-queue" {
  encrypted                                    = true
  service                                   = "inappreferral"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "referrals-savings-account-state-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Acquisitions"
  cloudwatch_alarm_priority                 = "P1"
  sns_topic_from_where_message_is_published = ["savings-account-state-update"]
}

module "referrals-eligibility-datacollector-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "referrals-eligibility-datacollector-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 8
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "Acquisitions"
  cloudwatch_alarm_priority               = "P1"
}

module "referrals-notification-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "referrals-notification-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 10
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "Acquisitions"
  cloudwatch_alarm_priority               = "P1"
}

module "rewards-kyc-event-queue" {
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-kyc-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "periodic-mail-sync-queue" {
  service                                   = "insights"
  bu-name                                   = "demystifi"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "periodic-mail-sync-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 21
  enable_queue_s3_events     = false
}

module "cx-upi-dispute-auto-update-queue" {
  source                         = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                     = "cx-upi-dispute-auto-update-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 30
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 6
  enable_cloudwatch_alarm_sqs    = true
  team                           = "growth-experience"
  avg_number_of_messages_visible = 10
  cloudwatch_alarm_priority      = "P0"
}

module "upi-req-mandate-confirmation-processing-queue" {
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "upi-req-mandate-confirmation-processing-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 20
}

module "upi-req-txn-confirmation-complaint-processing-queue" {
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "upi-req-txn-confirmation-complaint-processing-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 20
}

module "order-aa-data-purging-orchestration-queue" {
  service                                   = "order"
  bu-name                                   = "pay"
  source                         = "../../modules/sns_sqs/v2"
  queue_name                     = "order-aa-data-purging-orchestration-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 13
  enable_cloudwatch_alarm_sqs    = true
  team                           = "G3"
  avg_number_of_messages_visible = 100
}

module "event-af-purchase-queue" {
  source                         = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                     = "event-af-purchase-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 30
  enable_cloudwatch_alarm_sqs    = true
  team                           = "Onboarding"
  avg_number_of_messages_visible = 100
}

module "event-completed-tnc-queue" {
  source                      = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                  = "event-completed-tnc-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 30
  enable_cloudwatch_alarm_sqs = false
}

module "liveness-status-queue" {
  service                                   = "auth"
  bu-name                                   = "onboarding"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "liveness-status-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 16
  enable_cloudwatch_alarm_sqs = false
}

module "ca-send-notification-delay-queue" {
  service                                   = "connectedaccount"
  bu-name                                   = "wealth"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "ca-send-notification-delay-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 11
  enable_cloudwatch_alarm_sqs = false
}

module "rewards-manual-giveaway-event-queue" {
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "rewards-manual-giveaway-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 5
  cross_accounts_queue_publisher_access         = ["************"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "kyc-ekyc-success-queue" {
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "onboarding"
  queue_name                              = "kyc-ekyc-success-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "Onboarding"
}

module "wo-steps-retry-delay-queue" {
  source                      = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                  = "wo-steps-retry-delay-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 30
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 5
  enable_cloudwatch_alarm_sqs = false
}

module "upi-req-auth-val-cust-processing-queue" {
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "upi-req-auth-val-cust-processing-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 20
}

module "categorizer-update-order-queue" {
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "categorizer-update-order-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 13
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 70000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["order-update-topic"]
}

module "card-tracking-details-fetch-queue" {
  service                                   = "card"
  bu-name                                   = "cards"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "card-tracking-details-fetch-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "vn-ozonetel-call-details-queue" {
  source                         = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                     = "vn-ozonetel-call-details-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 6
  enable_cloudwatch_alarm_sqs    = true
  team                           = "growth-experience"
  avg_number_of_messages_visible = 10
  cloudwatch_alarm_priority      = "P0"
}

module "card-txns-decline-data-queue" {
  service                                   = "order"
  bu-name                                   = "pay"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "card-txns-decline-data-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
  enable_queue_s3_events     = true
  buckets                    = ["epifi-prod-federal-debit-card-data"]
}

module "cc-txn-notification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "cc-txn-notification-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_queue_s3_events     = true
}

module "mf-order-payment-update-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "mf-order-payment-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderTags\":[\"MUTUAL_FUND\"]}"
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "mf-order-settlement-order-updates-queue" {
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "mf-order-settlement-order-updates-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 30
  enable_cloudwatch_alarm_sqs = false
}

module "mf-insettlement-order-updates-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "mf-insettlement-order-updates-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 9
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}


module "vn-kaleyra-sms-callback-queue" {
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "vn-kaleyra-sms-callback-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
}

module "rms-command-processor-queue" {
  service                                   = "rms"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "rms-command-processor-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "kyc-vkyc-onboarding-completion-queue" {
  service                                   = "kyc"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "kyc-vkyc-onboarding-completion-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["onboarding-stage-update-topic"]
}

module "cx-update-ticket-queue" {
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "cx-update-ticket-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 7
  enable_cloudwatch_alarm_sqs             = true
  team                                    = "growth-experience"
  avg_number_of_messages_visible          = 100
  cloudwatch_alarm_priority               = "P0"
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
}
module "rewards-fittt-sports-event-queue" {
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "rewards-fittt-sports-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}
module "rewards-savings-account-state-update-queue" {
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-savings-account-state-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["savings-account-state-update"]
}

module "ca-data-refresh-on-auth-token-creation-queue" {
  service                                       = "connectedaccount"
  bu-name                                       = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "ca-data-refresh-on-auth-token-creation-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 50
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["auth-token-creation-topic"]
}

module "ca-consent-data-refresh-queue" {
  service                                   = "connectedaccount"
  bu-name                                   = "wealth"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "ca-consent-data-refresh-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 2
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 150
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq         = 604800
  team                                    = "G3"
  cross_accounts_queue_publisher_access   = ["************"]
}

module "analyser-order-update-queue" {
  service                                   = "analyser"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "analyser-order-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 200
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["order-update-topic"]
}

module "mf-recently-visited-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "mf-recently-visited-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 9
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "card-dispatch-request-queue" {
  service                                   = "card"
  bu-name                                   = "cards"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "card-dispatch-request-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "physical-card-dispatch-queue" {
  service                                   = "card"
  bu-name                                   = "cards"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "physical-card-dispatch-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "card-dispatch-request-callback-queue" {
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "card-dispatch-request-callback-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "simulator-federal-card-dispatch-callback-queue" {
  service                                   = "simulator"
  bu-name                                   = "horizontal"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "simulator-federal-card-dispatch-callback-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
}

module "search-category-consumer-queue" {
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-category-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 2
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["txn-category-detail-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 10000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  team                                          = "G3"
}

module "afu-manual-review-notification-queue" {
  service                                   = "auth"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "afu-manual-review-notification-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["liveness-manual-review-topic"]
}


module "categorizer-aa-txn-queue" {
  service                                   = "categorizer"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "categorizer-aa-txn-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 13
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 70000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["order-aa-txn-topic"]
}

module "ca-capture-column-update-queue" {
  service                                   = "connectedaccount"
  bu-name                                   = "wealth"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "ca-capture-column-update-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 3
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 150
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq         = 604800
  team                                    = "G3"
}

module "analyser-category-update-queue" {
  service                                   = "analyser"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "analyser-category-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 200
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["txn-category-detail-topic"]
}

module "segment-process-segment-export-part-file-queue" {
  service                                   = "segment"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "segment-process-segment-export-part-file-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 17
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["segment-export-s3-part-file-creation-event-topic"]
}

module "segment-compare-segment-instances-queue" {
  encrypted                                    = true
  service                                   = "segment"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "segment-compare-segment-instances-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 17
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "segment-process-segment-export-part-file-v2-queue" {
  encrypted                                    = true
  service                                   = "segment"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "segment-process-segment-export-part-file-v2-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 17
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["segment-export-s3-part-file-creation-event-v2-topic"]
}

module "segment-trigger-segment-export-queue" {
  service                                   = "segment"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "segment-trigger-segment-export-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "segment-poll-segment-export-queue" {
  service                                   = "segment"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "segment-poll-segment-export-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "search-batch-order-update-queue" {
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-batch-order-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["batch-order-update-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "search-batch-timeline-update-queue" {
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-batch-timeline-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["batch-timeline-update-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "search-category-batch-queue" {
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-category-batch-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  sns_topic_from_where_message_is_published     = ["batch-txn-category-topic"]
  team                                          = "G3"
}

module "analyser-aa-txn-queue" {
  service                                   = "analyser"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "analyser-aa-txn-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 200
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["order-aa-txn-topic"]
}

module "wealth-onb-auth-factor-update-event-consumer-queue" {
  service                                   = "wealthonboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "wealth-onb-auth-factor-update-event-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["auth-factor-update"]
}

module "referral-reward-generation-event-consumer-queue" {
  service                                   = "inappreferral"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "referral-reward-generation-event-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 5
  sns_topic_from_where_message_is_published = ["reward-generation-event-topic"]
}

module "mf-oms-workflow-event-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "mf-oms-workflow-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 11
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"$or\":[{\"OrderTags\":[\"MUTUAL_FUND\"],\"OrderWorkflow\":[\"P2P_FUND_TRANSFER\"]},{\"OrderWorkflow\":[\"ADD_FUNDS\",\"ADD_FUNDS_COLLECT\"]}]}"
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "salaryprogram-order-update-consumer-queue" {
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "salaryprogram-order-update-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 300
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 12
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
  sns_topic_from_where_message_is_published     = ["order-update-topic"]
  sns_filter_policy_scope                       = "MessageAttributes"
  sns_filter_policy                             = "{\"Amount\":[{\"numeric\":[\">=\",5000]}],\"PaymentProtocols\":[\"NEFT\",\"RTGS\",\"IMPS\",\"INTRA_BANK\",\"PAYMENT_PROTOCOL_UNSPECIFIED\"]}"
}

module "segment-upload-segment-export-part-file-queue" {
  service                                   = "segment"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "segment-upload-segment-export-part-file-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 17
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["segment-export-s3-part-file-creation-event-topic"]
}

module "rms-fittt-action-execution-update-queue" {
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "wealth"
  queue_name                 = "rms-fittt-action-execution-update-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "celestial-signal-workflow-queue" {
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "celestial-signal-workflow-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  team                                    = "Platform"
  avg_number_of_messages_visible          = 100
  avg_number_of_messages_not_visible      = 200
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 150
}

module "ca-user-heartbeat-notification-queue" {
  service                                   = "connectedaccount"
  bu-name                                   = "wealth"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "ca-user-heartbeat-notification-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 150
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq         = 604800
  team                                    = "G3"
}

module "salary-program-employment-update-queue" {
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "salary-program-employment-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 150
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["employment-update-topic"]
}

module "rewards-salaryprogram-salary-txn-detection-consumer-queue" {
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-salaryprogram-salary-txn-detection-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["salaryprogram-salary-txn-detection-topic"]
}

module "rewards-salaryprogram-salary-status-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-salaryprogram-salary-status-update-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["salaryprogram-status-update-topic"]
}


module "fittt-salaryprogram-salary-txn-detection-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "fittt-salaryprogram-salary-txn-detection-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["salaryprogram-salary-txn-detection-topic"]
}

module "mf-defer-pn-delay-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "mf-defer-pn-delay-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "mf-order-delayed-pn-delay-queue" {
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "mf-order-delayed-pn-delay-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "wonb-user-comms-delay-queue" {
  service                                   = "wealthonboarding"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "wonb-user-comms-delay-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "celestial-initiate-workflow-queue" {
  service                                   = "celestial"
  bu-name                                   = "platform"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "celestial-initiate-workflow-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 15
  enable_cloudwatch_alarm_sqs             = true
  team                                    = "Platform"
  avg_number_of_messages_visible          = 100
  avg_number_of_messages_not_visible      = 200
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 150
}

module "insights-onb-stage-update-queue" {
  service                                   = "insights"
  bu-name                                   = "demystifi"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "insights-onb-stage-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 11
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["onboarding-stage-update-topic"]
}

module "search-update-ontology-queue" {
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-update-ontology-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["txn-category-ontology-updates-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "reminder-category-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "budgeting"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "reminder-category-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 90
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["txn-category-detail-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  team                                          = "G3"
}

module "reminder-subscription-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "budgeting"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "reminder-subscription-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  team                                          = "G3"
}

module "search-cc-txn-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-cc-txn-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["cc-transaction-event-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  avg_number_of_messages_not_visible            = 500
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  team                                          = "G3"
}

module "rewards-onboarding-stage-update-queue" {
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-onboarding-stage-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["onboarding-stage-update-topic"]
}

module "inbound-upi-txn-queue" {
  source                             = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                         = "inbound-upi-txn-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 11
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  # todo(rohan): reduce this once the avg count goes down post adding caching for order and txns
  avg_number_of_messages_visible     = 25000
  avg_number_of_messages_not_visible = 500
  cloudwatch_alarm_priority          = "P0"
}

module "order-savings-ledger-recon-queue" {
  service                                   = "order"
  bu-name                                   = "pay"
  source                             = "../../modules/sns_sqs/v2"
  suffix                             = var.suffix
  queue_name                         = "order-savings-ledger-recon-queue"
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 900
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 7
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 4000
  avg_number_of_messages_not_visible = 500
  cloudwatch_alarm_priority          = "P0"
}

module "unverified-vpa-queue" {
  source                             = "../../modules/sns_sqs/v2"
  bu-name                                   = "pay"
  queue_name                         = "unverified-vpa-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 11
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 250
  avg_number_of_messages_not_visible = 400
  cloudwatch_alarm_priority          = "P0"
}

module "upi-resp-complaint-queue" {
  encrypted                                    = true
  source                             = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                         = "upi-resp-complaint-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 11
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 250
  avg_number_of_messages_not_visible = 400
  cloudwatch_alarm_priority          = "P0"
}

module "cx-ticket-data-reconciliation-event-queue" {
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "cx-ticket-data-reconciliation-event-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 300
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 4
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  cloudwatch_alarm_priority               = "P1"
}
module "risk-redlist-update-queue" {
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "risk-redlist-update-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 11
  enable_queue_s3_events                  = true
  buckets                                 = ["epifi-data-services", "epifi-prod-onboarding"]
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  team                                    = "G4"
  avg_number_of_messages_visible          = 5
  avg_number_of_messages_not_visible      = 10
  approximate_number_messages_visible_dlq = 20
  cloudwatch_alarm_priority               = "P1"
}
module "preapprovedloan-eligible-users-file-queue" {
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "preapprovedloan-eligible-users-file-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
  enable_queue_s3_events     = true
  buckets                    = ["epifi-prod-preapprovedloan"]
}
module "preapprovedloan-vendor-eligible-users-file-queue" {
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "preapprovedloan-vendor-eligible-users-file-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
  enable_queue_s3_events     = true
  buckets                    = ["epifi-prod-preapprovedloan"]
}
module "loan-inbound-transaction-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "loan-inbound-transaction-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "cc-transactions-for-pinot-queue" {
  encrypted                                    = true
  service                                   = "firefly"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "cc-transactions-for-pinot-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["cc-transaction-event-topic"]
}

module "cc-categorizer-update-for-pinot-queue" {
  encrypted                                    = true
  service                                   = "firefly"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "cc-categorizer-update-for-pinot-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["txn-category-detail-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"CategorizerDataChannel\":[\"DATA_CHANNEL_FI_CARD\"]}"
}

module "goals-investment-instrument-updates-queue" {
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "goals-investment-instrument-updates-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 30
  enable_cloudwatch_alarm_sqs = false
}

module "celestial-initiate-procrastinator-workflow-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "celestial-initiate-procrastinator-workflow-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 15
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 1000
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "Pay"
}
module "bankcust-customer-creation-queue" {
  encrypted                                    = true
  service                                   = "bankcust"
  bu-name                                   = "onboarding"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "bankcust-customer-creation-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}
module "bankcust-customer-creation-callback-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "bankcust-customer-creation-callback-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
}

module "debit-card-creation-success-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "debit-card-creation-callback-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 30
  sns_topic_from_where_message_is_published = ["card-creation-event-topic"]
}

module "onboarding-card-creation-event-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "onboarding-card-creation-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 6
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["card-creation-event-topic"]
}

module "nudge-kyc-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "nudge-kyc-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  sns_topic_from_where_message_is_published = ["user-kyc-level-upgrade-topic"]

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
}

module "nudge-order-update-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "nudge-order-update-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team                    = "growth-experience"
  sns_filter_policy_scope = "MessageAttributes"
  sns_filter_policy       = "{\"OrderStatus\":[\"PAID\",\"FULFILLED\",\"SETTLED\"]}"
}

module "nudge-ca-account-update-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-ca-account-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["ca-account-update-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
}

module "nudge-reward-generation-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-reward-generation-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["reward-generation-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 70000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 10000

  team = "growth-experience"
}

module "nudge-reward-status-update-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-reward-status-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["rewards-reward-status-update-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
}

module "nudge-exit-evaluator-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-exit-evaluator-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
}

module "nudge-dismissal-feedback-info-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-dismissal-feedback-info-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  sns_topic_from_where_message_is_published     = ["feedback-info-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 1000
  team                                          = "growth-experience"
}

module "vendormapping-actor-creation-event-queue" {
  encrypted                                    = true
  service                                   = "vendormapping"
  bu-name                                   = "horizontal"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "vendormapping-actor-creation-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["actor-creation-event-topic"]
}

module "vn-freshchat-action-callback-queue" {
  encrypted                                    = true
  source                         = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                     = "vn-freshchat-action-callback-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 6
  enable_cloudwatch_alarm_sqs    = true
  team                           = "growth-experience"
  avg_number_of_messages_visible = 10
  cloudwatch_alarm_priority      = "P0"
}

module "shipping-update-savings-account-state-update-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "onboarding-savings-account-state-update-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = false
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  # trigger alert when visible messages in dlq exceeds given threshold
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["savings-account-state-update"]
}

module "usstock-process-catalog-update-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "usstock-process-catalog-update-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 4
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-usstocks-alpaca", "epifi-${var.env}-usstocks-morningstar-u"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "usetf-process-catalog-update-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "usetf-process-catalog-update-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 4
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-usstocks-alpaca", "epifi-${var.env}-usstocks-morningstar-u"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "usstocks-process-reverse-feed-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "usstocks-process-reverse-feed-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 4
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-usstocks-alpaca"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "usstocks-oms-order-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "usstocks-oms-order-update-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderTags\":[\"US_STOCKS_DIVIDEND_CREDIT\",\"US_STOCKS_AGGREGATED_INWARD_REMITTANCE\"]}"
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "tss-webhook-callback-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "tss-webhook-callback-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "pay-international-fund-transfer-process-file-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "pay-international-fund-transfer-process-file-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 4
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-pay-international-fund-transfer"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "investment-event-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "wealth"
  queue_name                 = "investment-event-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "nudge-investment-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-investment-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["investment-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "rewards-investment-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-investment-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  sns_topic_from_where_message_is_published     = ["investment-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "rewards-order-update-queue" {
  encrypted                                    = true
  source                          = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                      = "rewards-order-update-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30
  sns_topic_from_where_message_is_published = ["order-update-topic"]

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
  sns_filter_policy_scope        = "MessageAttributes"
  sns_filter_policy              = "{\"OrderStatus\":[\"PAID\",\"FULFILLED\",\"SETTLED\"]}"
}

module "upi-vpa-migration-consent-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "upi-vpa-migration-consent-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 16
}

module "aml-file-generation-queue" {
  encrypted                                    = true
  service                                   = "aml"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "aml-file-generation-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "usstocks-fetch-reverse-feed-file-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "usstocks-fetch-reverse-feed-file-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 6
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "cc-statement-notification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "cc-statement-notification-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_queue_s3_events     = true
}

module "nudge-salaryprogram-status-update-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "nudge-salaryprogram-status-update-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  sns_topic_from_where_message_is_published = ["salaryprogram-status-update-topic"]

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
}
module "salaryprogram-healthinsurance-poll-policy-purchase-status-queue" {
  encrypted                                    = true
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "salaryprogram-healthinsurance-poll-policy-purchase-status-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "central-growth"
}

module "salaryprogram-healthinsurance-policy-issuance-completion-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "salaryprogram-healthinsurance-policy-issuance-completion-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "central-growth"
}

module "onboarding-liveness-manual-review-notification-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "onboarding-liveness-manual-review-notification-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 11
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = [
    "liveness-manual-review-topic"
  ]
}

module "risk-cases-ingestion-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "risk-cases-ingestion-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 4
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  team                                    = "G4"
  avg_number_of_messages_visible          = 400
  avg_number_of_messages_not_visible      = 200
  approximate_number_messages_visible_dlq = 20
  cloudwatch_alarm_priority               = "P1"
}

module "cx-create-ticket-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "cx-create-ticket-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs             = true
  team                                    = "growth-experience"
  avg_number_of_messages_visible          = 100
  cloudwatch_alarm_priority               = "P0"
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
}

# check this
module "usstocks-aml-action-consumer-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "usstocks-aml-action-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 6
  sns_topic_from_where_message_is_published = ["aml-case-decision-topic"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}
module "rewards-claim-reward-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-claim-reward-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "growth-experience"
}
module "rewards-clawback-event-collector-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-clawback-event-collector-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "growth-experience"
}
module "rewards-credit-card-txn-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-credit-card-txn-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["cc-transaction-event-topic"]
}

module "usstocks-catalog-refresh-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "usstocks-catalog-refresh-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 11
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "usstocks-account-activity-sync-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "usstocks-account-activity-sync-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 6
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}
module "lending-credit-report-presence-queue" {
  encrypted                                    = true
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "lending-credit-report-presence-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 30
  enable_cloudwatch_alarm_sqs = false
}

module "lending-credit-report-verification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "lending-credit-report-verification-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}
module "analyser-investment-analysis-task-queue" {
  encrypted                                    = true
  service                                   = "analyser"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "analyser-investment-analysis-task-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 250000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "analyser-investment-event-queue" {
  encrypted                                    = true
  service                                   = "analyser"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "analyser-investment-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 200
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["investment-event-topic"]
}

module "vkyc-order-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "kyc"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "vkyc-order-update-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  team                                      = "Onboarding"
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderStatus\":[\"PAID\",\"SETTLED\"]}"
}

module "categorizer-cc-transaction-event-queue" {
  encrypted                                    = true
  service                                   = "categorizer"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "categorizer-cc-transaction-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 200
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["cc-transaction-event-topic"]
}

module "transaction-notification-queue" {
  encrypted                                    = true
  service                                   = "order"
  bu-name                                   = "pay"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "transaction-notification-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
}

module "tiering-process-usstocks-wallet-order-event-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "tiering-process-usstocks-wallet-order-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 20
  enable_cloudwatch_alarm_sqs               = false
  avg_number_of_messages_visible            = 200
  enable_cloudwatch_alarm_sqs_dlq           = false
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["celestial-workflow-update-topic"]
#  sns_filter_policy_scope                   = "MessageAttributes"
#  sns_filter_policy                         = "{\"CelestialWorkflowType\":[\"ADD_FUNDS_TO_WALLET\"],\"CelestialWorkflowStage\":[\"TRACK_WALLET_FUND_TRANSFER\"],\"CelestialWorkflowStageStatus\":[\"SUCCESSFUL\"]}"
}

module "tiering-process-investment-event-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "tiering-process-investment-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs               = false
  avg_number_of_messages_visible            = 200
  enable_cloudwatch_alarm_sqs_dlq           = false
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["investment-event-topic"]
#  sns_filter_policy_scope                   = "MessageAttributes"
#  sns_filter_policy                         = "{\"InvestmentInstrumentType\":[\"SMART_DEPOSIT\", \"FIXED_DEPOSIT\"],\"EventType\":[\"EVENT_TYPE_DEPOSIT_BALANCE_UPDATE\"]}"
}

module "tiering-process-kyc-update-event-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "tiering-process-kyc-update-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs               = false
  avg_number_of_messages_visible            = 200
  enable_cloudwatch_alarm_sqs_dlq           = false
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["user-kyc-level-upgrade-topic"]
}

module "savings-kyc-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "savings-kyc-update-event-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs               = false
  avg_number_of_messages_visible            = 200
  enable_cloudwatch_alarm_sqs_dlq           = false
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["user-kyc-level-upgrade-topic"]
}

module "nudge-upi-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-upi-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  sns_topic_from_where_message_is_published     = ["pay-upi-event-topic"]
  team                                          = "G3"
}

module "tiering-process-salary-update-event-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "tiering-process-salary-update-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs               = false
  avg_number_of_messages_visible            = 200
  enable_cloudwatch_alarm_sqs_dlq           = false
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["salaryprogram-status-update-topic"]
}

module "tiering-order-add-funds-events-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "tiering-order-add-funds-events-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  enable_cloudwatch_alarm_sqs_dlq           = false
  team                                      = "central-growth"
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderWorkflow\":[\"ADD_FUNDS\",\"ADD_FUNDS_COLLECT\", \"P2P_FUND_TRANSFER\"]}"
}

module "salaryprogram-aa-salary-order-update-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "salaryprogram-aa-salary-order-update-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  enable_cloudwatch_alarm_sqs_dlq           = false
  team                                      = "central-growth"
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"Amount\":[{\"numeric\":[\">=\",10000]}],\"OrderStatus\":[\"PAID\"]}"
}


module "vn-process-mf-holdings-webhook-extended-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "vn-process-mf-holdings-webhook-extended-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 21
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 200
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "G3"
}

module "process-balance-update-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "process-balance-update-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  team                                      = "Pay"
  sns_topic_from_where_message_is_published = ["balance-update-topic"]
}

module "categorizer-crowd-aggregation-category-update-queue" {
  encrypted                                    = true
  service                                   = "categorizer"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "categorizer-crowd-aggregation-category-update-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  buckets                                       = ["epifi-data-services"]
  cross_accounts_queue_publisher_access         = ["************"]
  team                                          = "G3"
}

module "mf-historical-nav-request-queue" {
  encrypted                                    = true
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "mf-historical-nav-request-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "ca-first-data-pull-queue" {
  encrypted                                    = true
  service                                   = "connectedaccount"
  bu-name                                   = "wealth"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "ca-first-data-pull-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 9
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq         = 604800
  team                                    = "G3"
}

module "cx-watson-incident-reporting-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "cx-watson-incident-reporting-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  avg_number_of_messages_visible          = 500
  cloudwatch_alarm_priority               = "P0"
}

module "cx-watson-incident-resolution-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "cx-watson-incident-resolution-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  avg_number_of_messages_visible          = 1000
  cloudwatch_alarm_priority               = "P1"
}

module "order-update-preapprovedloan-queue" {
  encrypted                                    = true
  service                                   = "preapprovedloan"
  bu-name                                   = "lending"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "order-update-preapprovedloan-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 30
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageBody"
  sns_filter_policy                         = "{\"orderWithTransactions\":{\"order\":{\"tags\":[\"LOAN\"]}}}"
}

module "investment-event-based-notifications-queue" {
  encrypted                                    = true
  source                      = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                  = "investment-event-based-notifications-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 11
  enable_cloudwatch_alarm_sqs = false
}

module "mf-reports-queue" {
  encrypted                                    = true
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "mf-reports-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 11
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "credit-report-derived-attributes-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "credit-report-derived-attributes-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 3
  enable_cloudwatch_alarm_sqs = false
}

module "usstocks-celestial-wf-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "usstocks-celestial-wf-update-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 20
  sns_topic_from_where_message_is_published = ["celestial-workflow-update-topic"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "user-contact-onboarding-stage-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "user-contact-onboarding-stage-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 3
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["onboarding-stage-update-topic"]
}

module "user-contact-auth-factor-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "user-contact-auth-factor-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 3
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["auth-factor-update"]
}

module "rewards-reward-status-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-reward-status-update-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["rewards-reward-status-update-event-topic"]
}

module "rewards-credit-card-request-stage-update-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-credit-card-request-stage-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["cc-stage-update-event-topic"]
}

module "budgeting-reminder-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "budgeting-reminder-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  team                                          = "G3"
}

module "salaryprogram-ops-salary-ver-eligibility-refresh-order-update-queue" {
  encrypted                                    = true
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "salaryprogram-ops-salary-ver-eligibility-refresh-order-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 300
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 12
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
  sns_topic_from_where_message_is_published     = ["order-update-topic"]
  sns_filter_policy_scope                       = "MessageAttributes"
  sns_filter_policy                             = "{\"Amount\":[{\"numeric\":[\">=\",5000]}],\"PaymentProtocols\":[\"NEFT\",\"RTGS\",\"IMPS\",\"INTRA_BANK\",\"PAYMENT_PROTOCOL_UNSPECIFIED\"]}"
}

module "salaryprogram-salary-detection-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "salaryprogram-salary-detection-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 300
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
  sns_topic_from_where_message_is_published     = ["salaryprogram-salary-txn-detection-topic"]
}
module "card-switch-financial-notification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  suffix                     = var.suffix
  queue_name                 = "card-switch-financial-notification-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_cloudwatch_alarm_sqs_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  team                                    = "Cards"
}

module "card-switch-non-financial-notification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  suffix                     = var.suffix
  queue_name                 = "card-switch-non-financial-notification-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "reminder-action-executor-queue" {
  encrypted                                    = true
  service                                   = "budgeting"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "reminder-action-executor-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 10800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}


module "usstocks-send-mail-to-users-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "usstocks-send-mail-to-users-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 6
}
module "deemed-payment-upi-enquiry-queue" {
  encrypted                                    = true
  service                                   = "order"
  bu-name                                   = "pay"
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "deemed-payment-upi-enquiry-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 70
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 46
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 50
  avg_number_of_messages_not_visible = 400
}

module "nudge-entry-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  suffix                                        = var.suffix
  queue_name                                    = "nudge-entry-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "growth-experience"
}

module "nudge-exit-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  suffix                                        = var.suffix
  queue_name                                    = "nudge-exit-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "growth-experience"
}

module "rewards-reward-clawback-event-collector-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  queue_name                                    = "rewards-reward-clawback-event-collector-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  team                                          = "growth-experience"
}

module "rewards-reward-clawback-processing-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-reward-clawback-processing-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  team                                          = "growth-experience"
}

module "rewards-cc-bill-generation-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-cc-bill-generation-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["cc-bill-generation-event-topic"]
}

module "order-in-payment-order-update-queue" {
  encrypted                                    = true
  source                             = "../../modules/sns_sqs/v2"
  bu-name                                   = "pay"
  queue_name                         = "order-in-payment-order-update-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 70
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 9
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 50
  avg_number_of_messages_not_visible = 400
}

module "rewards-notification-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-notification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "p2investment-db-operations-queue" {
  encrypted                                    = true
  service                                   = "p2pinvestment"
  bu-name                                   = "wealth"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "p2investment-db-operations-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 11
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 2
  team                                    = "G3"
}

module "cc-cards-sent-for-printing-queue" {
  encrypted                                    = true
  service                                   = "firefly"
  bu-name                                   = "cards"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "cc-cards-sent-for-printing-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-creditcard-m2p-upload"]
}

module "cc-cards-dispatched-queue" {
  encrypted                                    = true
  service                                   = "firefly"
  bu-name                                   = "cards"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "cc-cards-dispatched-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-creditcard-seshaasai-upload"]
}

module "dc-cards-sent-for-printing-queue" {
  encrypted                                    = true
  service                                   = "card"
  bu-name                                   = "cards"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "dc-cards-sent-for-printing-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
  enable_queue_s3_events     = true
  buckets                    = ["epifi-${var.env}-debit-card-docs"]
}

module "nudge-preapprovedloan-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "nudge-preapprovedloan-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "Lending"
}

module "non-financial-investment-event-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "wealth"
  suffix                                  = var.suffix
  queue_name                              = "non-financial-investment-event-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 11
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  avg_number_of_messages_not_visible      = 100
  team                                    = "G3"
}

module "nudge-non-financial-investment-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "nudge-non-financial-investment-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["non-financial-investment-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  avg_number_of_messages_not_visible            = 100
  team                                          = "G3"
}

module "lending-creditreportv2-presence-queue" {
  encrypted                                    = true
  service                                   = "creditreportv2"
  bu-name                                   = "lending"
  source                      = "../../modules/sns_sqs/v2"
  queue_name                  = "lending-creditreportv2-presence-queue"
  suffix                      = var.suffix
  resource_is_queue           = true
  enable_dlq                  = true
  env                         = var.env
  region                      = var.region
  resource_owner              = var.resource_owner
  owner                       = var.owner
  delay_seconds               = 0
  max_message_size            = 262144
  visibility_timeout_seconds  = 120
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 0
  maxReceiveCount             = 3
  enable_cloudwatch_alarm_sqs = false
}

module "lending-creditreportv2-verification-queue" {
  encrypted                                    = true
  service                                   = "creditreportv2"
  bu-name                                   = "lending"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "lending-creditreportv2-verification-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 3
}

module "lending-creditreportv2-derivedattributes-queue" {
  encrypted                                    = true
  service                                   = "creditreportv2"
  bu-name                                   = "lending"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "lending-creditreportv2-derivedattributes-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 3
}
module "cx-chowkidar-alert-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "cx-chowkidar-alert-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  avg_number_of_messages_visible          = 100
  cloudwatch_alarm_priority               = "P0"
}

module "dc-forex-refund-order-update-queue" {
  encrypted                                    = true
  service                                   = "card"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "dc-forex-refund-order-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 12
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"PaymentProtocols\":[\"CARD\",\"INTRA_BANK\"]}"
}

module "dc-user-device-properties-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "card"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "dc-user-device-properties-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount            = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["user-device-properties-update-topic"]
  sns_filter_policy_scope                   = "MessageBody"
  sns_filter_policy                         = "{\"updatedProperties\":[\"DEVICE_PROP_DEVICE_LOCATION_TOKEN\"]}"
}

module "card-txns-update-queue" {
  encrypted                                    = true
  service                                   = "card"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "card-txns-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 12
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"PaymentProtocols\":[\"CARD\"]}"
}

module "health-state-update-queue" {
  encrypted                                    = true
  service                                   = "health_engine"
  bu-name                                   = "pay"
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "health-state-update-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 172800
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 11
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 10
  avg_number_of_messages_not_visible = 20
}

module "order-aa-first-data-pull-txn-queue" {
  encrypted                                    = true
  service                                   = "order"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "order-aa-first-data-pull-txn-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 13
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 50000
  avg_number_of_messages_not_visible        = 10000
  enable_cloudwatch_alarm_sqs_dlq           = true
  approximate_number_messages_visible_dlq   = 50
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq           = 604800
  team                                      = "G3"
  sns_topic_from_where_message_is_published = ["ca-data-transaction-topic"]
  sns_filter_policy_scope                   = "MessageBody"
  sns_filter_policy                         = "{\"txnEventTypeInitiatedBy\":[\"TRANSACTION_EVENT_TYPE_INITIATED_BY_FIRST_DATA_PULL_TXNS\"]}"
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  cloudwatch_alarm_priority                = "P0"
}

module "casper-vistara-upload-file-queue" {
  encrypted                                    = true
  service                                   = "casper"
  bu-name                                   = "growth-infra"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "casper-vistara-upload-file-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 13

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 2
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 2

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 2
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "casper-vistara-download-file-queue" {
  encrypted                                    = true
  service                                   = "casper"
  bu-name                                   = "growth-infra"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "casper-vistara-download-file-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 13

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 2
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 2

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 2
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "casper-itc-upload-file-queue" {
  encrypted                                    = true
  service                                   = "casper"
  bu-name                                   = "growth-infra"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "casper-itc-upload-file-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 13

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 2
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 2

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 2
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "casper-itc-download-file-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  suffix                     = var.suffix
  queue_name                 = "casper-itc-download-file-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 13

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 2
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 2

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 2
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "kyc-epan-comms-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "kyc-epan-comms-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "kyc-pan-comms-queue" {
  encrypted                                    = true
  service                                   = "pan"
  bu-name                                   = "onboarding"
  source                         = "../../modules/sns_sqs/v2"
  suffix                         = var.suffix
  queue_name                     = "kyc-pan-comms-queue"
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 30
  enable_cloudwatch_alarm_sqs    = true
  team                           = "Onboarding"
  avg_number_of_messages_visible = 100
}

module "referrals-order-update-first-add-funds-queue" {
  encrypted                                    = true
  service                                   = "inappreferral"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "referrals-order-update-first-add-funds-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Acquisitions"
  cloudwatch_alarm_priority                 = "P1"
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderTags\":[{\"exists\":false}]}"
}

module "recurring-txns-ds-file-upload-events-queue" {
  encrypted                                    = true
  service                                   = "upcomingtransactions"
  bu-name                                   = "demystifi"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "recurring-txns-ds-file-upload-events-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 1800
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 7
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  team                                    = "G3"
  buckets                                 = ["epifi-data-services"]
  cross_accounts_queue_publisher_access   = ["************"]
}

module "third-party-account-sharing-event-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "third-party-account-sharing-event-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 9
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  team                                    = "central-growth"
  approximate_number_messages_visible_dlq = 1
}

module "upcoming-transactions-update-order-queue" {
  encrypted                                    = true
  service                                   = "upcomingtransactions"
  bu-name                                   = "demystifi"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "upcoming-transactions-update-order-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  # TODO: set to true when upcoming transactions goes live in prod
  # enable_cloudwatch_alarm_sqs     = true
  # avg_number_of_messages_visible  = 2000
  # enable_cloudwatch_alarm_sqs_dlq = true
  # approximate_number_messages_visible_dlq   = 10
  team                                      = "G3"
  sns_topic_from_where_message_is_published = ["order-update-topic"]
}

module "risk-sync-lea-actors-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                         = "../../modules/sns_sqs/v2"
  suffix                         = var.suffix
  queue_name                     = "risk-sync-lea-actors-queue"
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 30
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 11
  enable_cloudwatch_alarm_sqs    = true
  team                           = "G4"
  avg_number_of_messages_visible = 10
  cloudwatch_alarm_priority      = "P2"
}

module "device-reg-sms-ack-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "device-reg-sms-ack-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 5
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "Onboarding"
  enable_cloudwatch_alarm_sqs             = true
}


module "nudge-debit-card-update-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-debit-card-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["debit-card-update-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "order-update-payincidentmanager-consumer-queue" {
  encrypted                                    = true
  service                                   = "pay"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "order-update-payincidentmanager-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 2000
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Pay"
  sns_topic_from_where_message_is_published = ["order-update-topic"]
}

module "cx-crm-issue-tracker-integration-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                         = "../../modules/sns_sqs/v2"
  queue_name                     = "cx-crm-issue-tracker-integration-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 6
  enable_cloudwatch_alarm_sqs    = true
  team                           = "growth-experience"
  avg_number_of_messages_visible = 10
  cloudwatch_alarm_priority      = "P0"
}

module "rewards-credit-report-download-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-credit-report-download-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 2000
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["credit-report-download-event-topic"]
}

module "savings-account-operational-status-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "savings-account-operational-status-update-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["account-operational-status-update-topic"]
}

module "upi-user-device-properties-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "upi"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "upi-user-device-properties-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["user-device-properties-update-topic"]
  sns_filter_policy_scope                   = "MessageBody"
  sns_filter_policy                         = "{\"updatedProperties\":[\"DEVICE_PROP_APP_INSTALL_ID\"]}"
}

module "account-status-callback-queue" {
  encrypted                                    = true
  service                                   = "accounts"
  bu-name                                   = "pay"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "account-status-callback-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 9
}

module "investment-order-eta-handler-delay-queue" {
  encrypted                                    = true
  service                                   = "investment"
  bu-name                                   = "wealth"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "investment-order-eta-handler-delay-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 9
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  avg_number_of_messages_not_visible      = 100
  team                                    = "G3"
}

module "raw-txn-processor-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "raw-txn-processor-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_queue_s3_events     = true
}
module "kyc-vkyc-call-completed-event-consumer-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "kyc-vkyc-call-completed-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 30
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["vkyc-call-completed-event-topic"]
}

module "user-vkyc-call-completed-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "user-vkyc-call-completed-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["vkyc-call-completed-event-topic"]
}

module "user-inhouse-vkyc-call-completed-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "user-inhouse-vkyc-call-completed-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["inhouse-vkyc-call-completed-event-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"Group\":[\"CHANNEL_NRI_ONBOARDING\"]}"
}

module "connected-account-enquiry-queue" {
  encrypted                                    = true
  service                                   = "screener"
  bu-name                                   = "onboarding"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "connected-account-enquiry-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 73
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 70
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  avg_number_of_messages_not_visible      = 100
  team                                    = "Onboarding"
}

module "savings-tier-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "savings-tier-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs_dlq           = true
  approximate_number_messages_visible_dlq   = 5
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 3000
  avg_number_of_messages_not_visible        = 5
  team                                      = "central-growth"
  sns_topic_from_where_message_is_published = ["tiering-tier-update-topic"]
}

module "cc-acs-notification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "cc-acs-notification-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_queue_s3_events     = true
}

module "dc-onboarding-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "dc-onboarding-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_queue_s3_events     = true
  team = "card"
}

module "usstocks-remittance-file-processing-events-queue" {
  encrypted                                    = true
  service                                   = "usstocks"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "usstocks-remittance-file-processing-events-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 21
  sns_topic_from_where_message_is_published = ["ift-remittance-file-processing-events-topic"]
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs        = true
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}


module "nudge-offer-redemption-status-update-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "nudge-offer-redemption-status-update-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["casper-offer-redemption-status-update-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}


module "pay-order-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "pay"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "pay-order-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 4
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  enable_cloudwatch_alarm_sqs_dlq           = false
}


module "rewards-offer-redemption-status-update-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-offer-redemption-status-update-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  sns_topic_from_where_message_is_published     = ["casper-offer-redemption-status-update-event-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "nudge-income-update-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "nudge-income-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "Onboarding"
  sns_topic_from_where_message_is_published = ["income-update-event-topic"]
}

module "upi-req-mapper-confirmation-processing-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "upi-req-mapper-confirmation-processing-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 16
}

module "biometrics-details-queue" {
  encrypted                                    = true
  service                                   = "auth"
  bu-name                                   = "onboarding"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "biometrics-details-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "tiering-balance-update-events-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "tiering-balance-update-events-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["balance-update-events-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"CurrentBalance\":[{\"numeric\":[\">\",5000]}],\"DiffBalance\":[{\"numeric\":[\">\",0]}]}"
}

module "savings-account-closure-balance-update-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "savings-account-closure-balance-update-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["balance-update-events-topic"]
  enable_cloudwatch_alarm_sqs               = true
  enable_cloudwatch_alarm_sqs_dlq           = true
  approximate_number_messages_visible_dlq   = 80000
  avg_number_of_messages_visible            = 500
  team                                      = "central-growth"
}

module "kyc-savings-account-state-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "kyc"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "kyc-savings-account-state-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 3
  sns_topic_from_where_message_is_published = ["savings-account-state-update"]
  team                                      = "Onboarding"
}

module "recurringpayment-creation-auth-vendor-callback-queue" {
  encrypted                                    = true
  source                             = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                         = "recurringpayment-creation-auth-vendor-callback-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 172800
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 31
  enable_cloudwatch_alarm_sqs        = true
  enable_cloudwatch_alarm_sqs_dlq    = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 50
  avg_number_of_messages_not_visible = 50
}

module "order-update-recurringpayemnt-consumer-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "order-update-recurringpayemnt-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 172800
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 50
  enable_cloudwatch_alarm_sqs               = true
  team                                      = "Pay"
  avg_number_of_messages_visible            = 50
  avg_number_of_messages_not_visible        = 50
  enable_cloudwatch_alarm_sqs_dlq           = true
}

module "recurringpayemnt-off-app-execution-queue" {
  encrypted                                    = true
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "recurringpayemnt-off-app-execution-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 172800
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 50
  enable_cloudwatch_alarm_sqs        = true
  enable_cloudwatch_alarm_sqs_dlq    = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 50
  avg_number_of_messages_not_visible = 50
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderStatus\":[\"PAID\",\"PAYMENT_FAILED\"],\"PaymentProtocols\":[\"ENACH\",\"CARD\"]}"
}

module "recurringpayment-failed-enach-transaction-queue" {
  encrypted                                    = true
  service                            = "recurringpayment"
  bu-name                            = "pay"
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "recurringpayment-failed-enach-transaction-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 172800
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 21
  enable_cloudwatch_alarm_sqs        = true
  enable_cloudwatch_alarm_sqs_dlq    = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 65000
  avg_number_of_messages_not_visible = 20
}

module "fetch-recurringpayemnt-from-vendor-queue" {
  encrypted                                    = true
  service                            = "recurringpayment"
  bu-name                            = "pay"
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "fetch-recurringpayemnt-from-vendor-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 172800
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 21
  enable_cloudwatch_alarm_sqs        = true
  enable_cloudwatch_alarm_sqs_dlq    = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 65000
  avg_number_of_messages_not_visible = 20
}

module "kyc-v2-update-status-queue" {
  encrypted                                    = true
  service                                   = "vendornotification"
  bu-name                                   = "horizontal"
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "kyc-v2-update-status-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 30
  message_retention_seconds          = 172800
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 50
  enable_cloudwatch_alarm_sqs        = true
  enable_cloudwatch_alarm_sqs_dlq    = true
  team                               = "Onboarding"
  avg_number_of_messages_visible     = 50
  avg_number_of_messages_not_visible = 50
}
module "enach-fund-transfer-order-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "recurringpayment"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "enach-fund-transfer-order-update-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 30
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderWorkflow\":[\"ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER\"], \"OrderStatus\":[\"PAID\"]}"

  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  team                                    = "Pay"
}

module "aa-txn-backfill-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "aa-txn-backfill-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  sns_topic_from_where_message_is_published = ["aa-txn-backfill-topic"]

  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 2000
  team                                    = "G3"
}

module "search-backfill-txn-timeline-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "search-backfill-txn-timeline-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["backfill-txn-timeline-topic"]
  enable_cloudwatch_alarm_sqs_dlq               = true
  approximate_number_messages_visible_dlq       = 10
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 20000
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "txn-detailed-status-update-payincidentmanager-consumer-queue" {
  encrypted                                    = true
  service                                   = "pay"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "txn-detailed-status-update-payincidentmanager-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  team                                      = "Pay"
  sns_topic_from_where_message_is_published = ["txn-detailed-status-update-topic"]
}

module "bank-customer-update-event-onboarding-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "bank-customer-update-event-onboarding-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["bank-customer-update-topic"]
}

module "rewards-reward-unlocker-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-reward-unlocker-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = false
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 100
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 100
  team                                          = "growth-experience"
}

module "cx-watson-ticket-event-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                         = "../../modules/sns_sqs/v2"
  queue_name                     = "cx-watson-ticket-event-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 6
  enable_cloudwatch_alarm_sqs    = true
  team                           = "growth-experience"
  avg_number_of_messages_visible = 100
  cloudwatch_alarm_priority      = "P0"
}

module "order-update-indexing-consumer-queue" {
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "order-update-indexing-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  sns_topic_from_where_message_is_published     = ["order-update-topic"]
  enable_cloudwatch_alarm_sqs                   = true
  team                                          = "G3"
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 15000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  cloudwatch_alarm_priority                     = "P0"
}

module "search-pay-txn-order-backfill-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "search-pay-txn-order-backfill-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  team                                      = "G3"
  sns_topic_from_where_message_is_published = ["pay-txn-order-backfill-topic"]
  enable_cloudwatch_alarm_sqs_dlq           = true
  approximate_number_messages_visible_dlq   = 10
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 20000
}

module "categorizer-pay-txn-order-backfill-queue" {
  encrypted                                    = true
  service                                   = "categorizer"
  bu-name                                   = "demystifi"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "categorizer-pay-txn-order-backfill-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  team                                      = "G3"
  sns_topic_from_where_message_is_published = ["pay-txn-order-backfill-topic"]
  enable_cloudwatch_alarm_sqs_dlq           = false
  approximate_number_messages_visible_dlq   = 10
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 2000
}

module "vn-loans-fiftyfin-callback-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  suffix                     = var.suffix
  queue_name                 = "vn-loans-fiftyfin-callback-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "rewards-projection-update-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-projection-update-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "rewards-projections-generation-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-projections-generation-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "rewards-tiering-periodic-reward-event-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-tiering-periodic-reward-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "bulk-claim-rewards-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "bulk-claim-rewards-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  team                                          = "growth-experience"
}

module "casper-fi-store-order-notification-queue" {
  encrypted                                    = true
  service                                   = "casper"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "casper-fi-store-order-notification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  team                                          = "growth-experience"
}

module "tiering-balance-update-marketing-events-queue" {
  encrypted                                    = true
  service                                   = "tiering"
  bu-name                                   = "central-growth"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "tiering-balance-update-marketing-events-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["balance-update-events-topic"]
  enable_cloudwatch_alarm_sqs               = true
  enable_cloudwatch_alarm_sqs_dlq           = true
  approximate_number_messages_visible_dlq   = 10
  avg_number_of_messages_visible            = 500
  team                                      = "central-growth"
}

module "rewards-tiering-tier-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-tiering-tier-update-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs                   = true
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["tiering-tier-update-topic"]
}

module "salaryprogram-recurring-payment-action-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "salaryprogram-recurring-payment-action-update-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 300
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
  sns_topic_from_where_message_is_published     = ["recurring-payment-action-update-topic"]
}

module "accrual-account-operational-status-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "accrual"
  bu-name                                   = "growth-infra"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "accrual-account-operational-status-update-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "growth-experience"
  sns_topic_from_where_message_is_published = ["account-operational-status-update-topic"]
}

module "risk-call-routing-event-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "risk-call-routing-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 4
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 100
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  cloudwatch_alarm_priority                 = "P2"
  team                                      = "G4"
  sns_topic_from_where_message_is_published = ["cx-call-routing-event-topic"]
}

module "compliance-account-operational-status-update-event-queue" {
  encrypted                                    = true
  service                                   = "bankcust"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "compliance-account-operational-status-update-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["account-operational-status-update-topic"]
}

module "employer-pi-mapping-update-event-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "employer-pi-mapping-update-event-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 10
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  team                                    = "central-growth"
}

module "risk-dp-rule-hit-callback-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "risk-dp-rule-hit-callback-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 300
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 4
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  cloudwatch_alarm_priority               = "P0"
  team                                    = "G4"
}

module "deposit-maturity-action-callback-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "deposit-maturity-action-callback-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 9
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 500
  enable_cloudwatch_alarm_sqs_dlq         = true
  seconds_for_oldest_messages_dlq         = 604800
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  cloudwatch_alarm_priority               = "P0"
  team                                    = "G3"
}

module "comms-email-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-email-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 21
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-notification-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-notification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-high-priority-notification-queue" {
  encrypted                                    = true
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-high-priority-notification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-normal-priority-notification-queue" {
  encrypted                                    = true
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-normal-priority-notification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-low-priority-notification-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-low-priority-notification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs                   = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 50000
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}


module "comms-sms-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-sms-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 16
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-whatsapp-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-whatsapp-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-notification-update-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-notification-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "whatsapp-bot-queue" {
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  queue_name                              = "whatsapp-bot-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 11
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 10
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  cloudwatch_alarm_priority               = "P0"
  team                                    = "growth-experience"
}

module "comms-sms-status-update-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-sms-status-update-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}

module "comms-sms-wait-status-queue" {
  service                                   = "comms"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-sms-wait-status-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  cloudwatch_alarm_priority                     = "P0"
  team                                          = "growth-experience"
}


module "pan-vkyc-call-completed-event-consumer-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "pan-vkyc-call-completed-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  team                                      = "Onboarding"
  sns_topic_from_where_message_is_published = ["vkyc-call-completed-event-topic"]
}

module "salaryprogram-onboarding-stage-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "salaryprogram"
  bu-name                                   = "central-growth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "salaryprogram-onboarding-stage-update-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 300
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
  sns_topic_from_where_message_is_published     = ["onboarding-stage-update-topic"]
}
module "timeline-indexing-consumer-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "timeline-indexing-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 5000
  cloudwatch_alarm_priority                     = "P0"
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["timeline-topic"]
}
module "search-update-aa-txn-consumer-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-update-aa-txn-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 10000
  cloudwatch_alarm_priority                     = "P0"
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["order-aa-txn-topic"]
}

module "pan-bkyc-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "pan"
  bu-name                                   = "onboarding"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "pan-bkyc-update-event-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  suffix                                    = var.suffix
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["bkyc-update-event-topic"]
}

module "risk-dispute-upload-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "risk-dispute-upload-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  suffix                     = var.suffix
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
}

module "lending-credit-report-flattening-queue" {
  encrypted                                    = true
  service                                   = "creditreportv2"
  bu-name                                   = "lending"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "lending-credit-report-flattening-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  suffix                     = var.suffix
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 16
}

module "risk-form-submission-event-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "risk-form-submission-event-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  suffix                     = var.suffix
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  team                                    = "G4"
  avg_number_of_messages_visible          = 400
  avg_number_of_messages_not_visible      = 200
  approximate_number_messages_visible_dlq = 20
  cloudwatch_alarm_priority               = "P1"
}

module "cx-s3-event-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  suffix                                  = var.suffix
  queue_name                              = "cx-s3-event-queue"
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  avg_number_of_messages_visible          = 100
  cloudwatch_alarm_priority               = "P0"
  buckets                                 = ["epifi-data-services"]
  cross_accounts_queue_publisher_access   = ["************"]
}

module "order-update-order-notification-consumer-queue" {
  service                                   = "order"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "order-update-order-notification-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 15
  enable_cloudwatch_alarm_sqs               = true
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  team                                      = "Pay"
  # reason for a high value: These many msgs lead to a delay of ~10min. We are fine to not act on it till then.
  # this usually happens when CRDB is under load and there is no direct AI.
  avg_number_of_messages_visible            = 25000
  cloudwatch_alarm_priority                 = "P0"
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 100
}

module "order-notification-fallback-consumer-queue" {
  service                                   = "order"
  bu-name                                   = "pay"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "order-notification-fallback-consumer-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  team                                    = "Pay"
  # reason for a high value: These many msgs lead to a delay of ~10min. We are fine to not act on it till then.
  # this usually happens when CRDB is under load and there is no direct AI.
  avg_number_of_messages_visible          = 2000
  cloudwatch_alarm_priority               = "P0"
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 2000
}

module "order-workflow-processing-queue" {
  service                                   = "order"
  bu-name                                   = "pay"
  source                             = "../../modules/sns_sqs/v2"
  queue_name                         = "order-workflow-processing-queue"
  suffix                             = var.suffix
  resource_is_queue                  = true
  enable_dlq                         = true
  env                                = var.env
  region                             = var.region
  resource_owner                     = var.resource_owner
  owner                              = var.owner
  delay_seconds                      = 0
  max_message_size                   = 262144
  visibility_timeout_seconds         = 60
  message_retention_seconds          = 1209600
  receive_wait_time_seconds          = 0
  maxReceiveCount                    = 100
  enable_cloudwatch_alarm_sqs        = true
  team                               = "Pay"
  avg_number_of_messages_visible     = 200
  avg_number_of_messages_not_visible = 400
  cloudwatch_alarm_priority          = "P0"
  enable_cloudwatch_alarm_sqs_dlq    = true
}

module "order-update-timeline-consumer-queue" {
  service                                   = "timeline"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "order-update-timeline-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 9
  enable_cloudwatch_alarm_sqs               = true
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  team                                      = "Pay"
  avg_number_of_messages_visible            = 7000
  cloudwatch_alarm_priority                 = "P0"
  enable_cloudwatch_alarm_sqs_dlq           = true
  #    trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 100
}

module "comms-gupshup-whatsapp-callback-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "comms-gupshup-whatsapp-callback-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 5
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
}

module "comms-gupshup-rcs-callback-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "comms-gupshup-rcs-callback-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 5
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
}

module "comms-kaleyra-sms-callback-sftp-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "comms-kaleyra-sms-callback-sftp-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
}

module "rewards-account-operational-status-update-consumer-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "rewards-account-operational-status-update-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "growth-experience"
  sns_topic_from_where_message_is_published = ["account-operational-status-update-topic"]
}

module "risk-alert-ingestion-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "risk-alert-ingestion-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 300
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 8
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  team                                    = "G4"
  avg_number_of_messages_visible          = 400
  avg_number_of_messages_not_visible      = 200
  approximate_number_messages_visible_dlq = 20
  cloudwatch_alarm_priority               = "P1"
}

module "salaryprogram-salary-status-update-event-consumer-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                                    = "salaryprogram-salary-status-update-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 300
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
  sns_topic_from_where_message_is_published     = ["salaryprogram-status-update-topic"]
}

module "generate-sof-limit-strategies-values-queue" {
  encrypted                                    = true
  service                                   = "pay"
  bu-name                                   = "pay"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "generate-sof-limit-strategies-values-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
}

module "retry-offer-redemption-queue" {
  service                                   = "casper"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "retry-offer-redemption-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 90
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "search-sherlock-scripts-queue" {
  encrypted                                    = true
  service                                   = "search"
  bu-name                                   = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "search-sherlock-scripts-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 100
  avg_number_of_messages_not_visible            = 100
  cloudwatch_alarm_priority                     = "P0"
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "growth-experience"
}

module "cc-credit-report-download-event-queue" {
  encrypted                                    = true
  service                                   = "firefly"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "cc-credit-report-download-event-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 2000
  enable_cloudwatch_alarm_sqs_dlq           = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq   = 10
  team                                      = "Cards"
  sns_topic_from_where_message_is_published = ["credit-report-download-event-topic"]
}

module "bankcust-bkyc-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "bankcust"
  bu-name                                   = "onboarding"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "bankcust-bkyc-update-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "Onboarding"
  sns_topic_from_where_message_is_published     = ["bkyc-update-event-topic"]
}

module "bankcust-kyc-state-change-event-consumer-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                              = "bankcust-kyc-state-change-event-consumer-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 11
  enable_cloudwatch_alarm_sqs             = true
  cloudwatch_alarm_priority               = "P1"
  avg_number_of_messages_visible          = 100
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 5
  team                                    = "Onboarding"
}
module "credit-card-auth-factor-update-event-consumer-queue" {
  encrypted                                    = true
  service                                   = "firefly"
  bu-name                                   = "cards"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "credit-card-auth-factor-update-event-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  sns_topic_from_where_message_is_published = ["auth-factor-update"]
}

module "rewards-reward-expiry-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                   = "growth-infra"
  queue_name                                    = "rewards-reward-expiry-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
}

module "bank-customer-residential-status-update-consumer-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  service                                 = "user"
  bu-name                                 = "onboarding"
  queue_name                              = "bank-customer-residential-status-update-consumer-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  cloudwatch_alarm_priority               = "P1"
  avg_number_of_messages_visible          = 100
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 5
  team                                    = "Onboarding"
}

module "bank-customer-mobile-number-update-consumer-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  service                                 = "user"
  bu-name                                 = "onboarding"
  queue_name                              = "bank-customer-mobile-number-update-consumer-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  cloudwatch_alarm_priority               = "P1"
  avg_number_of_messages_visible          = 100
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 5
  team                                    = "Onboarding"
}

module "bank-customer-auth-factor-update-event-consumer-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  service                                 = "user"
  bu-name                                 = "onboarding"
  queue_name                              = "bank-customer-auth-factor-update-event-consumer-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  cloudwatch_alarm_priority               = "P1"
  avg_number_of_messages_visible          = 100
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 5
  team                                    = "Onboarding"
  sns_topic_from_where_message_is_published = ["auth-factor-update"]
}

module "rewards-reward-terminal-status-update-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-reward-terminal-status-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["rewards-reward-status-update-event-topic"]
}

module "cc-non-financial-notification-queue" {
  encrypted                                    = true
  source                     = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                 = "cc-non-financial-notification-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 30
  enable_queue_s3_events     = true
}

module "order-update-event-for-compliance-consumer-queue" {
  encrypted                                    = true
  service                                   = "upi"
  bu-name                                   = "pay"
  source                                    = "../../modules/sns_sqs/v2"
  queue_name                                = "order-update-event-for-compliance-consumer-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = false
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 30
  team                                      = "Pay"
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderWorkflow\":[{\"anything-but\":[\"NO_OP\",\"OFF_APP_UPI\"]}],\"PaymentProtocols\":[\"UPI\"]}"
}

module "rewards-projection-event-for-pinot-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-projection-event-for-pinot-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["rewards-projection-event-topic"]
}


module "search-sherlock-sops-queue" {
  encrypted                                    = true
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "search-sherlock-sops-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 30
  enable_cloudwatch_alarm_sqs             = true
  avg_number_of_messages_visible          = 100
  avg_number_of_messages_not_visible      = 100
  cloudwatch_alarm_priority                 = "P0"
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 5
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq           = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                    = "growth-experience"
}

module "usstocks-income-update-event-queue" {
  encrypted                                    = true
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "usstocks-income-update-event-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 30
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  enable_cloudwatch_alarm_sqs_dlq           = false
  sns_topic_from_where_message_is_published = ["income-update-event-topic"]
}

module "user-contact-delete-user-event-consumer-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "user-contact-delete-user-event-consumer-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "Onboarding"
  sns_topic_from_where_message_is_published     = ["delete-user-event-topic"]
}

module "usstocks-tax-document-generation-request-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "usstocks-tax-document-generation-request-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  // no alert added on avg number of msg in main queue as this queue will be filled in bulk
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 86400
  team                                          = "G3"
}


module "fittt-order-update-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-order-update-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 10
  sns_topic_from_where_message_is_published = ["order-update-topic"]

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-cricket-event-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-cricket-event-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-enriched-event-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-enriched-event-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-rms-action-queue" {
  source                          = "../../modules/sns_sqs/v2"
  bu-name                                   = "wealth"
  queue_name                      = "fittt-rms-action-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-aggr-exec-trigger-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-aggr-exec-trigger-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-aggr-exec-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-aggr-exec-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-aggr-entity-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-aggr-entity-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "action-processing-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "action-processing-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 100
  enable_cloudwatch_alarm_sqs        = true
  enable_cloudwatch_alarm_sqs_dlq    = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  cloudwatch_alarm_priority          = "P0"
  avg_number_of_messages_visible     = 50000
  avg_number_of_messages_not_visible = 10000
  approximate_number_messages_visible_dlq = 10
  seconds_for_oldest_messages_dlq               = 172800
  team                                          = "G3"
}


module "rms-event-queue" {
  source                          = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                      = "rms-event-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "fittt-cricket-init-match-update-queue" {
  service                                   = "fittt"
  bu-name                                   = "wealth"
  source                          = "../../modules/sns_sqs/v2"
  queue_name                      = "fittt-cricket-init-match-update-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}


module "fittt-execution-update-queue" {
  source                          = "../../modules/sns_sqs/v2"
  bu-name                                   = "horizontal"
  queue_name                      = "fittt-execution-update-queue"
  suffix                          = var.suffix
  resource_is_queue               = true
  enable_dlq                      = true
  env                             = var.env
  region                          = var.region
  resource_owner                  = var.resource_owner
  owner                           = var.owner
  delay_seconds                   = 0
  max_message_size                = 262144
  visibility_timeout_seconds      = 30
  message_retention_seconds       = 1209600
  receive_wait_time_seconds       = 0
  maxReceiveCount                 = 30

  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "G3"

}

module "onboarding-credit-report-verification-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "onboarding-credit-report-verification-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  approximate_number_messages_visible_dlq       = 10
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  seconds_for_oldest_messages_dlq               = 86400
  team                                          = "Onboarding"
}

module "risk-cx-ticket-update-event-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "risk-cx-ticket-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  // no alert added on avg number of msg in main queue as this queue will be filled in bulk
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 86400
  team                                          = "G4"
  sns_topic_from_where_message_is_published     = ["cx-ticket-update-event-topic"]
  sns_filter_policy_scope                       = "MessageAttributes"
  sns_filter_policy                             = "{\"ProductCategory\":[\"PRODUCT_CATEGORY_FRAUD_AND_RISK\",\"PRODUCT_CATEGORY_RISK\"],\"Group\":[\"GROUP_RISK_OPS\"]}"
}

module "nudge-actor-nudge-status-update-event-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-actor-nudge-status-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = false
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 200
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
  sns_topic_from_where_message_is_published = ["nudge-actor-nudge-status-update-event-topic"]
}


module "order-update-deposit-consumer-queue" {
  service                                   = "deposit"
  bu-name                                   = "wealth"
  source                                    = "../../modules/sns_sqs/v2"
  suffix                                    = var.suffix
  queue_name                                = "order-update-deposit-consumer-queue"
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 0
  max_message_size                          = 262144
  visibility_timeout_seconds                = 60
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 100
  sns_topic_from_where_message_is_published = ["order-update-topic"]
  sns_filter_policy_scope                   = "MessageAttributes"
  sns_filter_policy                         = "{\"OrderTags\":[\"DEPOSIT\"]}"

  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 50
  team                                      = "G3"
}

module "risk-account-oper-status-update-queue" {
  encrypted                                    = true
  service                                   = "risk"
  bu-name                                   = "user-risk"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "risk-account-oper-status-update-queue"
  suffix                                    = var.suffix
  resource_is_queue                         = true
  enable_dlq                                = true
  env                                       = var.env
  region                                    = var.region
  resource_owner                            = var.resource_owner
  owner                                     = var.owner
  delay_seconds                             = 300
  max_message_size                          = 262144
  visibility_timeout_seconds                = 120
  message_retention_seconds                 = 1209600
  receive_wait_time_seconds                 = 0
  maxReceiveCount                           = 10
  // no alert added on avg number of msg in main queue as this queue will be filled in bulk
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 86400

  team                                          = "G4"
  sns_topic_from_where_message_is_published     = ["account-operational-status-update-topic"]
}

module "rewards-dc-card-switch-notification-queue" {
  encrypted                                    = true
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "rewards-dc-card-switch-notification-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "Cards"
  sns_topic_from_where_message_is_published     = ["dc-switch-notification-topic"]
}

module "nudge-journey-entry-evaluator-queue" {
  encrypted                                    = true
  service                                   = "nudge"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-journey-entry-evaluator-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 30000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 1000

  team = "growth-experience"
}

module "reward-epf-passbook-import-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "reward-epf-passbook-import-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["epf-passbook-import-topic"]
}

module "inhouse-vkyc-agent-and-auditor-response-queue" {
  encrypted                                    = true
  bu-name                                   = "onboarding"
  source                     = "../../modules/sns_sqs/v2"
  queue_name                 = "inhouse-vkyc-agent-and-auditor-response-queue"
  suffix                     = var.suffix
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 120
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 11
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 5
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq         = 86400
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible = 5000
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "Onboarding"
}

module "reward-ca-account-data-sync-event-queue" {
  encrypted                                    = true
  service                                   = "rewards"
  bu-name                                   = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "reward-ca-account-data-sync-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["ca-account-data-sync-topic"]
}

module "comms-netcore-sms-callback-queue" {
  encrypted                                    = true
  service                                       = "comms"
  bu-name                                       = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-netcore-sms-callback-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 10000
  team = "growth-experience"
}

module "epf-passbook-data-flattening-queue" {
  encrypted                                    = true
  service                                   = "insights"
  bu-name                                   = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "epf-passbook-data-flattening-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["epf-passbook-import-topic"]
}

module "pg-razorpay-inbound-event-queue" {
  encrypted                                    = true
  service                                       = "pay"
  bu-name                                       = "pay"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "pg-razorpay-inbound-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds or equal to given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when oldest messages in dlq are older than given threshold. 24 hours
  seconds_for_oldest_messages_dlq               = 86400
  # trigger alert when number of messages not visible is greater than or equal to 5
  avg_number_of_messages_not_visible = 5
  team                                          = "Pay"
}

module "user-access-revoke-cooldown-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "user-access-revoke-cooldown-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 180
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
  enable_cloudwatch_alarm_sqs             = true
  cloudwatch_alarm_priority               = "P0"
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  team                                    = "Onboarding"
}

module "auth-pin-attempts-exceeded-queue" {
  encrypted                                    = true
  service                                   = "user"
  bu-name                                   = "onboarding"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "auth-pin-attempts-exceeded-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 180
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10
  enable_cloudwatch_alarm_sqs             = true
  cloudwatch_alarm_priority               = "P0"
  enable_cloudwatch_alarm_sqs_dlq         = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 1
  team                                    = "Onboarding"
  sns_topic_from_where_message_is_published = ["pin-attempts-exceeded-topic"]
  sns_filter_policy_scope                   = "MessageBody"
  sns_filter_policy                         = "{\"pinType\":[\"PIN_TYPE_UPI_PIN\"],\"partnerBank\":[\"FEDERAL_BANK\"]}"
}

module "screener-sms-parser-data-fetch-queue" {
  encrypted                                    = true
  service                    = "screener"
  bu-name                    = "onboarding"
  source                     = "../../modules/sns_sqs/v2"
  suffix                     = var.suffix
  queue_name                 = "screener-sms-parser-data-fetch-queue"
  resource_is_queue          = true
  enable_dlq                 = true
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  owner                      = var.owner
  delay_seconds              = 0
  max_message_size           = 262144
  visibility_timeout_seconds = 30
  message_retention_seconds  = 1209600
  receive_wait_time_seconds  = 0
  maxReceiveCount            = 10

  enable_cloudwatch_alarm_sqs               = true
  avg_number_of_messages_visible            = 100
  enable_cloudwatch_alarm_sqs_dlq           = true
  seconds_for_oldest_messages_dlq           = 10800
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq = 10
  team                                    = "Onboarding"
}

module "comms-airtel-sms-callback-queue" {
  encrypted                                    = true
  service                                       = "comms"
  bu-name                                       = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-airtel-sms-callback-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 10000
  team = "growth-experience"
}

module "nudge-entry-evaluator-queue" {
  encrypted                                    = true
  service                                       = "nudge"
  bu-name                                       = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "nudge-entry-evaluator-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 30000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 10000
  team = "growth-experience"
}

module "cx-watson-create-ticket-event-queue" {
  encrypted                                    = true
  service                                   = "cx"
  bu-name                                   = "cx"
  source                         = "../../modules/sns_sqs/v2"
  queue_name                     = "cx-watson-create-ticket-event-queue"
  suffix                         = var.suffix
  resource_is_queue              = true
  enable_dlq                     = true
  env                            = var.env
  region                         = var.region
  resource_owner                 = var.resource_owner
  owner                          = var.owner
  delay_seconds                  = 0
  max_message_size               = 262144
  visibility_timeout_seconds     = 120
  message_retention_seconds      = 1209600
  receive_wait_time_seconds      = 0
  maxReceiveCount                = 6
  enable_cloudwatch_alarm_sqs    = true
  team                           = "growth-experience"
  avg_number_of_messages_visible = 100
  cloudwatch_alarm_priority      = "P0"
  sns_topic_from_where_message_is_published = ["cx-ticket-create-event-topic"]
}

module "third-party-fund-transfer-enquiry-event-queue" {
  encrypted                                    = true
  service                                   = "savings"
  bu-name                                   = "central-growth"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "third-party-fund-transfer-enquiry-event-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 30
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 9
  enable_cloudwatch_alarm_sqs             = true
  enable_cloudwatch_alarm_sqs_dlq         = true
  team                                    = "central-growth"
  approximate_number_messages_visible_dlq = 1
}

module "analyser-high-priority-investment-analysis-task-queue" {
  encrypted                                    = true
  service                                       = "analyser"
  bu-name                                       = "demystifi"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "analyser-high-priority-investment-analysis-task-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 1000
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "stocks-catalog-refresh-queue" {
  encrypted                                    = true
  service                                       = "stocks"
  bu-name                                       = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "stocks-catalog-refresh-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 10000
  cloudwatch_alarm_priority                     = "P0"
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than 2 days (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "rewards-actor-nudge-status-update-event-queue" {
  encrypted                                    = true
  service                                       = "rewards"
  bu-name                                       = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "rewards-actor-nudge-status-update-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "growth-experience"
  sns_topic_from_where_message_is_published     = ["nudge-actor-nudge-status-update-event-topic"]
}

module "cc-onboarding-state-update-event-callback-queue" {
  encrypted                                     = true
  source                                        = "../../modules/sns_sqs/v2"
  bu-name                                       = "cards"
  queue_name                                    = "cc-onboarding-state-update-event-callback-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 300
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 30
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "central-growth"
}

module "cx-escalation-creation-queue" {
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "cx-escalation-creation-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  cloudwatch_alarm_priority               = "P1"
}

module "cx-escalation-update-queue" {
  service                                   = "cx"
  bu-name                                   = "cx"
  source                                  = "../../modules/sns_sqs/v2"
  queue_name                              = "cx-escalation-update-queue"
  suffix                                  = var.suffix
  resource_is_queue                       = true
  enable_dlq                              = true
  env                                     = var.env
  region                                  = var.region
  resource_owner                          = var.resource_owner
  owner                                   = var.owner
  delay_seconds                           = 0
  max_message_size                        = 262144
  visibility_timeout_seconds              = 120
  message_retention_seconds               = 1209600
  receive_wait_time_seconds               = 0
  maxReceiveCount                         = 6
  enable_cloudwatch_alarm_sqs_dlq         = true
  approximate_number_messages_visible_dlq = 10
  team                                    = "growth-experience"
  cloudwatch_alarm_priority               = "P1"
}

module "comms-airtel-whatsapp-callback-queue" {
  encrypted                                    = true
  service                                       = "comms"
  bu-name                                       = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "comms-airtel-whatsapp-callback-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 30
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 9
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800

  enable_cloudwatch_alarm_sqs        = true
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible     = 10000
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible = 10000
  team = "growth-experience"
}

module "vendor-reward-fulfillment-event-queue" {
  encrypted                                     = true
  service                                       = "vendornotification"
  bu-name                                       = "growth-infra"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "vendor-reward-fulfillment-event-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 12
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 12
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  # trigger alert when Visible messages in this queue exceeds given threshold
  avg_number_of_messages_visible                = 20
  # trigger alert when NotVisible messages in this queue exceeds given threshold
  avg_number_of_messages_not_visible            = 20
  team                                          = "growth-experience"
  cloudwatch_alarm_priority                     = "P0"
}

module "securities-catalog-addition-queue" {
  service                                       = "securities"
  bu-name                                       = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "securities-catalog-addition-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 10000
  cloudwatch_alarm_priority                     = "P0"
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 1
  # trigger alert when messages in dlq are older than 1 day (86400 seconds)
  seconds_for_oldest_messages_dlq               = 86400
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "securities-historical-price-queue" {
  service                                       = "securities"
  bu-name                                       = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  queue_name                                    = "securities-historical-price-queue"
  suffix                                        = var.suffix
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 11
  # cloudwatch alarms
  enable_cloudwatch_alarm_sqs                   = true
  avg_number_of_messages_visible                = 50000
  avg_number_of_messages_not_visible            = 10000
  cloudwatch_alarm_priority                     = "P0"
  enable_cloudwatch_alarm_sqs_dlq               = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 10
  # trigger alert when messages in dlq are older than 2 day (172800 seconds)
  seconds_for_oldest_messages_dlq               = 172800
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  team                                          = "G3"
}

module "insights-ca-data-new-data-fetch-event-queue" {
  encrypted                                     = true
  service                                       = "insights"
  bu-name                                       = "wealth"
  source                                        = "../../modules/sns_sqs/v2"
  suffix                                        = var.suffix
  queue_name                                    = "insights-ca-data-new-data-fetch-event-queue"
  resource_is_queue                             = true
  enable_dlq                                    = true
  env                                           = var.env
  region                                        = var.region
  resource_owner                                = var.resource_owner
  owner                                         = var.owner
  delay_seconds                                 = 0
  max_message_size                              = 262144
  visibility_timeout_seconds                    = 120
  message_retention_seconds                     = 1209600
  receive_wait_time_seconds                     = 0
  maxReceiveCount                               = 10
  enable_cloudwatch_alarm_sqs_dlq               = true
  enable_cloudwatch_alarm_sqs_oldestmessage_dlq = true
  # trigger alert when visible messages in dlq exceeds given threshold
  approximate_number_messages_visible_dlq       = 50
  # trigger alert when oldest messages in dlq are older than given threshold
  seconds_for_oldest_messages_dlq               = 604800
  team                                          = "G3"
  sns_topic_from_where_message_is_published     = ["ca-account-data-sync-topic"]
}

