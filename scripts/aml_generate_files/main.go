//nolint:funlen
//nolint:dupl
//nolint:staticcheck
package main

import (
	"bytes"
	"context"
	"encoding/csv"
	"flag"
	"fmt"
	"time"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	epifisqs "github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/aml/dao"
	daoImpl "github.com/epifi/gamma/aml/dao/impl"
	amlPb "github.com/epifi/gamma/api/aml"
	"github.com/epifi/gamma/scripts/aml_generate_files/config"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

var (
	filePath = flag.String("filePath", "", "file path of csv in s3")
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	// load config
	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()
	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}

	// get db connection
	epifiDb, err := storagev2.NewCRDBWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}
	sqlDb, err := epifiDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDb.Close() }()

	// var dbConnTeardown func()
	// crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storagev2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	// if err != nil {
	// 	logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
	// 	return err
	// }
	// defer func() {
	// 	dbConnTeardown()
	// }()

	// create sqs client
	awsSession, err := session.NewSession(conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		fmt.Printf("error to initialize AWS session : %v\n", zap.Error(err))
		return
	}
	sqsClient := epifisqs.InitSQSClient(awsSession)
	fileGenerationPublisher, pubErr := epifisqs.NewPublisherWithConfig(conf.FileGenerationPublisher, sqsClient, queue.NewDefaultMessage())
	if pubErr != nil {
		logger.Panic("unable to initialize the tss callback publisher", zap.Error(pubErr))
	}

	ctx := context.Background()
	mapperDao := daoImpl.NewCrdbAmlClientIdToActorIdMappingDao(epifiDb)
	ownershipDBMap := map[common.Ownership]*cfg.DB{
		common.Ownership_EPIFI_TECH: conf.EpifiDb,
	}
	// nolint: dogsled
	epifiDBResourceProvider, _, _, _ := storagev2.NewDBResourceProviderV2(ownershipDBMap, false, nil)
	attemptsDao := daoImpl.NewCrdbScreeningAttemptsDao(epifiDBResourceProvider)
	txnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(epifiDb)

	s3Client := s3.NewClient(awsSession, conf.S3Conf.Bucket)

	var records [][]string
	if filePath != nil && *filePath != "" {
		// Read csv from s3
		csvData, csvErr := readCsvFileFromS3(s3Client, *filePath)
		if csvErr != nil {
			logger.ErrorNoCtx("could not read csv from s3", zap.Error(csvErr))
			return
		}
		// Parsing the records in the csv file
		reader := csv.NewReader(bytes.NewReader(csvData))
		var readErr error
		records, readErr = reader.ReadAll()
		if readErr != nil {
			logger.ErrorNoCtx("Failed to read CSV data", zap.Error(readErr))
			return
		}
		logger.InfoNoCtx("number of records", zap.Int("len", len(records)-1))
		records = records[1:]
	}

	clientRequestId := uuid.New().String()
	txnErr := txnExecutor.RunTxn(ctx, func(ctx context.Context) error {
		var eligibleActors []string
		if filePath != nil && *filePath != "" {
			for _, record := range records {
				eligibleActors = append(eligibleActors, record[0])
			}
		} else {
			actors, attemptErr := getEligibleActors(ctx, mapperDao, attemptsDao)
			if attemptErr != nil {
				return attemptErr
			}
			eligibleActors = append(eligibleActors, actors...)
		}

		if len(eligibleActors) == 0 {
			logger.Info(ctx, "empty eligible actor ids")
			return nil
		}
		logger.Info(ctx, "actor ids for aml file generation", zap.Strings(logger.ACTOR_ID_V2, eligibleActors))
		var mappings []*amlPb.AmlClientIdToActorIdMapping
		// create entries in attempt id client id mapper table for each attempt id
		for _, actor := range eligibleActors {
			mappings = append(mappings, &amlPb.AmlClientIdToActorIdMapping{
				ClientRequestId:      clientRequestId,
				FileGenerationStatus: amlPb.FileGenerationStatus_FILE_GENERATION_STATUS_PENDING,
				ActorId:              actor,
			})
		}
		_, cErr := mapperDao.BatchCreate(ctx, mappings)
		if cErr != nil {
			return cErr
		}
		// check this
		msgId, pErr := fileGenerationPublisher.Publish(ctx, &amlPb.GenerateFilesRequest{
			ClientRequestId: clientRequestId,
			FileType:        amlPb.FileType_FILE_TYPE_FL1_FL43,
		})
		if pErr != nil {
			logger.Error(ctx, "error in publishing aml file generation request", zap.Error(pErr), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
		}
		logger.Info(ctx, "successfully published aml file generation request", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error creating entries in mapper table in txn", zap.Error(txnErr))
		return
	}
}

func getEligibleActors(ctx context.Context, mapperDao dao.AmlClientIdToActorIdMappingDao, attemptsDao dao.ScreeningAttemptsDao) ([]string, error) {
	// get previous attempts for which retry is needed
	retryMappings, mErr := mapperDao.GetByFileGenerationStatus(ctx, amlPb.FileGenerationStatus_FILE_GENERATION_STATUS_RETRY_NEEDED)
	if mErr != nil {
		return nil, errors.Wrap(mErr, "error in fetching retry attempts")
	}

	// get attempts from the previous day
	year, month, day := time.Now().In(datetime.IST).Add(-24 * time.Hour).Date()
	startTime := time.Date(year, month, day, 0, 0, 0, 0, datetime.IST)
	endTime := time.Date(year, month, day, 23, 59, 59, 1e9-1, datetime.IST)
	freshAttempts, sErr := attemptsDao.GetByScreeningTimeRange(ctx, startTime, endTime, common.Owner_OWNER_EPIFI_TECH)
	if sErr != nil {
		return nil, errors.Wrap(sErr, "error in fetching fresh attempts")
	}

	// filter us stocks attempts since aml is not integrated in MF yet and file gen for MF users is done through a different job
	// TODO (sharath): remove this once aml is integrated in wealth onboarding
	freshAttempts = filterUsStocksAttempts(freshAttempts)

	if len(freshAttempts) == 0 {
		logger.Info(ctx, "empty fresh attempts")
	}

	var retryIds []string
	for _, at := range retryMappings {
		retryIds = append(retryIds, at.GetId())
	}
	if len(retryIds) != 0 {
		logger.Info(ctx, "retry needed mapping ids", zap.Strings(logger.ID, retryIds))

		// update retry needed records to retry done since we are retrying them with new client request id
		upErr := mapperDao.BatchUpdate(ctx, retryIds, map[amlPb.AmlClientIdToActorIdMappingFiledMask]interface{}{
			amlPb.AmlClientIdToActorIdMappingFiledMask_AML_CLIENT_ID_TO_ACTOR_ID_MAPPING_FILED_MASK_FILE_GENERATION_STATUS: amlPb.FileGenerationStatus_FILE_GENERATION_STATUS_RETRY_DONE,
		})
		if upErr != nil {
			return nil, errors.Wrap(upErr, "error in updating mapping dao")
		}
	}

	// store actorIds in map to get unique actorIds
	actorIdToBoolMap := make(map[string]bool)
	for _, mapping := range retryMappings {
		actorIdToBoolMap[mapping.GetActorId()] = true
	}
	for _, attempt := range freshAttempts {
		actorIdToBoolMap[attempt.GetActorId()] = true
	}

	var actorIds []string
	for actorId := range actorIdToBoolMap {
		if actorIdToBoolMap[actorId] {
			actorIds = append(actorIds, actorId)
		}
	}
	return actorIds, nil
}

func readCsvFileFromS3(s3Client s3.S3Client, filePath string) ([]byte, error) {
	return s3Client.Read(filePath)
}

func filterUsStocksAttempts(attempts []*amlPb.ScreeningAttempt) []*amlPb.ScreeningAttempt {
	var ret []*amlPb.ScreeningAttempt
	for _, attempt := range attempts {
		if attempt.GetProduct() == amlPb.AmlProduct_AML_PRODUCT_US_STOCKS {
			ret = append(ret, attempt)
		}
	}
	return ret
}
