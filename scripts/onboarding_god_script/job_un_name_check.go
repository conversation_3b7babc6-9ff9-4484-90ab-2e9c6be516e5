//nolint:funlen
package main

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/typesv2/common"
	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	protoPkg "github.com/epifi/be-common/pkg/proto"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/actor/dao"
	daoImpl "github.com/epifi/gamma/aml/dao/impl"
	amlPb "github.com/epifi/gamma/api/aml"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	dao2 "github.com/epifi/gamma/user/dao"
)

type userIdRow struct {
	UserId  string
	ActorId string
}

type UNNameBlacklistCheck struct {
	s3Client                s3.S3Client
	db                      *cfg.DB
	dbConn                  *gorm.DB
	actorDao                *dao.ActorDaoPgdb
	FileGenerationPublisher queue.Publisher
	userDao                 *dao2.UserDaoGormCrdb
	amlScreeningAttemptDao  *daoImpl.CrdbScreeningAttemptsDao
}

func (u *UNNameBlacklistCheck) DoJob(ctx context.Context, req *JobRequest) error {
	var (
		err          error
		clientReqIds []string
		batchSize    = 500
	)
	// Create a slice to hold the unmarshalled data
	var userAndActorIdList []*userIdRow
	// file containing user ids from snowflake, fetched using below query
	// select * from onboarding_details od where TRIM(PARSE_JSON(stage_metadata)['uNNameCheckStatus'],'"') = 'UNNC_NAME_IN_SANCTIONS_LIST'
	// and DATE_TRUNC('DAY',DATE(od.updated_at)) > '2022-05-01' and od.deleted_at is null;
	fileName1 := req.Args1
	userAndActorIdList, err = u.populateActorIds(ctx, fileName1, userAndActorIdList)
	if err != nil {
		logger.ErrorNoCtx("error while fetching user ids", zap.Error(err))
		return err
	}

	// file containing actor ids from kibana logs, fetched manually
	fileName2 := req.Args2
	userAndActorIdList, err = u.populateUserIds(ctx, fileName2, userAndActorIdList)
	if err != nil {
		logger.ErrorNoCtx("error while fetching user ids", zap.Error(err))
		return err
	}

	// skip users for whom we already ran the un name checks
	userAndActorIdList = u.skipDedupeUsers(ctx, userAndActorIdList)
	logger.InfoNoCtx(fmt.Sprintf("users post skipping dedupe %v", len(userAndActorIdList)))

	mapperDao := daoImpl.NewCrdbAmlClientIdToActorIdMappingDao(u.dbConn)
	ownershipDBMap := map[common.Ownership]*cfg.DB{
		common.Ownership_EPIFI_TECH: u.db,
	}
	// nolint: dogsled
	epifiDBResourceProvider, _, _, _ := storagev2.NewDBResourceProviderV2(ownershipDBMap, false, nil)
	attemptsDao := daoImpl.NewCrdbScreeningAttemptsDao(epifiDBResourceProvider)
	txnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(u.dbConn)

	numBatches := len(userAndActorIdList)/batchSize + 1
	for i := 0; i < numBatches; i++ {
		var start, end int
		start = i * batchSize
		if (i+1)*batchSize >= len(userAndActorIdList) {
			end = len(userAndActorIdList)
		} else {
			end = (i + 1) * batchSize
		}

		clientRequestId := uuid.New().String()
		clientReqIds = append(clientReqIds, clientRequestId)
		var mappings []*amlPb.AmlClientIdToActorIdMapping
		var attempts []*amlPb.ScreeningAttempt

		// create entries in attempt id client id mapper table for each attempt id
		for _, userIdAndActorId := range userAndActorIdList[start:end] {
			user, usersErr := u.userDao.GetUser(ctx, userIdAndActorId.UserId, nil)
			if usersErr != nil {
				logger.Error(ctx, "error in getting users details", zap.Error(usersErr))
				continue
			}
			if user.GetProfile().GetKycName() == nil {
				logger.Info(ctx, "invalid user details for aml screening", zap.String(logger.ID, user.GetId()))
				continue
			}

			gender, err := protoPkg.ConvertEnumFromType1ToType2(user.GetProfile().GetKycGender(), commonTypesPb.Gender_value, commonTypesPb.Gender(0))
			if err != nil {
				logger.Error(ctx, "unable to convert gender from typesV2 to common", zap.Error(err))
				continue
			}

			attempts = append(attempts, &amlPb.ScreeningAttempt{
				ActorId:         userIdAndActorId.ActorId,
				ClientRequestId: uuid.New().String(),
				Product:         amlPb.AmlProduct_AML_PRODUCT_SAVINGS_ACCOUNT,
				Vendor:          commonvgpb.Vendor_TSS,
				CustomerDetails: &amlPb.CustomerDetails{
					Name:       user.GetProfile().GetKycName(),
					FatherName: user.GetProfile().GetFatherName(),
					MotherName: user.GetProfile().GetMotherName(),
					Gender:     gender,
					IncomeSlab: getIncomeSlabBySalaryRange(user.GetProfile().GetSalaryRange()),
					PanNumber:  user.GetProfile().GetPAN(),
					// assuming all will be indian since they have reached the UN_NAME_CHECK
					Nationality: commonTypesPb.Nationality_NATIONALITY_INDIAN,
					PhoneNumber: user.GetProfile().GetPhoneNumber(),
					Email:       user.GetProfile().GetEmail(),
					DateOfBirth: user.GetProfile().GetDateOfBirth(),
				},
				Status:                   amlPb.AmlScreeningStatus_AML_SCREENING_STATUS_SUCCESS,
				LastScreeningAttemptedAt: timestamppb.Now(),
				Owner:                    common.Owner_OWNER_EPIFI_TECH,
			})
			mappings = append(mappings, &amlPb.AmlClientIdToActorIdMapping{
				ClientRequestId:      clientRequestId,
				FileGenerationStatus: amlPb.FileGenerationStatus_FILE_GENERATION_STATUS_PENDING,
				ActorId:              userIdAndActorId.ActorId,
			})
		}
		logger.Info(ctx, fmt.Sprintf("length of client id to actor id mappings: %v", len(mappings)))
		txnCtx := epificontext.CloneCtx(ctx)
		txnErr := txnExecutor.RunTxn(txnCtx, func(ctx context.Context) error {
			_, cErr := mapperDao.BatchCreate(ctx, mappings)
			if cErr != nil {
				return errors.Wrap(cErr, "error in creating mappings")
			}
			_, cErr = attemptsDao.BatchCreate(ctx, attempts)
			if cErr != nil {
				return errors.Wrap(cErr, "error in creating screening attempt")
			}
			return nil
		})
		if txnErr != nil {
			logger.Panic("error in txn", zap.Error(txnErr))
		}
		// check this
		msgId, pErr := u.FileGenerationPublisher.Publish(ctx, &amlPb.GenerateFilesRequest{
			ClientRequestId:          clientRequestId,
			FileType:                 amlPb.FileType_FILE_TYPE_FL1_FL43,
			IsFreshScreeningRequired: true,
		})
		if pErr != nil {
			logger.Panic("error in publishing aml file generation request", zap.Error(pErr), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
		}
		logger.Info(ctx, "successfully published aml file generation request", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
		time.Sleep(10 * time.Second)
	}
	logger.Info(ctx, fmt.Sprintf("loggint the all client request ids for file generation %v", clientReqIds))
	return nil
}

func getIncomeSlabBySalaryRange(salaryRange *userPb.SalaryRange) commonTypesPb.IncomeSlab {
	if salaryRange == nil {
		return commonTypesPb.IncomeSlab_INCOME_SLAB_UNSPECIFIED
	}
	switch {
	default:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_UNSPECIFIED
	case salaryRange.MaxValue <= 100000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_BELOW_1_LAC
	case salaryRange.MinValue >= 100000 && salaryRange.MaxValue <= 500000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_1_TO_5_LAC
	case salaryRange.MinValue >= 500000 && salaryRange.MaxValue <= 1000000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_5_TO_10_LAC
	case salaryRange.MinValue >= 1000000 && salaryRange.MaxValue <= 2500000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_10_TO_25_LAC
	case salaryRange.MinValue >= 2500000 && salaryRange.MaxValue <= 10000000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_25_LAC_TO_1_CRORE
	case salaryRange.MinValue >= 10000000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_ABOVE_1_CRORE
	}
}

func (u *UNNameBlacklistCheck) populateActorIds(ctx context.Context, fileName string, userAndActorIdList []*userIdRow) ([]*userIdRow, error) {
	csvData, err := u.s3Client.Read(fileName)
	if err != nil {
		logger.Error(ctx, "error while reading file from S3", zap.Error(err))
		return nil, err
	}
	// Read all records from the CSV file
	reader := csv.NewReader(bytes.NewReader(csvData))
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading CSV:", err)
		return nil, err
	}
	// Skip the header row (column names)
	if len(records) > 0 {
		records = records[1:]
	}
	// Unmarshal the data into the slice of structs
	for ind, record := range records {
		if len(record) == 1 {
			actor, err := u.actorDao.GetByEntityId(ctx, record[0], typesPb.Actor_USER)
			if err != nil {
				logger.ErrorNoCtx(fmt.Sprintf("error in fetching actor by entity id %v", record[0]))
				continue
			}
			userAndActorIdList = append(userAndActorIdList, &userIdRow{
				UserId:  record[0],
				ActorId: actor.GetId(),
			})
		}
		if ind%1000 == 0 {
			time.Sleep(4 * time.Second)
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("logging the count of users %v", len(userAndActorIdList)))
	return userAndActorIdList, nil
}

func (u *UNNameBlacklistCheck) populateUserIds(ctx context.Context, fileName string, userAndActorIdList []*userIdRow) ([]*userIdRow, error) {
	csvData, err := u.s3Client.Read(fileName)
	if err != nil {
		logger.Error(ctx, "error while reading file from S3", zap.Error(err))
		return nil, err
	}
	// Read all records from the CSV file
	reader := csv.NewReader(bytes.NewReader(csvData))
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading CSV:", err)
		return nil, err
	}

	// Unmarshal the data into the slice of structs
	for ind, record := range records {
		if len(record) == 1 {
			actor, err := u.actorDao.GetById(ctx, record[0])
			if err != nil {
				logger.ErrorNoCtx(fmt.Sprintf("error in fetching actor by entity id %v", record[0]))
				continue
			}
			userAndActorIdList = append(userAndActorIdList, &userIdRow{
				UserId:  actor.GetEntityId(),
				ActorId: record[0],
			})
		}
		if ind%1000 == 0 {
			time.Sleep(4 * time.Second)
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("logging the count of users %v", len(userAndActorIdList)))
	return userAndActorIdList, nil
}

func (u *UNNameBlacklistCheck) skipDedupeUsers(ctx context.Context, userAndActorIdList []*userIdRow) []*userIdRow {
	var newUserAndActorIdList []*userIdRow
	var csvData [][]string
	// nolint: ineffassign
	csvData = append(csvData, []string{"ActorId", "UserId"})
	for _, rec := range userAndActorIdList {
		attempts, err := u.amlScreeningAttemptDao.GetByActorId(ctx, rec.ActorId, common.Owner_OWNER_EPIFI_TECH)
		// skip the user for whom entry is created in aml after 1st august
		if err != nil || len(attempts) == 0 || (attempts[0].CreatedAt.AsTime().After(time.Date(2023, 8, 1, 0, 0, 0, 0, datetime.IST))) {
			continue
		}
		newUserAndActorIdList = append(newUserAndActorIdList, rec)
	}
	return newUserAndActorIdList
}
