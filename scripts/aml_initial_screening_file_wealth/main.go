//nolint:funlen
//nolint:dupl
//nolint:staticcheck
package main

import (
	"context"
	"flag"
	"fmt"
	"time"

	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/session"
	epifisqs "github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	protoPkg "github.com/epifi/be-common/pkg/proto"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	daoImpl "github.com/epifi/gamma/aml/dao/impl"
	amlPb "github.com/epifi/gamma/api/aml"
	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/scripts/aml_initial_screening_file_wealth/config"
	wealthDao "github.com/epifi/gamma/wealthonboarding/dao/impl"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	startDate = flag.String("startDate", "", "start date in yyyymmdd format")
	endDate   = flag.String("endDate", "", "end date in yyyymmdd format")
	batchSize = 1000
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	// load config
	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()
	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	// get db connection
	epifiDb, err := storagev2.NewCRDBWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}
	sqlDb, err := epifiDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDb.Close() }()

	// create sqs client
	awsSession, err := session.NewSession(conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		logger.Panic("error to initialize AWS session", zap.Error(err))
	}
	sqsClient := epifisqs.InitSQSClient(awsSession)
	fileGenerationPublisher, pubErr := epifisqs.NewPublisherWithConfig(conf.FileGenerationPublisher, sqsClient, queue.NewDefaultMessage())
	if pubErr != nil {
		logger.Panic("unable to initialize the file generation publisher", zap.Error(pubErr))
	}

	epifiWealthDb, err := storagev2.NewCRDBWithConfig(conf.EpifiWealthDb, false)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}
	sqlWealthDb, err := epifiWealthDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlWealthDb.Close() }()

	ctx := context.Background()
	mapperDao := daoImpl.NewCrdbAmlClientIdToActorIdMappingDao(epifiDb)
	ownershipDBMap := map[commonTypesPb.Ownership]*cfg.DB{
		commonTypesPb.Ownership_EPIFI_TECH: conf.EpifiDb,
	}
	// nolint: dogsled
	epifiDBResourceProvider, _, _, _ := storagev2.NewDBResourceProviderV2(ownershipDBMap, false, nil)
	attemptsDao := daoImpl.NewCrdbScreeningAttemptsDao(epifiDBResourceProvider)
	txnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(epifiDb)
	wealthDao := wealthDao.NewCrdbOnboardingDetailsDao(epifiWealthDb)
	var startTime, endTime time.Time
	if startDate == nil || *startDate == "" || endDate == nil || *endDate == "" {
		year, month, day := time.Now().In(datetime.IST).Add(-7 * 24 * time.Hour).Date()
		startTime = time.Date(year, month, day, 0, 0, 0, 0, datetime.IST)
		year, month, day = time.Now().In(datetime.IST).Add(-24 * time.Hour).Date()
		endTime = time.Date(year, month, day, 23, 59, 59, 1e9-1, datetime.IST)
	} else {
		s, sErr := time.Parse("20060102", *startDate)
		if sErr != nil {
			logger.Panic("failed to parse start time", zap.Error(sErr))
		}
		year, month, day := s.Date()
		startTime = time.Date(year, month, day, 0, 0, 0, 0, datetime.IST)
		e, eErr := time.Parse("20060102", *endDate)
		if eErr != nil {
			logger.Panic("failed to parse end time", zap.Error(eErr))
		}
		year, month, day = e.Date()
		endTime = time.Date(year, month, day, 23, 59, 59, 1e9-1, datetime.IST)
	}
	var odIds []string
	if err = epifiWealthDb.Raw(`SELECT onboarding_details_id FROM onboarding_step_details WHERE step IN ('ONBOARDING_STEP_UPLOAD_DOCKET','ONBOARDING_STEP_DOWNLOAD_KRA_DOC') AND created_at BETWEEN ? AND ?`, startTime, endTime).Find(&odIds).Error; err != nil {
		logger.Panic("failed to run select query", zap.Error(err))
	}

	logger.Info(ctx, fmt.Sprintf("length of odIds: %v", len(odIds)))
	numThreads := len(odIds)/batchSize + 1
	for i := 0; i < numThreads; i++ {
		var start, end int
		start = i * batchSize
		if (i+1)*batchSize >= len(odIds) {
			end = len(odIds)
		} else {
			end = (i + 1) * batchSize
		}
		od, onbErr := wealthDao.GetByIds(ctx, odIds[start:end])
		if onbErr != nil {
			logger.Panic("error in getting onboarding details", zap.Error(onbErr))
		}

		logger.Info(ctx, fmt.Sprintf("length of ods: %v", len(od)))
		clientRequestId := uuid.New().String()
		var mappings []*amlPb.AmlClientIdToActorIdMapping
		var attempts []*amlPb.ScreeningAttempt

		// create entries in attempt id client id mapper table for each attempt id
		for _, onbDetails := range od {
			if onbDetails.GetMetadata().GetPersonalDetails().GetName().GetFirstName() == "" || onbDetails.GetMetadata().GetPersonalDetails().GetNationality() == types.Nationality_NATIONALITY_UNSPECIFIED {
				logger.Info(ctx, "invalid user details for aml screening", zap.String(logger.ID, onbDetails.GetId()))
				continue
			}
			permAddress, corrAddress := getAddress(onbDetails)
			passport, passportExpiry := getPassport(onbDetails)
			license, licenseExpiry := getLicense(onbDetails)
			var voterId string
			if onbDetails.GetMetadata().GetPoaDetails().GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_VOTER_ID {
				voterId = onbDetails.GetMetadata().GetPoaDetails().GetId()
			}
			gender, err := protoPkg.ConvertEnumFromType1ToType2(onbDetails.GetMetadata().GetPersonalDetails().GetGender(), commonTypesPb.Gender_value, commonTypesPb.Gender(0))
			if err != nil {
				logger.Error(ctx, "unable to convert gender from typesV2 to common", zap.Error(err))
				continue
			}
			maritalStatus, err := protoPkg.ConvertEnumFromType1ToType2(onbDetails.GetMetadata().GetPersonalDetails().GetMaritalStatus(), commonTypesPb.MaritalStatus_value, commonTypesPb.MaritalStatus(0))
			if err != nil {
				logger.Error(ctx, "unable to convert marital status from typesV2 to common", zap.Error(err))
				continue
			}
			incomeSlab, err := protoPkg.ConvertEnumFromType1ToType2(onbDetails.GetMetadata().GetPersonalDetails().GetIncomeSlab(), commonTypesPb.IncomeSlab_value, commonTypesPb.IncomeSlab(0))
			if err != nil {
				logger.Error(ctx, "unable to convert income slab from typesV2 to common", zap.Error(err))
				continue
			}
			nationality, err := protoPkg.ConvertEnumFromType1ToType2(onbDetails.GetMetadata().GetPersonalDetails().GetNationality(), commonTypesPb.Nationality_value, commonTypesPb.Nationality(0))
			if err != nil {
				logger.Error(ctx, "unable to convert Nationality from typesV2 to common", zap.Error(err))
				continue
			}
			documentProofType, err := protoPkg.ConvertEnumFromType1ToType2(onbDetails.GetMetadata().GetPoaDetails().GetProofType(), commonTypesPb.DocumentProofType_value, commonTypesPb.DocumentProofType(0))
			if err != nil {
				logger.Error(ctx, "unable to convert documentProofType from typesV2 to common", zap.Error(err))
				continue
			}
			politicallyExposedStatus, err := protoPkg.ConvertEnumFromType1ToType2(onbDetails.GetMetadata().GetPersonalDetails().GetPoliticallyExposedStatus(), commonTypesPb.PoliticallyExposedStatus_value, commonTypesPb.PoliticallyExposedStatus(0))
			if err != nil {
				logger.Error(ctx, "unable to convert PoliticallyExposedStatus from typesV2 to common", zap.Error(err))
				continue
			}

			attempts = append(attempts, &amlPb.ScreeningAttempt{
				ActorId:         onbDetails.GetActorId(),
				ClientRequestId: uuid.New().String(),
				Product:         amlPb.AmlProduct_AML_PRODUCT_MUTUAL_FUNDS,
				Vendor:          commonvgpb.Vendor_TSS,
				CustomerDetails: &amlPb.CustomerDetails{
					Name:                     onbDetails.GetMetadata().GetPersonalDetails().GetName(),
					FatherName:               onbDetails.GetMetadata().GetPersonalDetails().GetFatherName(),
					MotherName:               onbDetails.GetMetadata().GetPersonalDetails().GetMotherName(),
					Gender:                   gender,
					MaritalStatus:            maritalStatus,
					IncomeSlab:               incomeSlab,
					PanNumber:                onbDetails.GetMetadata().GetPanDetails().GetId(),
					Nationality:              nationality,
					PassportNumber:           passport,
					PassportExpiryDate:       passportExpiry,
					DrivingLicenseNumber:     license,
					DrivingLicenseExpiryDate: licenseExpiry,
					VoterId:                  voterId,
					PoaType:                  documentProofType,
					PhoneNumber:              onbDetails.GetMetadata().GetPersonalDetails().GetPhoneNumber(),
					Email:                    onbDetails.GetMetadata().GetPersonalDetails().GetEmail(),
					DateOfBirth:              onbDetails.GetMetadata().GetPersonalDetails().GetDob(),
					PermanentAddress:         permAddress.GetCommonPostalAddress(),
					CorrespondenceAddress:    corrAddress.GetCommonPostalAddress(),
					PoliticallyExposedStatus: politicallyExposedStatus,
				},
				Status:                   amlPb.AmlScreeningStatus_AML_SCREENING_STATUS_SUCCESS,
				LastScreeningAttemptedAt: timestamppb.Now(),
				Owner:                    commonTypesPb.Owner_OWNER_EPIFI_TECH,
			})
			mappings = append(mappings, &amlPb.AmlClientIdToActorIdMapping{
				ClientRequestId:      clientRequestId,
				FileGenerationStatus: amlPb.FileGenerationStatus_FILE_GENERATION_STATUS_PENDING,
				ActorId:              onbDetails.GetActorId(),
			})
		}
		logger.Info(ctx, fmt.Sprintf("length of client id to actor id mappings: %v", len(mappings)))
		txnCtx := epificontext.CloneCtx(ctx)
		txnErr := txnExecutor.RunTxn(txnCtx, func(ctx context.Context) error {
			_, cErr := mapperDao.BatchCreate(ctx, mappings)
			if cErr != nil {
				return errors.Wrap(cErr, "error in creating mappings")
			}
			_, cErr = attemptsDao.BatchCreate(ctx, attempts)
			if cErr != nil {
				return errors.Wrap(cErr, "error in creating screening attempt")
			}
			return nil
		})
		if txnErr != nil {
			logger.Panic("error in txn", zap.Error(txnErr))
		}
		// check this
		msgId, pErr := fileGenerationPublisher.Publish(ctx, &amlPb.GenerateFilesRequest{
			ClientRequestId: clientRequestId,
			FileType:        amlPb.FileType_FILE_TYPE_FL1_FL43,
		})
		if pErr != nil {
			logger.Panic("error in publishing aml file generation request", zap.Error(pErr), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
		}
		logger.Info(ctx, "successfully published aml file generation request", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	}
}

func getAddress(onbDetails *woPb.OnboardingDetails) (*types.PostalAddress, *types.PostalAddress) {
	downloadedPanDetails := onbDetails.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails()
	if downloadedPanDetails != nil {
		return downloadedPanDetails.GetPermAddress(), downloadedPanDetails.GetCorrAddress()
	}
	ckycData := onbDetails.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails()
	if ckycData != nil {
		return ckycData.GetPermanentAddress(), ckycData.GetCurrentAddress()
	}
	digilockerData := onbDetails.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData()
	if digilockerData != nil {
		return digilockerData.GetAddress(), nil
	}
	return nil, nil
}

func getPassport(onbDetails *woPb.OnboardingDetails) (string, *date.Date) {
	poaDetails := onbDetails.GetMetadata().GetPoaDetails()
	if poaDetails.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT {
		if poaDetails.GetExpiry() != nil {
			return poaDetails.GetId(), poaDetails.GetExpiry()
		}
	}
	return "", nil
}

func getLicense(onbDetails *woPb.OnboardingDetails) (string, *date.Date) {
	poaDetails := onbDetails.GetMetadata().GetPoaDetails()
	if poaDetails.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE {
		if poaDetails.GetExpiry() != nil {
			return poaDetails.GetId(), poaDetails.GetExpiry()
		}
	}
	return "", nil
}
