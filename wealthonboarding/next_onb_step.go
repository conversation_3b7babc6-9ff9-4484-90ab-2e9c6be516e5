package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/account/signup"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/onboarding"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/orchestrator_model"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	"github.com/epifi/gamma/wealthonboarding/release"
)

//nolint:funlen
func (s *Service) GetNextOnboardingStep(ctx context.Context, req *woPb.GetNextOnboardingStatusRequest) (*woPb.GetNextOnboardingStatusResponse, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	logger.Info(ctx, fmt.Sprintf("onboarding type in be request: %v", req.GetOnboardingType().String()))
	if req.GetOnboardingType() == woPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED {
		logger.Error(ctx, "Invalid onboarding type", zap.Error(errors.New("Invalid onboarding type")))
		return &woPb.GetNextOnboardingStatusResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	ctx, ctxErr := getCtxWithDevicePlatformVersion(ctx, s.userClient, req.GetActorId())
	if ctxErr != nil {
		logger.Error(ctx, "error getting ctx with device and platform")
		return &woPb.GetNextOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
	}

	nomineeModificationEnabled, updateAppDeeplink, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS).WithActorId(req.GetActorId()))
	if err != nil {
		logger.Error(ctx, "error evaluating wealth onboarding feature for nominee modification enabled", zap.Error(err))
		return &woPb.GetNextOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
	}
	logger.Info(ctx, "feature flag for nominee modification step", zap.Bool("nominee modification enabled", nomineeModificationEnabled))
	if updateAppDeeplink != nil {
		updateAppDeeplink = steps.GetUpdateAppDeeplink(updateAppDeeplink)
		return &woPb.GetNextOnboardingStatusResponse{
			Status:   rpc.StatusOk(),
			NextStep: updateAppDeeplink,
		}, nil
	}
	if !nomineeModificationEnabled {
		return &woPb.GetNextOnboardingStatusResponse{
			Status: rpc.StatusOk(),
			NextStep: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingStatusScreenOptions{
					WealthOnboardingStatusScreenOptions: &deeplinkPb.WealthOnboardingStatusScreenOptions{
						OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_WAIT,
						Title:            "Under Maintenance",
						Description:      "We're doing some behind-the-scenes upgrades with our partners. Will be back by tomorrow! Sorry for the inconvenience.",
						Cta: &deeplinkPb.Cta{
							Text: "Ok",
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_HOME,
							},
						},
					},
				},
			},
		}, nil
	}
	od, dLink, err := s.orchestrateWithFallbackDeeplink(ctx, &orchestrator_model.OrchestrateRequest{
		ActorId:    req.GetActorId(),
		OnbType:    woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		EntryPoint: req.GetEntryPoint(),
	})
	if err != nil {
		logger.Error(ctx, "failed to orchestrate onboarding-wealth for actor", zap.Error(err))
		return &woPb.GetNextOnboardingStatusResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED && req.GetOnboardingType() == woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT {
		var (
			invDeeplink *deeplinkPb.Deeplink
			ioErr       error
		)
		od, invDeeplink, ioErr = s.orchestrateWithFallbackDeeplink(ctx, &orchestrator_model.OrchestrateRequest{
			ActorId:    req.GetActorId(),
			OnbType:    woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
			EntryPoint: req.GetEntryPoint(),
		})
		if ioErr != nil {
			logger.Error(ctx, "failed to orchestrate onboarding-investment for actor", zap.Error(ioErr))
			return &woPb.GetNextOnboardingStatusResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}

		// if both wealth and pre-investment onboarding have deeplinks, wealth onboarding deeplink gets prioritized
		if dLink == nil {
			dLink = invDeeplink
		}
	}

	wealthFlow := req.GetWealthFlow()
	if wealthFlow == woPb.WealthFlow_WEALTH_FLOW_UNSPECIFIED {
		logger.Info(ctx, "wealth flow is unspecified, defaulting to investment flow")
		wealthFlow = woPb.WealthFlow_WEALTH_FLOW_INVESTMENT
	}
	uErr := s.updateWealthFlow(ctx, od, wealthFlow)
	if uErr != nil {
		logger.Error(ctx, "error while updating wealth flow", zap.Error(uErr))
		return &woPb.GetNextOnboardingStatusResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	// calling repair data to fix investment required data
	s.repairDataWithCondition(ctx, od)

	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED && dLink == nil {
		dLink, err = getDeeplinkForOnboardingCompleted(ctx, helper.GetFeWealthFlow(wealthFlow), od)
		if err != nil {
			logger.Error(ctx, "error getting deeplink for onboarding completed", zap.Error(err))
			return &woPb.GetNextOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
		}
		dLink, err = s.getPanDobScreen(ctx, req.GetActorId(), dLink)
		if err != nil {
			logger.Error(ctx, "error getting deeplink for onboarding completed", zap.Error(err))
			return &woPb.GetNextOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
		}
	} else {
		deeplinkEnrichmentErr := s.updateNextStepDeeplink(ctx, dLink, helper.GetFeWealthFlow(wealthFlow), req.GetActorId())
		if deeplinkEnrichmentErr != nil {
			logger.Error(ctx, "error enriching next step deeplink", zap.Error(deeplinkEnrichmentErr))
			return &woPb.GetNextOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	logger.Info(ctx, "deeplink in response", zap.String(logger.SCREEN, dLink.GetScreen().String()))
	return &woPb.GetNextOnboardingStatusResponse{
		Status:           rpc.StatusOk(),
		NextStep:         dLink,
		OnboardingStatus: od.GetStatus(),
	}, nil
}

// If there's no deeplink sent by orchestrator, it sends a generic deeplink based on the onboarding status (or no deeplink if onboarding has been completed)
// Special cases where we still don't send deeplink even if orchestrator doesn't send one:
// - User should not be blocked while we are waiting on KRA to provide the user's KYC docket (because KYC status is sufficient to allow user to proceed)
// - UNLESS their PoA is Aadhaar and KYC record was created after 1 Nov, user should not be blocked while we are waiting on KRA to approve the user's new / modified KYC docket
//   - Ideally for ALL cases where a KYC docket is sent to KRA, we should keep checking the latest KYC status of these users and fail their onboarding if KYC doesn't succeed
//
// Note: even in special cases, if orchestrator sends a deeplink, that is still shown to user
func (s *Service) orchestrateWithFallbackDeeplink(ctx context.Context, req *orchestrator_model.OrchestrateRequest) (*woPb.OnboardingDetails, *deeplinkPb.Deeplink, error) {
	od, dLink, orcErr := s.orchestratorService.Orchestrate(ctx, req)
	if orcErr != nil {
		logger.Error(ctx, "failed to orchestrate onboarding for actor", zap.Error(orcErr))
		return nil, nil, errors.Wrap(orcErr, fmt.Sprintf("failed to orchestrate for %v", req.OnbType))
	}
	if dLink != nil {
		return od, dLink, nil
	}
	switch od.GetCurrentStep() {
	default:
		return od, getDefaultDeeplinkBasedOnOnboardingStatus(ctx, od.GetStatus(), od.GetCurrentStep()), nil
	case woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC:
		return od, nil, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET:
		isValidationRequired, validationErr := helper.IsAddressProofValidationRequired(ctx, od)
		if validationErr != nil {
			logger.Error(ctx, "error checking if address proof validation required", zap.Error(validationErr))
			return nil, nil, errors.Wrap(validationErr, "error getting default deeplink for upload docket step")
		}
		if isValidationRequired {
			return od, getDefaultDeeplinkBasedOnOnboardingStatus(ctx, od.GetStatus(), od.GetCurrentStep()), nil
		} else {
			return od, nil, nil
		}
	}
}

func getDefaultDeeplinkBasedOnOnboardingStatus(ctx context.Context, onbStatus woPb.OnboardingStatus, onbStep woPb.OnboardingStep) *deeplinkPb.Deeplink {
	if onbStatus == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		logger.Info(ctx, "onboarding complete")
		return nil
	}
	switch onbStatus {
	default:
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingStatusScreenOptions{
				WealthOnboardingStatusScreenOptions: &deeplinkPb.WealthOnboardingStatusScreenOptions{
					OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_CONTACT_CUSTOMER_SUPPORT,
					Message:          "We are facing some issue with your data verification. Please contact customer support.",
				},
			},
		}
	case woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS:
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
				WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
					OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_RETRY,
				},
			},
		}
	case woPb.OnboardingStatus_ONBOARDING_STATUS_FAILED:
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
				WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
					OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FAILED,
				},
			},
		}
	case woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED:
		// handling manual intervention based on error type
		title, description := getErrorTileAndDesc(onbStep)
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
				WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
					Title:            title,
					Description:      description,
					OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_CONTACT_CUSTOMER_SUPPORT,
				},
			},
		}
	case woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE:
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingStatusScreenOptions{
				WealthOnboardingStatusScreenOptions: &deeplinkPb.WealthOnboardingStatusScreenOptions{
					OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FUTURE_SCOPE,
					Message:          "We will notify you.",
				},
			},
		}
	}
}

func getErrorTileAndDesc(step woPb.OnboardingStep) (string, string) {
	switch step {
	case woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION:
		return "Uh oh! Your PAN seems \nto be invalid", "No worries! Our Fi care specialists can help you sort this out"
	case woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS:
		return "Uh-oh! You seem to have run out of attempts for video submission", "Unable to verify your video"
	default:
		return "Uh oh, some error occurred while verifying your data", "Your data could not be verified"
	}
}

// updateWealthFlow updates wealth flow in db
func (s *Service) updateWealthFlow(ctx context.Context, od *woPb.OnboardingDetails, flow woPb.WealthFlow) error {
	od.CurrentWealthFlow = flow
	err := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_WEALTH_FLOW})
	if err != nil {
		return errors.Wrap(err, "failed to update current wealth flow in DB")
	}
	return nil
}

// getPanDobScreen checks if pan dob details exists and returns PAN DOB screen if details not present, else returns the existing deeplink
func (s *Service) getPanDobScreen(ctx context.Context, actorId string, dl *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	userRes, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		if !userRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in getting user", zap.Error(err))
		}
		return nil, rpcErr
	}

	if onboarding.DobPanExists(userRes.GetUser().GetProfile().GetPAN(), userRes.GetUser().GetProfile().GetDateOfBirth()) {
		return dl, nil
	}

	dlBytes, err := json.Marshal(dl)
	if err != nil {
		logger.Error(ctx, "error in marshaling deeplink", zap.Error(err))
		return nil, err
	}
	return getPanDobDeeplink(dlBytes), nil
}

func getPanDobDeeplink(sdkDlBytes []byte) *deeplinkPb.Deeplink {
	consents := []*deeplinkPb.Consent{
		{
			Text: onboardingPkg.FiWealthTncText,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiWealthTncText,
				},
			},
			Consent: deeplinkPb.ConsentTypeUrl_FI_WEALTH_TNC.String(),
			ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.KnowMoreTitle,
					Description: onboardingPkg.KnowMoreDescription,
					TitleV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.KnowMoreTitle,
						},
					},
					DescriptionV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.KnowMoreDescription,
						},
					},
				},
				{
					Title:       onboardingPkg.CKYCFailureTitle,
					Description: onboardingPkg.CKYCFailureDescription,
					TitleV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.CKYCFailureTitle,
						},
					},
					DescriptionV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.CKYCFailureTitle,
						},
					},
				},
			},
		},
		{
			Text: onboardingPkg.FiPrivacyPolicyText,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiPrivacyPolicyText,
				},
			},
			Consent: deeplinkPb.ConsentTypeUrl_FI_PRIVACY_POLICY.String(),
			ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.EligibilityChecksTitle,
					Description: onboardingPkg.EligibilityChecksDescription,
				},
			},
		},
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_REGISTER_CKYC,
		ScreenOptions: &deeplinkPb.Deeplink_RegisterCkycScreenOptions{
			RegisterCkycScreenOptions: &deeplinkPb.RegisterCKYCScreenOptions{
				Title:      onboardingPkg.PanDobTitleForCa,
				Subtitle:   onboardingPkg.PanDobSubtitleForCa,
				IsEditable: commontypes.BooleanEnum_TRUE,
				EntryPoint: signup.EntryPoint_ENTRY_POINT_CONNECTED_ACCOUNTS.String(),
				Consents:   consents,
				BackAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HOME,
				},
				BackActionV2: &deeplinkPb.BackAction{
					ShowButton: commontypes.BooleanEnum_TRUE,
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HOME,
					},
				},
				Blob: sdkDlBytes,
			},
		},
	}
}
