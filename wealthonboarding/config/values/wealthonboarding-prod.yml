Application:
  Environment: "prod"
  Name: "wealthonboarding"

Server:
  Ports:
    GrpcPort: 8089
    GrpcSecurePort: 9507
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "wealthonboarding"
  StatementTimeout: 10s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

VendorRequestProducer:
  StreamName: "prod-wealthonboarding-vendor-request-publish-stream"

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true
  VendorRequestStore: "kinesis"   # options are "kinesis" "crdb" "crdb_and_kinesis"
  EnableBankAccountValidation: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    OfficerSignature: "prod/connectedaccount/docket-verification-officer-signature"

RefreshFaceMatchStatusSqsPublisher:
  QueueName: "prod-wo-refresh-fm-status-delay-queue"

RefreshFaceMatchStatusSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-wo-refresh-fm-status-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

FetchFMStatusInitialDelayDuration: 60s

UpdateTxnStateSqsPublisher:
  QueueName: "prod-wo-e-sign-txn-state-delay-queue"

UpdateTxnStateSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-wo-e-sign-txn-state-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

UpdateTxnStateDelayDuration: 5s

InitWealthOnboardingSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 3
  QueueName: "prod-wo-onboarding-stage-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "connectedaccount"

MinNameMatchScore: 1

CvlKra:
  UploadPath: "/test"
  PdfExpiryTime: 86400
  VerificationTimeInHours: 48

S3Conf:
  Bucket: "epifi-prod-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"

VendorRequestS3Conf:
  Bucket: "epifi-prod-wealthonboarding-vendor-request"

Digio:
  BaseSignPath: "https://ext.digio.in/#/gateway/login"

LivenessConfig:
  OTPThreshold: 50
  LivenessThreshold: 50
  LivenessStalenessDuration: 72h

KraDocketSignConfig:
  OfficerName: "Rohan Cutinha"
  EmployeeCode: "EP158"
  OfficerDesignation: "MANAGER"
  IntermediaryCode: "Epifi Wealth Private Limited"
  CallBackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

EnableNameMatch: true

OCRThresholdScoreConfig:
    DOCUMENT_PROOF_TYPE_PAN: 40.0
    DOCUMENT_PROOF_TYPE_DRIVING_LICENSE: 100.0
    DOCUMENT_PROOF_TYPE_PASSPORT: 100.0
    DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR: 100.0
    DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION: 100.0

DaysToExpiry: 180

WealthOnboardingStepsRetrySqsCustomDelayPublisher:
  DestQueueName: "prod-wo-steps-retry-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

WealthOnboardingStepsRetryDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-wo-steps-retry-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

StepsRetryStrategy:
  "ONBOARDING_STEP_DOWNLOAD_KRA_DOC":
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET":
        RegularInterval:
          Interval: 360
          MaxAttempts: 28
          TimeUnit: "Minute"
  "ONBOARDING_STEP_UPLOAD_DOCKET":
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_UNSPECIFIED":
        RegularInterval:
          Interval: 360
          MaxAttempts: 28
          TimeUnit: "Minute"
    "STEP_STATUS_TRANSIENT_FAILURE":
      "STEP_SUB_STATUS_UNSPECIFIED":
        RegularInterval:
          Interval: 360
          MaxAttempts: 28
          TimeUnit: "Minute"
  "ALLSTEPS":
    "STEP_STATUS_TRANSIENT_FAILURE":
      "ALLSUBSTATUS":
        RegularInterval:
          Interval: 1
          MaxAttempts: 24
          TimeUnit: "Hour"
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET":
        RegularInterval:
          Interval: 360
          MaxAttempts: 28
          TimeUnit: "Minute"
      "STEP_SUB_STATUS_VENDOR_DOWNTIME":
        RegularInterval:
          Interval: 3
          MaxAttempts: 12
          TimeUnit: "Hour"


FeatureControlledReleaseConfig:
  WEALTH_ONBOARDING_FEATURE_TNC_V2:
    AppVersionConstraintConfig:
      MinAndroidVersion: 321
      MinIOSVersion: 461
  WEALTH_ONBOARDING_FEATURE_PAN_DOB_SUBMIT_SCREEN:
    AppVersionConstraintConfig:
      MinAndroidVersion: 317
      MinIOSVersion: 452
  WEALTH_ONBOARDING_FEATURE_NSDL_PAN_INQUIRY_WITH_NAME:
    AppVersionConstraintConfig:
      MinAndroidVersion: 334
      MinIOSVersion: 487
  WEALTH_ONBOARDING_FEATURE_RISK_PROFILING:
    AppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
  WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 367
      MinIOSVersion: 518
  WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 452
      MinIOSVersion: 615
    AppPlatformConstraintConfig:
      Android: true
      Ios: true
  WEALTH_ONBOARDING_FEATURE_MITC_CONTENT:
    AppVersionConstraintConfig:
      MinAndroidVersion: 10000
      MinIOSVersion: 10000

NotifyMeFix:
  MaxAndroidVersion: 9999
  MaxIOSVersion: 206

DigilockerConfig:
  ClientId: "EBB0DE86"
  LoginUrl: "https://api.digitallocker.gov.in/public/oauth2/1/authorize?response_type=code&client_id=%v&redirect_uri=%v&state=myData"
  CallbackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

KraDowntime:
  DowntimeFrom: "2022-03-12 15:00:00.000"
  DowntimeTo: "2022-03-12 18:00:00.000"

LambdaFunctions:
  ImageConverterName: "ImageConverterFunction"

MaxPanUploadAttempts: 5

Tracing:
  Enable: true

WealthAuthFactorUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-wealth-onb-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

AuthFactorUpdateWealthPublisher:
  TopicName: "prod-auth-factor-update-wealth-topic"

UserCommsPublisher:
  QueueName: "prod-wonb-user-comms-delay-queue"

UserCommsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-wonb-user-comms-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

InhouseNameMatchThreshold: 0.8

AdvisoryAgreement:
  UnsignedDocS3Path: "common/legal/Epifi_Wealth_Private_Limited_Investment_Advisory_Services_Agreement.pdf"
  AdvisoryAgreementUpdateAt: "2025-07-25T11:00:00+05:30"

Manch:
  DocketTemplateKey: "TEMPL00998"
  AdvisoryAgreementTemplateKey: "TEMPL01467"

InvestmentsFaqCategoryId: "82000473194"
MaxPanDobSubmitAttempts: 3
DisableCKYC: true
