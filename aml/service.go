package aml

import (
	"context"
	"fmt"
	"regexp"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/pkg/owner"

	"github.com/epifi/gamma/aml/config"
	"github.com/epifi/gamma/aml/dao"
	"github.com/epifi/gamma/aml/wire/types"
	amlPb "github.com/epifi/gamma/api/aml"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	vmPb "github.com/epifi/gamma/api/vendormapping"
)

const (
	VendorRequestIdLen   = 10
	MaxAddressLineLength = 500
	MaxCityLength        = 100
	MaxStateLength       = 100
	MaxPinCodeLength     = 25
	MaxNameLength        = 3000
	MaxPassportLength    = 200
)

type Service struct {
	amlPb.UnimplementedAmlServer
	screeningAttemptDao         dao.ScreeningAttemptsDao
	amlCaseDetailsDao           dao.AmlCaseDetailsDao
	amlVgClient                 amlVgPb.AmlClient
	vmClient                    vmPb.VendorMappingServiceClient
	conf                        *config.Config
	caseDecisionPublisher       types.CaseDecisionPublisher
	clientIdToActorIdMappingDao dao.AmlClientIdToActorIdMappingDao
	txnExecutorProvider         *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]
}

func NewService(
	screeningAttemptDao dao.ScreeningAttemptsDao,
	amlCaseDetailsDao dao.AmlCaseDetailsDao,
	amlVgClient amlVgPb.AmlClient,
	vmClient vmPb.VendorMappingServiceClient,
	conf *config.Config,
	caseDecisionPublisher types.CaseDecisionPublisher,
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	clientIdToActorIdMappingDao dao.AmlClientIdToActorIdMappingDao,
) *Service {
	return &Service{
		screeningAttemptDao:         screeningAttemptDao,
		amlCaseDetailsDao:           amlCaseDetailsDao,
		amlVgClient:                 amlVgClient,
		vmClient:                    vmClient,
		conf:                        conf,
		caseDecisionPublisher:       caseDecisionPublisher,
		txnExecutorProvider:         txnExecutorProvider,
		clientIdToActorIdMappingDao: clientIdToActorIdMappingDao,
	}
}

// nolint:funlen
func (s *Service) ScreenCustomer(ctx context.Context, request *amlPb.ScreenCustomerRequest) (*amlPb.ScreenCustomerResponse, error) {
	if pErr := prepareCustomerDetails(request.GetCustomerDetails()); pErr != nil {
		logger.Error(ctx, "error in customer details", zap.Error(pErr), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	// Get RecordIdentifier from vendormapping service
	// think if we can use this ID for old actors when migrating
	// what's the diff b/w request id,source system customer code and unique identifier
	vmRes, vmErr := s.vmClient.GetBEMappingById(ctx, &vmPb.GetBEMappingByIdRequest{Id: request.GetActorId()})
	if vmErr != nil {
		logger.Error(ctx, "error in getting record identifier", zap.Error(vmErr), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}

	vgCusDetails, convErr := s.convertToVgCustomerDetails(ctx, request.GetCustomerDetails())
	if convErr != nil {
		logger.Error(ctx, "error in converting customer details", zap.Error(convErr), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}
	vgRes, vgErr := s.amlVgClient.ScreenCustomer(ctx, &amlVgPb.ScreenCustomerRequest{
		Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_TSS},
		RecordIdentifier: vmRes.GetTssId(),
		VendorRequestId:  idgen.RandSeqDigitsWithoutLeadingZeroes(VendorRequestIdLen),
		Product:          request.GetAmlProduct(),
		CustomerDetails:  vgCusDetails,
		Owner:            request.GetOwner(),
	})
	if rpcErr := epifigrpc.IsRPCErrorWithDowntime(vgRes, vgErr); rpcErr != nil {
		logger.Error(ctx, "error in vg API", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		if errors.Is(rpcErr, epifierrors.ErrDowntimeExpected) {
			return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusUnavailable()}, nil
		}
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if vgRes.GetRejectionCode() != amlPb.RejectionCode_REJECTION_CODE_UNSPECIFIED {
		// do not return here since this is also a valid screening attempt
		logger.Error(ctx, fmt.Sprintf("rejection code from vendor: %v", vgRes.GetRejectionCode().String()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
	}

	ownership, err := owner.GetOwnershipFromOwner(request.GetOwner())
	if err != nil {
		logger.Error(ctx, "error in getting ownership from owner", zap.Error(err), zap.String(logger.OWNERSHIP, request.GetOwner().String()))
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}
	txnExecutor, txnExecErr := s.txnExecutorProvider.GetResourceForOwnership(ownership)
	if txnExecErr != nil {
		logger.Error(ctx, "error getting txn executor for owner", zap.Error(err), zap.String(logger.OWNERSHIP, ownership.String()))
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if txnErr := txnExecutor.RunTxn(ctx, func(ctx context.Context) error {
		// store screening attempt details
		screeningAttemptId, sErr := s.storeScreeningAttemptDetails(ctx, request, vgRes)
		if sErr != nil {
			return errors.Wrap(sErr, "error in storing attempt details")
		}

		// store match details (if match is found)
		if vgRes.GetMatchStatus() == amlVgPb.MatchStatus_MATCH_STATUS_MATCHED {
			if mErr := s.storeCaseDetails(ctx, request, vgRes, screeningAttemptId); mErr != nil {
				return errors.Wrap(mErr, "error in storing case details")
			}
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in running txn", zap.Error(txnErr), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return &amlPb.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &amlPb.ScreenCustomerResponse{
		Status:    getStatusFromRejectionCode(vgRes.GetRejectionCode()),
		Match:     convertToBEResult(vgRes.GetMatchStatus()),
		MatchData: convertToBeBEMatchData(vgRes.GetMatchDetails()),
	}, nil
}

// nolint:funlen
func prepareCustomerDetails(details *amlPb.CustomerDetails) error {
	// remove special characters from names
	if details.GetName() != nil {
		details.GetName().FirstName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetName().GetFirstName(), "")
		details.GetName().MiddleName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetName().GetMiddleName(), "")
		details.GetName().LastName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetName().GetLastName(), "")
	}
	if details.GetFatherName() != nil {
		details.GetFatherName().FirstName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetFatherName().GetFirstName(), "")
		details.GetFatherName().MiddleName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetFatherName().GetMiddleName(), "")
		details.GetFatherName().LastName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetFatherName().GetLastName(), "")
	}
	if details.GetMotherName() != nil {
		details.GetMotherName().FirstName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetMotherName().GetFirstName(), "")
		details.GetMotherName().MiddleName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetMotherName().GetMiddleName(), "")
		details.GetMotherName().LastName = regexp.MustCompile(`[^a-zA-Z ]+`).ReplaceAllString(details.GetMotherName().GetLastName(), "")
	}
	if details.GetName().GetFirstName() == "" {
		return errors.New("first name empty")
	}
	if len(details.GetPermanentAddress().GetAddressLines()) > 0 {
		if len(details.GetPermanentAddress().GetAddressLines()[0]) > MaxAddressLineLength {
			details.GetPermanentAddress().AddressLines[0] = details.GetPermanentAddress().GetAddressLines()[0][:MaxAddressLineLength]
		}
	}
	if len(details.GetPermanentAddress().GetAddressLines()) > 1 {
		if len(details.GetPermanentAddress().GetAddressLines()[1]) > MaxAddressLineLength {
			details.GetPermanentAddress().AddressLines[1] = details.GetPermanentAddress().GetAddressLines()[1][:MaxAddressLineLength]
		}
	}
	if len(details.GetPermanentAddress().GetAddressLines()) > 2 {
		if len(details.GetPermanentAddress().GetAddressLines()[2]) > MaxAddressLineLength {
			details.GetPermanentAddress().AddressLines[2] = details.GetPermanentAddress().GetAddressLines()[2][:MaxAddressLineLength]
		}
	}
	if len(details.GetCorrespondenceAddress().GetAddressLines()) > 0 {
		if len(details.GetCorrespondenceAddress().GetAddressLines()[0]) > MaxAddressLineLength {
			details.GetCorrespondenceAddress().AddressLines[0] = details.GetCorrespondenceAddress().GetAddressLines()[0][:MaxAddressLineLength]
		}
	}
	if len(details.GetCorrespondenceAddress().GetAddressLines()) > 1 {
		if len(details.GetCorrespondenceAddress().GetAddressLines()[1]) > MaxAddressLineLength {
			details.GetCorrespondenceAddress().AddressLines[1] = details.GetCorrespondenceAddress().GetAddressLines()[1][:MaxAddressLineLength]
		}
	}
	if len(details.GetCorrespondenceAddress().GetAddressLines()) > 2 {
		if len(details.GetCorrespondenceAddress().GetAddressLines()[2]) > MaxAddressLineLength {
			details.GetCorrespondenceAddress().AddressLines[2] = details.GetCorrespondenceAddress().GetAddressLines()[2][:MaxAddressLineLength]
		}
	}
	if len(details.GetPermanentAddress().GetLocality()) > MaxCityLength {
		details.GetPermanentAddress().Locality = details.GetPermanentAddress().GetLocality()[:MaxCityLength]
	}
	if len(details.GetCorrespondenceAddress().GetLocality()) > MaxCityLength {
		details.GetCorrespondenceAddress().Locality = details.GetCorrespondenceAddress().GetLocality()[:MaxCityLength]
	}
	if len(details.GetPermanentAddress().GetAdministrativeArea()) > MaxStateLength {
		details.GetPermanentAddress().AdministrativeArea = details.GetPermanentAddress().GetAdministrativeArea()[:MaxStateLength]
	}
	if len(details.GetCorrespondenceAddress().GetAdministrativeArea()) > MaxStateLength {
		details.GetCorrespondenceAddress().AdministrativeArea = details.GetCorrespondenceAddress().GetAdministrativeArea()[:MaxStateLength]
	}
	if len(details.GetPermanentAddress().GetPostalCode()) > MaxPinCodeLength {
		details.GetPermanentAddress().PostalCode = details.GetPermanentAddress().GetPostalCode()[:MaxPinCodeLength]
	}
	if len(details.GetCorrespondenceAddress().GetPostalCode()) > MaxPinCodeLength {
		details.GetCorrespondenceAddress().PostalCode = details.GetCorrespondenceAddress().GetPostalCode()[:MaxPinCodeLength]
	}
	if len(details.GetName().GetFirstName()) > MaxNameLength {
		details.GetName().FirstName = details.GetName().GetFirstName()[:MaxNameLength]
	}
	if len(details.GetName().GetMiddleName()) > MaxNameLength {
		details.GetName().MiddleName = details.GetName().GetMiddleName()[:MaxNameLength]
	}
	if len(details.GetName().GetLastName()) > MaxNameLength {
		details.GetName().LastName = details.GetName().GetLastName()[:MaxNameLength]
	}
	if len(details.GetPassportNumber()) > MaxPassportLength {
		details.PassportNumber = details.GetPassportNumber()[:MaxPassportLength]
	}
	return nil
}

func getStatusFromRejectionCode(rejectionCode amlPb.RejectionCode) *rpcPb.Status {
	switch rejectionCode {
	case amlPb.RejectionCode_REJECTION_CODE_INVALID_PAN_FORMAT:
		return &rpcPb.Status{
			Code:         uint32(amlPb.ScreenCustomerResponse_INVALID_PAN_FORMAT),
			ShortMessage: amlPb.ScreenCustomerResponse_INVALID_PAN_FORMAT.String(),
		}
	case amlPb.RejectionCode_REJECTION_CODE_PASSPORT_LENGTH_EXCEEDED:
		return &rpcPb.Status{
			Code:         uint32(amlPb.ScreenCustomerResponse_PASSPORT_LENGTH_EXCEEDED),
			ShortMessage: amlPb.ScreenCustomerResponse_PASSPORT_LENGTH_EXCEEDED.String(),
		}
	case amlPb.RejectionCode_REJECTION_CODE_UNSPECIFIED:
		return rpcPb.StatusOk()
	default:
		return rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled rejection code: %v", rejectionCode.String()))
	}
}

func (s *Service) GetScreeningStatus(ctx context.Context, request *amlPb.GetScreeningStatusRequest) (*amlPb.GetScreeningStatusResponse, error) {
	attempt, aErr := s.screeningAttemptDao.GetByClientId(ctx, request.GetClientRequestId(), request.GetOwner())
	if aErr != nil {
		if errors.Is(aErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "screening attempt record not found", zap.String(logger.CLIENT_REQUEST_ID, request.GetClientRequestId()))
			return &amlPb.GetScreeningStatusResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error in getting screening attempt details", zap.Error(aErr))
		return &amlPb.GetScreeningStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	var (
		reviewStatus amlPb.ReviewStatus
		matchData    []*amlPb.MatchData
	)
	if attempt.GetResult() == amlPb.AmlMatch_AML_MATCH_FOUND {
		caseDetails, mErr := s.amlCaseDetailsDao.GetByScreeningAttemptId(ctx, attempt.GetId(), request.GetOwner())
		if mErr != nil {
			if errors.Is(mErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "case details record not found for given attempt id", zap.String(logger.ATTEMPT_ID, attempt.GetId()), zap.String(logger.ACTOR_ID_V2, attempt.GetActorId()))
				return &amlPb.GetScreeningStatusResponse{Status: rpcPb.StatusRecordNotFound()}, nil
			}
			logger.Error(ctx, "error in getting case details", zap.Error(mErr), zap.String(logger.ACTOR_ID_V2, attempt.GetActorId()))
			return &amlPb.GetScreeningStatusResponse{Status: rpcPb.StatusInternal()}, nil
		}
		if len(caseDetails) == 0 {
			logger.Error(ctx, "case details is empty", zap.String(logger.ACTOR_ID_V2, attempt.GetActorId()))
			return &amlPb.GetScreeningStatusResponse{Status: rpcPb.StatusInternal()}, nil
		}
		reviewStatus = caseDetails[0].GetReviewStatus()
		// currently only a single case can be created for a screening attempt, refactor this code when more than one case is supported
		matchData = convertToBeBEMatchData(caseDetails[0].GetMatchDetailsCollection().GetMatchDetails())
	}
	return &amlPb.GetScreeningStatusResponse{
		Status:          rpcPb.StatusOk(), // TODO(sharath) send appropriate status based on rejection code
		ScreeningStatus: attempt.GetStatus(),
		MatchData:       matchData,
		Match:           attempt.GetResult(),
		ReviewStatus:    reviewStatus,
	}, nil
}
